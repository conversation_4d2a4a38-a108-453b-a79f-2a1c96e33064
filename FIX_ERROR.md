# 🚨 حل خطأ Internal Server Error

## المشكلة:
```
Internal Server Error
The server encountered an internal error or misconfiguration and was unable to complete your request.
```

## ✅ الحلول السريعة:

### الحل الأول: استخدم التثبيت المبسط
```
http://localhost/simple_install.php
```

### الحل الثاني: فحص النظام أولاً
```
http://localhost/check_system.php
```

### الحل الثالث: تشخيص المشكلة

#### 1. تحقق من أخطاء PHP:
أضف هذا الكود في بداية أي ملف PHP:
```php
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
```

#### 2. تحقق من ملف error_log:
```bash
# في XAMPP
C:\xampp\apache\logs\error.log

# في WAMP
C:\wamp64\logs\apache_error.log

# في Linux
/var/log/apache2/error.log
```

#### 3. تحقق من إصدار PHP:
```bash
php --version
# يجب أن يكون 7.4 أو أحدث
```

## 🔧 خطوات الإصلاح:

### الخطوة 1: تشغيل الخادم
```bash
# Windows
start_server.bat

# Linux/Mac
./start_server.sh

# أو مباشرة
php -S localhost:8000
```

### الخطوة 2: فحص النظام
افتح: `http://localhost:8000/check_system.php`

### الخطوة 3: التثبيت المبسط
افتح: `http://localhost:8000/simple_install.php`

### الخطوة 4: إدخال بيانات قاعدة البيانات
```
Host: localhost
Username: root
Password: (اتركه فارغ في XAMPP)
Database: restaurant_management_system
```

## 🐛 مشاكل شائعة وحلولها:

### المشكلة: ملف database.sql غير موجود
**الحل:** تأكد من وجود الملف في نفس مجلد المشروع

### المشكلة: خطأ في الاتصال بـ MySQL
**الحل:** 
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من بيانات الاتصال
3. جرب كلمة مرور فارغة

### المشكلة: مجلد config غير قابل للكتابة
**الحل:**
```bash
# Windows: تشغيل كـ Administrator
# Linux/Mac:
chmod 755 config/
```

### المشكلة: خطأ في syntax
**الحل:** تحقق من إصدار PHP (يجب 7.4+)

## 📱 اختبار سريع:

### 1. تحقق من PHP:
```bash
php -v
```

### 2. تحقق من MySQL:
```bash
mysql --version
```

### 3. تحقق من الملفات:
```bash
ls -la
# يجب أن ترى database.sql
```

## 🎯 الحل النهائي:

إذا استمرت المشاكل، استخدم هذا الترتيب:

1. **شغل الخادم:** `php -S localhost:8000`
2. **افتح:** `http://localhost:8000/check_system.php`
3. **اقرأ التقرير** وطبق التوصيات
4. **افتح:** `http://localhost:8000/simple_install.php`
5. **أدخل بيانات قاعدة البيانات**
6. **اضغط "تثبيت النظام"**

## 📞 إذا لم تنجح الحلول:

### تحقق من:
- [ ] تشغيل Apache و MySQL
- [ ] إصدار PHP 7.4+
- [ ] وجود ملف database.sql
- [ ] صلاحيات الكتابة في المجلد
- [ ] عدم وجود مسافات في مسار المجلد

### ملفات التثبيت المتاحة:
- `simple_install.php` - الأبسط والأكثر أماناً
- `install.php` - تثبيت متقدم
- `setup.php` - معالج الإعداد
- `check_system.php` - فحص وتشخيص

---

**💡 نصيحة:** ابدأ دائماً بـ `check_system.php` لفهم المشكلة!
