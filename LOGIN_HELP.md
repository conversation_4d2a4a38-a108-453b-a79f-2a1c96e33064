# 🔑 حل مشاكل تسجيل الدخول

## 🚨 المشكلة: خطأ في تحميل صفحة تسجيل الدخول

### ✅ الحلول السريعة:

#### الحل الأول: استخدم صفحة التشخيص
```
http://localhost:8000/debug_login.php
```
هذه الصفحة ستفحص جميع المشاكل المحتملة وتقدم حلول فورية.

#### الحل الثاني: استخدم صفحة تسجيل الدخول الآمنة
```
http://localhost:8000/login_safe.php
```
هذه نسخة محسنة من صفحة تسجيل الدخول مع معالجة أفضل للأخطاء.

#### الحل الثالث: إعادة التثبيت
```
http://localhost:8000/simple_install.php
```

---

## 🎯 خطوات الإصلاح المرتبة:

### الخطوة 1: تشخيص المشكلة
```bash
# افتح هذا الرابط أولاً
http://localhost:8000/debug_login.php
```

### الخطوة 2: تطبيق الإصلاحات
حسب نتائج التشخيص:

**إذا كان ملف الإعداد مفقود:**
- اذهب إلى `simple_install.php`

**إذا كانت كلمات المرور خاطئة:**
- اضغط "إصلاح كلمات المرور" في صفحة التشخيص

**إذا كانت قاعدة البيانات فارغة:**
- أعد التثبيت من `simple_install.php`

### الخطوة 3: اختبار تسجيل الدخول
```
http://localhost:8000/login.php
```

---

## 🔧 بيانات تسجيل الدخول الصحيحة:

| المستخدم | كلمة المرور | الدور |
|----------|-------------|-------|
| `admin` | `password123` | مدير |
| `cashier` | `password123` | كاشير |
| `kitchen` | `password123` | مطبخ |

---

## 🚀 أزرار تسجيل الدخول السريع

في صفحة تسجيل الدخول الجديدة، ستجد أزرار:
- **دخول كمدير** - يملأ البيانات تلقائياً
- **دخول ككاشير** - يملأ البيانات تلقائياً  
- **دخول كمطبخ** - يملأ البيانات تلقائياً

---

## 🐛 مشاكل شائعة وحلولها:

### المشكلة: "خطأ في تحميل الإعدادات"
**الحل:**
```bash
# تحقق من وجود الملف
ls config/database.php

# إذا لم يكن موجود
http://localhost:8000/simple_install.php
```

### المشكلة: "اسم المستخدم أو كلمة المرور غير صحيحة"
**الحل:**
```bash
# افتح صفحة التشخيص
http://localhost:8000/debug_login.php

# اضغط "إصلاح كلمات المرور"
```

### المشكلة: "حدث خطأ في النظام"
**الحل:**
```bash
# تحقق من تشغيل MySQL
# في XAMPP: تأكد من تشغيل MySQL

# أعد التثبيت
http://localhost:8000/simple_install.php
```

### المشكلة: صفحة فارغة
**الحل:**
```bash
# تحقق من أخطاء PHP
http://localhost:8000/check_system.php

# أو استخدم الصفحة الآمنة
http://localhost:8000/login_safe.php
```

---

## 📱 اختبار سريع:

### 1. تحقق من الخادم:
```bash
# تأكد من تشغيل الخادم
php -S localhost:8000
```

### 2. تحقق من MySQL:
```bash
# في XAMPP Control Panel
# تأكد من تشغيل MySQL (أخضر)
```

### 3. اختبر الاتصال:
```bash
http://localhost:8000/check_system.php
```

### 4. اختبر تسجيل الدخول:
```bash
http://localhost:8000/debug_login.php
```

---

## 🎉 الحل النهائي (مضمون):

إذا لم تنجح جميع الحلول:

```bash
# 1. احذف مجلد config
rm -rf config/

# 2. أعد التثبيت
http://localhost:8000/simple_install.php

# 3. أدخل بيانات قاعدة البيانات:
Host: localhost
Username: root  
Password: (فارغ في XAMPP)
Database: restaurant_management_system

# 4. اضغط "تثبيت النظام"

# 5. جرب تسجيل الدخول:
http://localhost:8000/login.php
```

---

## 📞 ملفات المساعدة المتاحة:

- `debug_login.php` - تشخيص شامل لمشاكل تسجيل الدخول
- `login_safe.php` - صفحة تسجيل دخول آمنة ومحسنة
- `simple_install.php` - إعادة تثبيت سريعة
- `check_system.php` - فحص النظام الكامل
- `LOGIN_HELP.md` - هذا الملف

---

## 💡 نصائح مهمة:

1. **استخدم دائماً `debug_login.php` أولاً** لفهم المشكلة
2. **تأكد من تشغيل MySQL** قبل أي شيء
3. **استخدم الأزرار السريعة** لتسجيل الدخول
4. **احتفظ بنسخة احتياطية** من قاعدة البيانات

---

**🚀 ابدأ الآن:** `http://localhost:8000/debug_login.php`
