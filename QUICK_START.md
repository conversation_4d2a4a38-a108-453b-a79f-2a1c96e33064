# 🚀 دليل التشغيل السريع

## خطوات التشغيل في 5 دقائق

### 1️⃣ إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات في MySQL
mysql -u root -p
CREATE DATABASE restaurant_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit

# استيراد البيانات
mysql -u root -p restaurant_management_system < database.sql
```

### 2️⃣ تشغيل الخادم المحلي

**على Windows:**
```bash
# انقر مرتين على الملف أو شغل الأمر
start_server.bat
```

**على Linux/Mac:**
```bash
# اجعل الملف قابل للتنفيذ
chmod +x start_server.sh

# شغل الخادم
./start_server.sh
```

**أو استخدم PHP مباشرة:**
```bash
php -S localhost:8000
```

### 3️⃣ الوصول للنظام
افتح المتصفح وانتقل إلى: **http://localhost:8000**

### 4️⃣ تسجيل الدخول

| المستخدم | كلمة المرور | الصلاحيات |
|----------|-------------|-----------|
| `admin` | `password123` | مدير النظام |
| `cashier` | `password123` | موظف كاشير |
| `kitchen` | `password123` | موظف مطبخ |

## 🎯 اختبار النظام

### للمدير/الكاشير:
1. سجل دخول بحساب `admin` أو `cashier`
2. اذهب إلى "إدارة الطاولات"
3. اختر طاولة متاحة لإضافة طلب
4. أضف منتجات للطلب
5. اذهب إلى "الكاشير" لإدارة الطلب
6. أتمم الطلب واطبع الفاتورة

### لموظف المطبخ:
1. سجل دخول بحساب `kitchen`
2. ستظهر شاشة المطبخ مباشرة
3. حدث حالة الطلبات من "معلق" إلى "قيد التحضير" إلى "جاهز"

## ⚡ مميزات سريعة

- ✅ **واجهة عربية 100%** مع دعم RTL
- ✅ **تحديث تلقائي** كل 30 ثانية
- ✅ **طباعة فواتير** بتصميم احترافي
- ✅ **إدارة طاولات** بصرية وسهلة
- ✅ **تتبع طلبات** في الوقت الفعلي
- ✅ **أدوار مستخدمين** متعددة

## 🔧 إعدادات سريعة

### تغيير بيانات المطعم:
عدل ملف `config.php`:
```php
define('RESTAURANT_NAME', 'اسم مطعمك');
define('RESTAURANT_ADDRESS', 'عنوان المطعم');
define('RESTAURANT_PHONE', 'رقم الهاتف');
```

### إضافة منتجات جديدة:
```sql
INSERT INTO products (name, description, price, category_id) 
VALUES ('قهوة تركية', 'قهوة تركية أصيلة', 12.00, 1);
```

### إضافة طاولات جديدة:
```sql
INSERT INTO tables (table_number, capacity) 
VALUES ('6', 4);
```

## 🐛 حل المشاكل السريع

**مشكلة: خطأ في الاتصال بقاعدة البيانات**
- تحقق من تشغيل MySQL
- عدل بيانات الاتصال في `config/database.php`

**مشكلة: صفحة فارغة**
- تحقق من أخطاء PHP في `error_log`
- تأكد من صلاحيات الملفات

**مشكلة: النص العربي لا يظهر صحيحاً**
- تأكد من أن قاعدة البيانات تستخدم `utf8mb4`
- تحقق من إعدادات الخادم

## 📱 اختبار على الهاتف

للاختبار على الهاتف المحمول:
1. تأكد من أن الهاتف والكمبيوتر على نفس الشبكة
2. اعرف IP الكمبيوتر: `ipconfig` (Windows) أو `ifconfig` (Linux/Mac)
3. افتح المتصفح في الهاتف واذهب إلى: `http://IP_ADDRESS:8000`

## 🎉 جاهز للاستخدام!

النظام الآن جاهز للاستخدام. يمكنك:
- إضافة المزيد من المنتجات والفئات
- تخصيص التصميم حسب احتياجاتك
- إضافة المزيد من المستخدمين
- تطوير مميزات إضافية

---

**💡 نصيحة:** احفظ نسخة احتياطية من قاعدة البيانات بانتظام!
