# نظام إدارة كاشير المطاعم والكافي

## 🚀 النسخة السريعة - المميزات

✅ **تسجيل دخول للمستخدمين** - نظام مصادقة آمن مع أدوار مختلفة  
✅ **إدارة الطاولات** - عرض حالة الطاولات (متاحة/مشغولة)  
✅ **إضافة الطلبات للطاولات** - واجهة سهلة لإضافة المنتجات  
✅ **شاشة كاشير** - إدارة الطلبات والدفع مع عرض الفاتورة  
✅ **شاشة المطبخ (KOT)** - تتبع حالة تحضير الطلبات  
✅ **واجهة عربية 100%** - تصميم متجاوب يدعم اللغة العربية  
✅ **قاعدة بيانات MySQL مرفقة** - مع بيانات تجريبية  
✅ **كود PHP خام** - بدون إطار عمل معقد  

## 📋 متطلبات التشغيل

- **خادم ويب**: Apache أو Nginx
- **PHP**: الإصدار 7.4 أو أحدث
- **قاعدة البيانات**: MySQL 5.7 أو أحدث
- **متصفح**: Chrome, Firefox, Safari, Edge

## 🛠️ تعليمات التثبيت

### 1. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE restaurant_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد البيانات
mysql -u root -p restaurant_management_system < database.sql
```

### 2. إعداد ملف الاتصال

قم بتعديل ملف `config/database.php` وتحديث بيانات الاتصال:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'restaurant_management_system');
define('DB_USER', 'root');
define('DB_PASS', 'كلمة_المرور_الخاصة_بك');
```

### 3. رفع الملفات

- انسخ جميع الملفات إلى مجلد الخادم (htdocs أو www)
- تأكد من أن الخادم يدعم PHP و MySQL

### 4. الوصول للنظام

افتح المتصفح وانتقل إلى: `http://localhost/اسم_المجلد`

## 👥 بيانات تسجيل الدخول التجريبية

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|--------------|-------------|-----------|
| **مدير** | `admin` | `password123` | جميع الصلاحيات |
| **كاشير** | `cashier` | `password123` | الطاولات، الطلبات، الكاشير |
| **مطبخ** | `kitchen` | `password123` | شاشة المطبخ فقط |

## 🎯 كيفية الاستخدام

### للمدير/الكاشير:
1. **تسجيل الدخول** باستخدام بيانات المدير أو الكاشير
2. **إدارة الطاولات** - عرض وإضافة الطاولات
3. **إضافة طلب جديد** - اختيار طاولة متاحة وإضافة المنتجات
4. **شاشة الكاشير** - إدارة الطلبات النشطة والدفع
5. **طباعة الفاتورة** - إصدار فاتورة للعميل

### لموظف المطبخ:
1. **تسجيل الدخول** باستخدام بيانات المطبخ
2. **شاشة المطبخ** - عرض الطلبات الجديدة
3. **تحديث حالة الطلبات** - من معلق → قيد التحضير → جاهز
4. **إشعار الكاشير** عند اكتمال الطلب

## 📱 المميزات التقنية

- **تصميم متجاوب** - يعمل على جميع الأجهزة
- **تحديث تلقائي** - تحديث الشاشات كل 30 ثانية
- **واجهة عربية** - دعم كامل للغة العربية واتجاه RTL
- **طباعة الفواتير** - تصميم مخصص للطباعة الحرارية
- **أمان عالي** - حماية من SQL Injection و XSS
- **سهولة الاستخدام** - واجهة بديهية وسريعة

## 🗂️ هيكل المشروع

```
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── includes/
│   ├── header.php           # رأس الصفحة
│   └── footer.php           # تذييل الصفحة
├── ajax/
│   ├── add_table.php        # إضافة طاولة
│   ├── create_order.php     # إنشاء طلب
│   ├── complete_order.php   # إتمام طلب
│   └── ...                  # ملفات AJAX أخرى
├── database.sql             # قاعدة البيانات
├── index.php               # الصفحة الرئيسية
├── login.php               # تسجيل الدخول
├── dashboard.php           # لوحة التحكم
├── tables.php              # إدارة الطاولات
├── cashier.php             # شاشة الكاشير
├── kitchen.php             # شاشة المطبخ
├── print_invoice.php       # طباعة الفاتورة
└── README.md               # هذا الملف
```

## 🔧 التخصيص

### إضافة منتجات جديدة:
```sql
INSERT INTO products (name, description, price, category_id) 
VALUES ('اسم المنتج', 'الوصف', السعر, معرف_الفئة);
```

### إضافة فئة جديدة:
```sql
INSERT INTO categories (name) VALUES ('اسم الفئة');
```

### إضافة مستخدم جديد:
```sql
INSERT INTO users (username, password, full_name, role) 
VALUES ('اسم_المستخدم', '$2y$10$...', 'الاسم الكامل', 'الدور');
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من بيانات الاتصال في `config/database.php`
   - تأكد من تشغيل خدمة MySQL

2. **صفحة فارغة أو خطأ 500**
   - تحقق من سجلات الأخطاء في PHP
   - تأكد من صلاحيات الملفات

3. **مشاكل في الترميز العربي**
   - تأكد من أن قاعدة البيانات تستخدم `utf8mb4_unicode_ci`
   - تحقق من إعدادات الخادم

## 📞 الدعم

لأي استفسارات أو مشاكل تقنية، يمكنك:
- فحص ملف `error_log` في الخادم
- التحقق من إعدادات PHP و MySQL
- مراجعة بيانات الاتصال بقاعدة البيانات

## 📄 الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله بحرية.

---

**تم تطوير النظام باستخدام:**
- PHP 8.0+
- MySQL 8.0+
- Bootstrap 5.3
- Font Awesome 6.0
- jQuery 3.6

**آخر تحديث:** ديسمبر 2024
