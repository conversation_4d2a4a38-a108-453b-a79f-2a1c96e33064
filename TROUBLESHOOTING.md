# 🔧 حل المشاكل الشائعة

## ❌ المشكلة: Warning: Undefined array key

### الأعراض:
```
Warning: Undefined array key "host" in setup.php
Warning: Undefined array key "username" in setup.php
Warning: Undefined array key "password" in setup.php
Warning: Undefined array key "database" in setup.php
```

### ✅ الحل:
تم إصلاح هذه المشكلة! استخدم أحد الحلول التالية:

#### الحل الأول: استخدم ملف التثبيت الجديد
```
http://localhost:8000/install.php
```

#### الحل الثاني: استخدم ملف setup.php المحدث
```
http://localhost:8000/setup.php
```

---

## 🗄️ مشاكل قاعدة البيانات

### المشكلة: خطأ في الاتصال بقاعدة البيانات

#### الحلول:
1. **تأكد من تشغيل MySQL:**
   ```bash
   # Windows (XAMPP)
   تشغيل XAMPP Control Panel وتفعيل MySQL
   
   # Linux
   sudo systemctl start mysql
   
   # Mac
   brew services start mysql
   ```

2. **تحقق من بيانات الاتصال:**
   - Host: عادة `localhost`
   - Username: عادة `root`
   - Password: قد تكون فارغة في XAMPP
   - Database: `restaurant_management_system`

3. **إنشاء قاعدة البيانات يدوياً:**
   ```sql
   CREATE DATABASE restaurant_management_system 
   CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

---

## 🌐 مشاكل الخادم

### المشكلة: صفحة فارغة أو خطأ 500

#### الحلول:
1. **تحقق من إصدار PHP:**
   ```bash
   php --version
   # يجب أن يكون 7.4 أو أحدث
   ```

2. **تفعيل عرض الأخطاء:**
   أضف في بداية ملف index.php:
   ```php
   ini_set('display_errors', 1);
   error_reporting(E_ALL);
   ```

3. **تحقق من صلاحيات الملفات:**
   ```bash
   chmod -R 755 /path/to/project
   ```

---

## 🔤 مشاكل الترميز العربي

### المشكلة: النص العربي يظهر كرموز غريبة

#### الحلول:
1. **تأكد من ترميز قاعدة البيانات:**
   ```sql
   ALTER DATABASE restaurant_management_system 
   CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **تحقق من ترميز الملفات:**
   - احفظ جميع ملفات PHP بترميز UTF-8

3. **تأكد من إعدادات المتصفح:**
   - اضبط ترميز الصفحة على UTF-8

---

## 📱 مشاكل التصميم المتجاوب

### المشكلة: التصميم لا يظهر صحيحاً على الهاتف

#### الحلول:
1. **تحقق من اتصال الإنترنت:**
   - النظام يستخدم Bootstrap من CDN

2. **استخدم متصفح حديث:**
   - Chrome, Firefox, Safari, Edge

---

## 🖨️ مشاكل الطباعة

### المشكلة: الفاتورة لا تطبع صحيحاً

#### الحلول:
1. **تحقق من إعدادات الطابعة:**
   - اختر حجم ورق مناسب (A4 أو 80mm)

2. **استخدم متصفح مختلف:**
   - Chrome عادة يعطي أفضل نتائج طباعة

3. **تحقق من CSS الطباعة:**
   - تأكد من تحميل ملفات CSS بشكل صحيح

---

## 🔐 مشاكل تسجيل الدخول

### المشكلة: لا يمكن تسجيل الدخول

#### الحلول:
1. **استخدم البيانات الصحيحة:**
   ```
   المدير: admin / password123
   الكاشير: cashier / password123
   المطبخ: kitchen / password123
   ```

2. **تحقق من الجلسات:**
   ```php
   // في config/database.php
   session_start();
   ```

3. **امسح cache المتصفح:**
   - Ctrl+F5 أو Cmd+Shift+R

---

## 🚀 نصائح للأداء

### تحسين سرعة النظام:

1. **استخدم خادم محلي سريع:**
   ```bash
   php -S localhost:8000
   ```

2. **تفعيل ضغط الملفات:**
   - ملف .htaccess يحتوي على إعدادات الضغط

3. **تحديث PHP و MySQL:**
   - استخدم أحدث الإصدارات المستقرة

---

## 📞 الحصول على المساعدة

### إذا استمرت المشاكل:

1. **تحقق من ملف error_log:**
   ```bash
   tail -f /path/to/error_log
   ```

2. **استخدم ملف الاختبار:**
   ```
   http://localhost:8000/test_connection.php
   ```

3. **تأكد من المتطلبات:**
   - PHP 7.4+
   - MySQL 5.7+
   - Apache/Nginx

---

## ✅ اختبار سريع

للتأكد من عمل النظام:

```bash
# 1. تشغيل الخادم
php -S localhost:8000

# 2. فتح المتصفح
http://localhost:8000/test_connection.php

# 3. تسجيل الدخول
http://localhost:8000/login.php
```

---

**💡 نصيحة:** احتفظ بنسخة احتياطية من قاعدة البيانات قبل إجراء أي تعديلات!
