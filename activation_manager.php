<?php
require_once 'config/database.php';
require_once 'includes/activation_check.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

$page_title = 'إدارة التفعيل والترخيص';
$message = '';
$message_type = '';

// معالجة تفعيل ترخيص جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['activate_license'])) {
    $license_key = trim($_POST['license_key']);
    
    if (empty($license_key)) {
        $message = 'يرجى إدخال مفتاح الترخيص';
        $message_type = 'danger';
    } else {
        try {
            $pdo->beginTransaction();
            
            // البحث عن الترخيص
            $stmt = $pdo->prepare("
                SELECT * FROM activation_licenses 
                WHERE license_key = ? AND is_active = 1
            ");
            $stmt->execute([$license_key]);
            $license = $stmt->fetch();
            
            if (!$license) {
                throw new Exception('مفتاح الترخيص غير صحيح أو غير نشط');
            }
            
            // فحص انتهاء الصلاحية
            if ($license['expires_at'] && strtotime($license['expires_at']) < time()) {
                throw new Exception('انتهت صلاحية هذا الترخيص');
            }
            
            // فحص إذا كان مفعل مسبقاً
            if ($license['activated_at']) {
                throw new Exception('هذا الترخيص مفعل مسبقاً');
            }
            
            // تفعيل الترخيص
            $stmt = $pdo->prepare("
                UPDATE activation_licenses 
                SET activated_at = NOW(), activated_by = ? 
                WHERE id = ?
            ");
            $stmt->execute([$_SESSION['user_id'], $license['id']]);
            
            // تحديث إعدادات النظام
            $new_status = $license['license_type'] == 'trial' ? 'trial' : 'active';
            
            $updates = [
                ['system_status', $new_status],
                ['license_key', $license_key],
                ['activation_date', date('Y-m-d H:i:s')],
                ['trial_orders_limit', $license['max_orders']]
            ];
            
            $stmt = $pdo->prepare("
                UPDATE system_settings 
                SET setting_value = ? 
                WHERE setting_key = ?
            ");
            
            foreach ($updates as $update) {
                $stmt->execute([$update[1], $update[0]]);
            }
            
            // تسجيل في سجل التفعيل
            $stmt = $pdo->prepare("
                INSERT INTO activation_log 
                (action, license_key, user_id, new_data, ip_address, user_agent) 
                VALUES ('activate', ?, ?, ?, ?, ?)
            ");
            
            $activation_data = json_encode([
                'license_type' => $license['license_type'],
                'max_orders' => $license['max_orders'],
                'expires_at' => $license['expires_at']
            ]);
            
            $stmt->execute([
                $license_key,
                $_SESSION['user_id'],
                $activation_data,
                $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ]);
            
            $pdo->commit();
            
            $message = 'تم تفعيل الترخيص بنجاح!';
            $message_type = 'success';
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $message = 'خطأ في التفعيل: ' . $e->getMessage();
            $message_type = 'danger';
        }
    }
}

// الحصول على حالة التفعيل الحالية
$activation_status = checkSystemActivation();
$usage_stats = isset($activationManager) ? $activationManager->getUsageStats() : [];

include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- حالة النظام الحالية -->
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        حالة التفعيل الحالية
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <?php
                                    $status_icons = [
                                        'trial_active' => '<i class="fas fa-clock fa-2x text-warning"></i>',
                                        'active' => '<i class="fas fa-check-circle fa-2x text-success"></i>',
                                        'expired' => '<i class="fas fa-times-circle fa-2x text-danger"></i>',
                                        'not_setup' => '<i class="fas fa-exclamation-triangle fa-2x text-warning"></i>'
                                    ];
                                    echo $status_icons[$activation_status['status']] ?? '<i class="fas fa-question-circle fa-2x text-secondary"></i>';
                                    ?>
                                </div>
                                <div>
                                    <h5 class="mb-1"><?php echo htmlspecialchars($activation_status['message']); ?></h5>
                                    <small class="text-muted">
                                        حالة النظام: 
                                        <strong>
                                            <?php
                                            $status_text = [
                                                'trial_active' => 'نسخة تجريبية نشطة',
                                                'active' => 'مفعل بالكامل',
                                                'expired' => 'منتهي الصلاحية',
                                                'not_setup' => 'غير مُعد'
                                            ];
                                            echo $status_text[$activation_status['status']] ?? 'غير معروف';
                                            ?>
                                        </strong>
                                    </small>
                                </div>
                            </div>
                            
                            <?php if (isset($activation_status['remaining_orders'])): ?>
                                <div class="progress mb-3" style="height: 25px;">
                                    <?php
                                    $used = ($usage_stats['total_orders'] ?? 0);
                                    $total = $used + $activation_status['remaining_orders'];
                                    $percentage = $total > 0 ? ($used / $total) * 100 : 0;
                                    $color = $percentage > 80 ? 'danger' : ($percentage > 60 ? 'warning' : 'success');
                                    ?>
                                    <div class="progress-bar bg-<?php echo $color; ?>" style="width: <?php echo $percentage; ?>%">
                                        <?php echo $used; ?> / <?php echo $total; ?> طلبات
                                    </div>
                                </div>
                                <p class="text-muted small">
                                    متبقي <strong><?php echo $activation_status['remaining_orders']; ?></strong> طلبات من النسخة التجريبية
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="table-responsive">
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td><strong>إجمالي الطلبات:</strong></td>
                                        <td><span class="badge bg-primary"><?php echo $usage_stats['total_orders'] ?? 0; ?></span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>طلبات اليوم:</strong></td>
                                        <td><span class="badge bg-info"><?php echo $usage_stats['today_orders'] ?? 0; ?></span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>عدد المستخدمين:</strong></td>
                                        <td><span class="badge bg-success"><?php echo $usage_stats['total_users'] ?? 0; ?></span></td>
                                    </tr>
                                    <?php if (isset($activation_status['expires_at']) && $activation_status['expires_at']): ?>
                                    <tr>
                                        <td><strong>ينتهي في:</strong></td>
                                        <td><small class="text-muted"><?php echo date('Y-m-d', strtotime($activation_status['expires_at'])); ?></small></td>
                                    </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تفعيل ترخيص جديد -->
            <?php if (!$activation_status['can_use'] || (isset($activation_status['is_trial']) && $activation_status['is_trial'])): ?>
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        تفعيل ترخيص جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="license_key" class="form-label">مفتاح الترخيص:</label>
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="license_key" 
                                           name="license_key" 
                                           placeholder="أدخل مفتاح الترخيص هنا..."
                                           required>
                                    <div class="form-text">
                                        أدخل مفتاح الترخيص الذي حصلت عليه لتفعيل النسخة الكاملة
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" name="activate_license" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-unlock me-2"></i>
                                    تفعيل الترخيص
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- معلومات الترخيص -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-gift me-2"></i>
                                النسخة التجريبية
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>10 طلبات مجانية</li>
                                <li><i class="fas fa-check text-success me-2"></i>30 يوم تجربة</li>
                                <li><i class="fas fa-check text-success me-2"></i>جميع المزايا الأساسية</li>
                                <li><i class="fas fa-times text-danger me-2"></i>بدون دعم فني</li>
                            </ul>
                            <div class="text-center">
                                <span class="badge bg-warning">مجاني</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-star me-2"></i>
                                النسخة الكاملة
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>طلبات غير محدودة</li>
                                <li><i class="fas fa-check text-success me-2"></i>بدون انتهاء صلاحية</li>
                                <li><i class="fas fa-check text-success me-2"></i>جميع المزايا</li>
                                <li><i class="fas fa-check text-success me-2"></i>دعم فني كامل</li>
                            </ul>
                            <div class="text-center">
                                <span class="badge bg-success">$99</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-crown me-2"></i>
                                النسخة المتقدمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>جميع مزايا الكاملة</li>
                                <li><i class="fas fa-check text-success me-2"></i>تقارير متقدمة</li>
                                <li><i class="fas fa-check text-success me-2"></i>تكامل مع أنظمة أخرى</li>
                                <li><i class="fas fa-check text-success me-2"></i>دعم أولوية عالية</li>
                            </ul>
                            <div class="text-center">
                                <span class="badge bg-primary">$199</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- روابط مفيدة -->
            <div class="card mt-4 border-0 shadow-sm">
                <div class="card-body text-center">
                    <h6 class="mb-3">إدارة النظام</h6>
                    <div class="d-flex justify-content-center gap-2 flex-wrap">
                        <a href="license_generator.php" class="btn btn-outline-primary">
                            <i class="fas fa-certificate me-1"></i>مولد التراخيص
                        </a>
                        <a href="system_status.php" class="btn btn-outline-info">
                            <i class="fas fa-chart-line me-1"></i>حالة النظام
                        </a>
                        <a href="setup_activation_system.php" class="btn btn-outline-secondary">
                            <i class="fas fa-cog me-1"></i>إعدادات التفعيل
                        </a>
                        <a href="dashboard.php" class="btn btn-outline-dark">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
