<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$table_id = intval($_GET['table_id'] ?? 0);

if ($table_id < 1) {
    showMessage('معرف الطاولة غير صحيح', 'error');
    redirect('tables.php');
}

try {
    // جلب بيانات الطاولة
    $stmt = $pdo->prepare("SELECT * FROM tables WHERE id = ? AND status = 'available'");
    $stmt->execute([$table_id]);
    $table = $stmt->fetch();
    
    if (!$table) {
        showMessage('الطاولة غير متاحة', 'error');
        redirect('tables.php');
    }
    
    // جلب فئات المنتجات
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
    
    // جلب المنتجات مع التحقق من عمود الصورة
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'image_path'");
    $has_image_column = $stmt->rowCount() > 0;

    $image_select = $has_image_column ? ', p.image_path' : ', NULL as image_path';

    $stmt = $pdo->query("
        SELECT p.id, p.name, p.description, p.price, p.category_id $image_select, c.name as category_name
        FROM products p
        JOIN categories c ON p.category_id = c.id
        ORDER BY c.name, p.name
    ");
    $products = $stmt->fetchAll();
    
    // تجميع المنتجات حسب الفئة
    $products_by_category = [];
    foreach ($products as $product) {
        $products_by_category[$product['category_id']][] = $product;
    }
    
} catch (PDOException $e) {
    showMessage('حدث خطأ في جلب البيانات', 'error');
    redirect('tables.php');
}

$page_title = 'إضافة طلب - طاولة ' . $table['table_number'];
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-plus me-2"></i>
        إضافة طلب - طاولة <?php echo $table['table_number']; ?>
    </h2>
    <a href="tables.php" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للطاولات
    </a>
</div>

<div class="row">
    <!-- قائمة المنتجات -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المنتجات
                </h5>
            </div>
            <div class="card-body">
                <!-- فلاتر الفئات -->
                <div class="mb-3">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary active" onclick="filterCategory('all')">
                            الكل
                        </button>
                        <?php foreach ($categories as $category): ?>
                            <button type="button" class="btn btn-outline-primary" onclick="filterCategory(<?php echo $category['id']; ?>)">
                                <?php echo $category['name']; ?>
                            </button>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- المنتجات -->
                <div class="row" id="products-container">
                    <?php foreach ($products as $product): ?>
                        <div class="col-md-6 col-lg-4 mb-3 product-item" data-category="<?php echo $product['category_id']; ?>">
                            <div class="card h-100 product-card" onclick="addProductToOrder(<?php echo $product['id']; ?>)">
                                <?php
                                // التحقق من وجود الصورة
                                $has_image = false;
                                $image_url = '';

                                if (!empty($product['image_path'])) {
                                    if (file_exists($product['image_path'])) {
                                        $has_image = true;
                                        $image_url = $product['image_path'] . '?v=' . time();
                                    }
                                }
                                ?>

                                <?php if ($has_image): ?>
                                    <img src="<?php echo $image_url; ?>"
                                         class="card-img-top"
                                         style="height: 120px; object-fit: cover;"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <?php endif; ?>

                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                     style="height: 120px; <?php echo $has_image ? 'display: none;' : ''; ?>">
                                    <div class="text-center">
                                        <i class="fas fa-utensils fa-2x text-muted mb-1"></i>
                                        <div class="small text-muted">لا توجد صورة</div>
                                    </div>
                                </div>

                                <div class="card-body text-center">
                                    <h6 class="card-title"><?php echo $product['name']; ?></h6>
                                    <p class="card-text small text-muted"><?php echo $product['description']; ?></p>
                                    <div class="price-tag">
                                        <strong><?php echo formatPrice($product['price']); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- سلة الطلب -->
    <div class="col-md-4">
        <div class="card sticky-top">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    سلة الطلب
                </h5>
            </div>
            <div class="card-body">
                <div id="order-items">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>لم يتم إضافة أي منتجات بعد</p>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <strong>الإجمالي:</strong>
                    <strong id="total-amount">0 د.ع</strong>
                </div>
                
                <button type="button" class="btn btn-success w-100" id="submit-order" onclick="submitOrder()" disabled>
                    <i class="fas fa-check me-2"></i>
                    تأكيد الطلب
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.product-card {
    cursor: pointer;
    transition: all 0.3s;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-5px);
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.price-tag {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    display: inline-block;
    margin-top: 10px;
}

.order-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    background: #f8f9fa;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quantity-controls button {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-controls input {
    width: 50px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.quantity-display {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 5px 10px;
    min-width: 40px;
    text-align: center;
    font-weight: bold;
}
</style>

<script>
// متغيرات النظام
let orderItems = {};
let products = <?php echo json_encode($products); ?>;

// تسجيل بداية تحميل الصفحة
console.log('🚀 بدء تحميل صفحة إضافة الطلب');
console.log('📋 عدد المنتجات المتاحة:', products.length);

// التأكد من تحميل الدوال
window.addEventListener('DOMContentLoaded', function() {
    console.log('✅ تم تحميل الصفحة بالكامل');
    console.log('🔧 فحص الدوال المطلوبة...');

    // فحص الدوال
    console.log('updateQuantity:', typeof updateQuantity);
    console.log('addProductToOrder:', typeof addProductToOrder);
    console.log('updateOrderDisplay:', typeof updateOrderDisplay);

    // فحص العناصر HTML
    const elements = {
        'order-items': document.getElementById('order-items'),
        'total-amount': document.getElementById('total-amount'),
        'submit-order': document.getElementById('submit-order')
    };

    console.log('🔍 فحص عناصر HTML:', elements);

    // تحديث العرض الأولي
    updateOrderDisplay();

    console.log('🎉 تم تهيئة النظام بنجاح');
});

function filterCategory(categoryId) {
    // تحديث أزرار الفلتر
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // إظهار/إخفاء المنتجات
    document.querySelectorAll('.product-item').forEach(item => {
        if (categoryId === 'all' || item.dataset.category == categoryId) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

function addProductToOrder(productId) {
    const product = products.find(p => p.id == productId);
    if (!product) return;

    if (orderItems[productId]) {
        orderItems[productId].quantity++;
    } else {
        orderItems[productId] = {
            id: productId,
            name: product.name,
            price: parseFloat(product.price),
            quantity: 1
        };
    }

    updateOrderDisplay();
}

// تم حذف الدالة المكررة - سيتم استخدام الدالة المحسنة أدناه

// تم حذف الدالة المكررة - سيتم استخدام removeItem أدناه

function removeItem(productId) {
    console.log('حذف منتج:', productId);
    delete orderItems[productId];
    updateOrderDisplay();
}

// دالة تحديث الكمية - الدالة الرئيسية
function updateQuantity(productId, change) {
    console.log('🔄 استدعاء updateQuantity:', productId, 'التغيير:', change);

    // التحقق من صحة المعاملات
    if (!productId || productId === undefined || productId === null) {
        console.error('❌ معرف المنتج غير صحيح:', productId);
        alert('خطأ: معرف المنتج غير صحيح');
        return false;
    }

    if (!orderItems[productId]) {
        console.error('❌ المنتج غير موجود في السلة:', productId);
        console.log('📋 محتويات السلة الحالية:', orderItems);
        alert('خطأ: المنتج غير موجود في السلة');
        return false;
    }

    try {
        const oldQuantity = orderItems[productId].quantity;
        console.log(`📊 الكمية الحالية للمنتج ${productId}: ${oldQuantity}`);

        if (typeof change === 'string') {
            // من input field
            const newQuantity = parseInt(change);
            console.log('📝 تحديث من input field:', newQuantity);

            if (newQuantity > 0) {
                orderItems[productId].quantity = newQuantity;
                console.log('✅ تم تحديث الكمية من input:', newQuantity);
            } else {
                const productName = orderItems[productId].name;
                delete orderItems[productId];
                console.log(`🗑️ تم حذف ${productName} (كمية 0 من input)`);
            }
        } else {
            // من أزرار + أو -
            console.log('🔘 تحديث من زر:', change > 0 ? '+' : '-');
            orderItems[productId].quantity += change;

            console.log(`📊 الكمية: ${oldQuantity} → ${orderItems[productId].quantity}`);

            if (orderItems[productId].quantity <= 0) {
                const productName = orderItems[productId].name;
                delete orderItems[productId];
                console.log(`🗑️ حذف ${productName} من السلة (كمية <= 0)`);
            }
        }

        console.log('📋 حالة السلة بعد التحديث:', orderItems);

        // تحديث العرض
        const updateResult = updateOrderDisplay();
        if (updateResult) {
            console.log('✅ تم تحديث الكمية والعرض بنجاح');
        } else {
            console.error('❌ فشل في تحديث العرض');
        }

        return true;

    } catch (error) {
        console.error('❌ خطأ في updateQuantity:', error);
        console.error('Stack trace:', error.stack);
        alert('حدث خطأ في تحديث الكمية: ' + error.message);
        return false;
    }
}

function updateOrderDisplay() {
    console.log('🔄 بدء تحديث عرض السلة...');

    // البحث عن العناصر المطلوبة
    const container = document.getElementById('order-items');
    const totalElement = document.getElementById('total-amount');
    const submitButton = document.getElementById('submit-order');

    console.log('🔍 فحص عناصر HTML:');
    console.log('- order-items:', container ? '✅ موجود' : '❌ مفقود');
    console.log('- total-amount:', totalElement ? '✅ موجود' : '❌ مفقود');
    console.log('- submit-order:', submitButton ? '✅ موجود' : '❌ مفقود');

    // التحقق من وجود العناصر المطلوبة
    if (!container) {
        console.error('❌ عنصر order-items غير موجود');
        alert('خطأ: عنصر السلة غير موجود في الصفحة');
        return false;
    }

    if (!totalElement) {
        console.error('❌ عنصر total-amount غير موجود');
        return false;
    }

    if (!submitButton) {
        console.error('❌ عنصر submit-order غير موجود');
        return false;
    }

    console.log('📋 عناصر السلة الحالية:', orderItems);
    console.log('📦 عدد العناصر في السلة:', Object.keys(orderItems).length);
    
    if (Object.keys(orderItems).length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>لم يتم إضافة أي منتجات بعد</p>
            </div>
        `;
        totalElement.textContent = '0 د.ع';
        submitButton.disabled = true;
        return;
    }
    
    let html = '';
    let total = 0;
    
    for (const item of Object.values(orderItems)) {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        console.log(`🔨 إنشاء HTML للمنتج: ${item.name} (ID: ${item.id})`);

        html += `
            <div class="order-item mb-3" data-product-id="${item.id}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">${Math.round(item.price)} د.ع</small>
                    </div>
                    <button type="button"
                            class="btn btn-sm btn-outline-danger"
                            onclick="console.log('🗑️ حذف منتج ${item.id}'); removeItem(${item.id})"
                            title="حذف المنتج">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="quantity-controls">
                        <button type="button"
                                class="btn btn-sm btn-outline-secondary btn-minus"
                                onclick="console.log('➖ تقليل كمية منتج ${item.id}'); updateQuantity(${item.id}, -1)"
                                title="تقليل الكمية"
                                data-product-id="${item.id}"
                                data-action="decrease">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="quantity-display mx-2 px-2 py-1 bg-light border rounded">${item.quantity}</span>
                        <button type="button"
                                class="btn btn-sm btn-outline-secondary btn-plus"
                                onclick="console.log('➕ زيادة كمية منتج ${item.id}'); updateQuantity(${item.id}, 1)"
                                title="زيادة الكمية"
                                data-product-id="${item.id}"
                                data-action="increase">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <strong>${Math.round(itemTotal)} د.ع</strong>
                </div>
            </div>
        `;

        console.log(`✅ تم إنشاء HTML للمنتج ${item.id} بنجاح`);
    }
    
    console.log('🔄 تحديث HTML في container...');
    container.innerHTML = html;

    console.log('💰 تحديث الإجمالي...');
    totalElement.textContent = Math.round(total) + ' د.ع';

    console.log('🔘 تفعيل زر التأكيد...');
    submitButton.disabled = false;

    console.log('✅ تم تحديث العرض بنجاح');
    console.log('💰 الإجمالي النهائي:', total, 'د.ع');
    console.log('📦 عدد العناصر:', Object.keys(orderItems).length);

    // فحص الأزرار المنشأة
    const plusButtons = container.querySelectorAll('.btn-plus');
    const minusButtons = container.querySelectorAll('.btn-minus');
    console.log(`🔘 تم إنشاء ${plusButtons.length} زر +`);
    console.log(`🔘 تم إنشاء ${minusButtons.length} زر -`);

    return true;
}

function submitOrder() {
    if (Object.keys(orderItems).length === 0) {
        alert('يرجى إضافة منتجات للطلب');
        return;
    }

    const orderData = {
        table_id: <?php echo $table_id; ?>,
        items: Object.values(orderItems)
    };

    console.log('إرسال طلب:', orderData);

    fetch('ajax/create_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => {
        console.log('استجابة HTTP:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('بيانات الاستجابة:', data);
        if (data.success) {
            alert('تم إنشاء الطلب بنجاح');
            window.location.href = 'tables.php';
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ في AJAX:', error);
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

// دوال اختبار إضافية
function testCartButtons() {
    console.log('🧪 اختبار أزرار السلة...');

    // فحص الدوال
    console.log('updateQuantity:', typeof updateQuantity);
    console.log('addProductToOrder:', typeof addProductToOrder);
    console.log('removeItem:', typeof removeItem);

    // فحص العناصر
    const elements = {
        'order-items': document.getElementById('order-items'),
        'total-amount': document.getElementById('total-amount'),
        'submit-order': document.getElementById('submit-order')
    };
    console.log('عناصر HTML:', elements);

    // فحص السلة
    console.log('محتويات السلة:', orderItems);
    console.log('عدد العناصر:', Object.keys(orderItems).length);

    // فحص الأزرار الموجودة
    const plusButtons = document.querySelectorAll('.btn-plus');
    const minusButtons = document.querySelectorAll('.btn-minus');
    console.log(`أزرار + موجودة: ${plusButtons.length}`);
    console.log(`أزرار - موجودة: ${minusButtons.length}`);

    console.log('✅ انتهى اختبار الأزرار');
}

function forceUpdateDisplay() {
    console.log('🔄 إجبار تحديث العرض...');
    updateOrderDisplay();
}

function showCartContents() {
    console.log('📋 محتويات السلة الحالية:');
    console.table(orderItems);

    if (Object.keys(orderItems).length === 0) {
        alert('السلة فارغة');
    } else {
        let message = 'محتويات السلة:\n\n';
        for (const item of Object.values(orderItems)) {
            message += `${item.name}: ${item.quantity} × ${item.price} = ${item.quantity * item.price} د.ع\n`;
        }
        alert(message);
    }
}

// إضافة الدوال للنطاق العام للاختبار
window.testCartButtons = testCartButtons;
window.forceUpdateDisplay = forceUpdateDisplay;
window.showCartContents = showCartContents;

// التقاط أخطاء JavaScript
window.onerror = function(msg, url, line, col, error) {
    console.error('❌ خطأ JavaScript:', {
        message: msg,
        source: url,
        line: line,
        column: col,
        error: error
    });
    return false;
};

window.addEventListener('unhandledrejection', function(event) {
    console.error('❌ خطأ Promise:', event.reason);
});
</script>

<?php include 'includes/footer.php'; ?>
