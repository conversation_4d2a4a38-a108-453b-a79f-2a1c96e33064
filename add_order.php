<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$table_id = intval($_GET['table_id'] ?? 0);

if ($table_id < 1) {
    showMessage('معرف الطاولة غير صحيح', 'error');
    redirect('tables.php');
}

try {
    // جلب بيانات الطاولة
    $stmt = $pdo->prepare("SELECT * FROM tables WHERE id = ? AND status = 'available'");
    $stmt->execute([$table_id]);
    $table = $stmt->fetch();
    
    if (!$table) {
        showMessage('الطاولة غير متاحة', 'error');
        redirect('tables.php');
    }
    
    // جلب فئات المنتجات
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
    
    // جلب المنتجات
    $stmt = $pdo->query("
        SELECT p.*, c.name as category_name 
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        ORDER BY c.name, p.name
    ");
    $products = $stmt->fetchAll();
    
    // تجميع المنتجات حسب الفئة
    $products_by_category = [];
    foreach ($products as $product) {
        $products_by_category[$product['category_id']][] = $product;
    }
    
} catch (PDOException $e) {
    showMessage('حدث خطأ في جلب البيانات', 'error');
    redirect('tables.php');
}

$page_title = 'إضافة طلب - طاولة ' . $table['table_number'];
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-plus me-2"></i>
        إضافة طلب - طاولة <?php echo $table['table_number']; ?>
    </h2>
    <a href="tables.php" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للطاولات
    </a>
</div>

<div class="row">
    <!-- قائمة المنتجات -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المنتجات
                </h5>
            </div>
            <div class="card-body">
                <!-- فلاتر الفئات -->
                <div class="mb-3">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary active" onclick="filterCategory('all')">
                            الكل
                        </button>
                        <?php foreach ($categories as $category): ?>
                            <button type="button" class="btn btn-outline-primary" onclick="filterCategory(<?php echo $category['id']; ?>)">
                                <?php echo $category['name']; ?>
                            </button>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- المنتجات -->
                <div class="row" id="products-container">
                    <?php foreach ($products as $product): ?>
                        <div class="col-md-6 col-lg-4 mb-3 product-item" data-category="<?php echo $product['category_id']; ?>">
                            <div class="card h-100 product-card" onclick="addProductToOrder(<?php echo $product['id']; ?>)">
                                <div class="card-body text-center">
                                    <h6 class="card-title"><?php echo $product['name']; ?></h6>
                                    <p class="card-text small text-muted"><?php echo $product['description']; ?></p>
                                    <div class="price-tag">
                                        <strong><?php echo formatPrice($product['price']); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- سلة الطلب -->
    <div class="col-md-4">
        <div class="card sticky-top">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    سلة الطلب
                </h5>
            </div>
            <div class="card-body">
                <div id="order-items">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>لم يتم إضافة أي منتجات بعد</p>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <strong>الإجمالي:</strong>
                    <strong id="total-amount">0.00 ريال</strong>
                </div>
                
                <button type="button" class="btn btn-success w-100" id="submit-order" onclick="submitOrder()" disabled>
                    <i class="fas fa-check me-2"></i>
                    تأكيد الطلب
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.product-card {
    cursor: pointer;
    transition: all 0.3s;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-5px);
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.price-tag {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    display: inline-block;
    margin-top: 10px;
}

.order-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    background: #f8f9fa;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quantity-controls button {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-controls input {
    width: 50px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}
</style>

<script>
let orderItems = {};
let products = <?php echo json_encode($products); ?>;

function filterCategory(categoryId) {
    // تحديث أزرار الفلتر
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // إظهار/إخفاء المنتجات
    document.querySelectorAll('.product-item').forEach(item => {
        if (categoryId === 'all' || item.dataset.category == categoryId) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

function addProductToOrder(productId) {
    const product = products.find(p => p.id == productId);
    if (!product) return;
    
    if (orderItems[productId]) {
        orderItems[productId].quantity++;
    } else {
        orderItems[productId] = {
            id: productId,
            name: product.name,
            price: parseFloat(product.price),
            quantity: 1
        };
    }
    
    updateOrderDisplay();
}

function updateQuantity(productId, quantity) {
    if (quantity < 1) {
        delete orderItems[productId];
    } else {
        orderItems[productId].quantity = quantity;
    }
    
    updateOrderDisplay();
}

function removeItem(productId) {
    delete orderItems[productId];
    updateOrderDisplay();
}

function updateOrderDisplay() {
    const container = document.getElementById('order-items');
    const totalElement = document.getElementById('total-amount');
    const submitButton = document.getElementById('submit-order');
    
    if (Object.keys(orderItems).length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>لم يتم إضافة أي منتجات بعد</p>
            </div>
        `;
        totalElement.textContent = '0.00 ريال';
        submitButton.disabled = true;
        return;
    }
    
    let html = '';
    let total = 0;
    
    for (const item of Object.values(orderItems)) {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        html += `
            <div class="order-item">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">${item.price.toFixed(2)} ريال</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(${item.id})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="quantity-controls">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" value="${item.quantity}" min="1" onchange="updateQuantity(${item.id}, this.value)">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <strong>${itemTotal.toFixed(2)} ريال</strong>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
    totalElement.textContent = total.toFixed(2) + ' ريال';
    submitButton.disabled = false;
}

function submitOrder() {
    if (Object.keys(orderItems).length === 0) {
        alert('يرجى إضافة منتجات للطلب');
        return;
    }
    
    const orderData = {
        table_id: <?php echo $table_id; ?>,
        items: Object.values(orderItems)
    };
    
    fetch('ajax/create_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إنشاء الطلب بنجاح');
            window.location.href = 'tables.php';
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال');
    });
}
</script>

<?php include 'includes/footer.php'; ?>
