<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('login.php');
}

$page_title = 'إضافة طلبات تجريبية';

// إضافة طلبات تجريبية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_test_orders'])) {
    try {
        $pdo->beginTransaction();
        
        // طلب معلق
        $stmt = $pdo->prepare("INSERT INTO orders (table_id, user_id, total_amount, status, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([1, $_SESSION['user_id'], 5000, 'pending']);
        $order1_id = $pdo->lastInsertId();
        
        // إضافة عناصر للطلب الأول
        $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
        $stmt->execute([$order1_id, 1, 2, 1000]); // شاي عراقي × 2
        $stmt->execute([$order1_id, 2, 1, 1500]); // قهوة عربية × 1
        $stmt->execute([$order1_id, 5, 1, 3000]); // حمص بالطحينة × 1
        
        // طلب قيد التحضير
        $stmt = $pdo->prepare("INSERT INTO orders (table_id, user_id, total_amount, status, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([3, $_SESSION['user_id'], 14000, 'processing']);
        $order2_id = $pdo->lastInsertId();
        
        // إضافة عناصر للطلب الثاني
        $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
        $stmt->execute([$order2_id, 3, 1, 8000]); // كباب عراقي × 1
        $stmt->execute([$order2_id, 4, 1, 6000]); // دولمة عراقية × 1
        
        // طلب آخر معلق
        $stmt = $pdo->prepare("INSERT INTO orders (table_id, user_id, total_amount, status, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([5, $_SESSION['user_id'], 9000, 'pending']);
        $order3_id = $pdo->lastInsertId();
        
        // إضافة عناصر للطلب الثالث
        $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
        $stmt->execute([$order3_id, 1, 3, 1000]); // شاي عراقي × 3
        $stmt->execute([$order3_id, 4, 1, 6000]); // دولمة عراقية × 1
        
        // تحديث حالة الطاولات
        $stmt = $pdo->prepare("UPDATE tables SET status = 'occupied' WHERE id IN (1, 3, 5)");
        $stmt->execute();
        
        $pdo->commit();
        
        showMessage('تم إضافة الطلبات التجريبية بنجاح!', 'success');
        
    } catch (Exception $e) {
        $pdo->rollBack();
        showMessage('حدث خطأ في إضافة الطلبات: ' . $e->getMessage(), 'error');
    }
}

// حذف جميع الطلبات التجريبية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['clear_test_orders'])) {
    try {
        $pdo->beginTransaction();
        
        // حذف عناصر الطلبات
        $stmt = $pdo->prepare("DELETE FROM order_items WHERE order_id IN (SELECT id FROM orders WHERE created_at >= CURDATE())");
        $stmt->execute();
        
        // حذف الطلبات
        $stmt = $pdo->prepare("DELETE FROM orders WHERE created_at >= CURDATE()");
        $stmt->execute();
        
        // تحرير جميع الطاولات
        $stmt = $pdo->prepare("UPDATE tables SET status = 'available'");
        $stmt->execute();
        
        $pdo->commit();
        
        showMessage('تم حذف جميع الطلبات التجريبية!', 'success');
        
    } catch (Exception $e) {
        $pdo->rollBack();
        showMessage('حدث خطأ في حذف الطلبات: ' . $e->getMessage(), 'error');
    }
}

// جلب الطلبات الحالية
$stmt = $pdo->prepare("
    SELECT o.*, t.table_number, u.username 
    FROM orders o 
    LEFT JOIN tables t ON o.table_id = t.id 
    LEFT JOIN users u ON o.user_id = u.id 
    WHERE DATE(o.created_at) = CURDATE()
    ORDER BY o.created_at DESC
");
$stmt->execute();
$orders = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-flask me-2"></i>إضافة طلبات تجريبية</h2>
    <div>
        <a href="test_complete_order.php" class="btn btn-info me-2">
            <i class="fas fa-vial me-2"></i>اختبار إكمال الطلبات
        </a>
        <a href="orders.php" class="btn btn-primary me-2">
            <i class="fas fa-list me-2"></i>عرض الطلبات
        </a>
        <a href="dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>لوحة التحكم
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة طلبات تجريبية
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>ما سيتم إضافته:</h6>
                    <ul class="mb-0">
                        <li><strong>طلب معلق</strong> - طاولة 1 (شاي × 2، قهوة × 1، حمص × 1)</li>
                        <li><strong>طلب قيد التحضير</strong> - طاولة 3 (كباب × 1، دولمة × 1)</li>
                        <li><strong>طلب معلق آخر</strong> - طاولة 5 (شاي × 3، دولمة × 1)</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <div class="d-grid gap-2">
                        <button type="submit" name="add_test_orders" class="btn btn-success btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إضافة الطلبات التجريبية
                        </button>
                        
                        <button type="submit" name="clear_test_orders" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            حذف جميع الطلبات التجريبية
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- الطلبات الحالية -->
        <?php if (!empty($orders)): ?>
            <div class="card mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        الطلبات الحالية (<?php echo count($orders); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>الطاولة</th>
                                    <th>الإجمالي</th>
                                    <th>الحالة</th>
                                    <th>الوقت</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td>#<?php echo $order['id']; ?></td>
                                        <td><?php echo $order['table_number'] ?? 'طاولة ' . $order['table_id']; ?></td>
                                        <td><?php echo number_format($order['total_amount']); ?> د.ع</td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'pending' => 'warning',
                                                'processing' => 'info',
                                                'completed' => 'success',
                                                'cancelled' => 'danger'
                                            ];
                                            $status_text = [
                                                'pending' => 'معلق',
                                                'processing' => 'قيد التحضير',
                                                'completed' => 'مكتمل',
                                                'cancelled' => 'ملغي'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_classes[$order['status']] ?? 'secondary'; ?>">
                                                <?php echo $status_text[$order['status']] ?? $order['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('H:i', strtotime($order['created_at'])); ?></td>
                                        <td>
                                            <?php if ($order['status'] == 'pending'): ?>
                                                <button class="btn btn-sm btn-success" onclick="testStartProcessing(<?php echo $order['id']; ?>)">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            <?php endif; ?>
                                            
                                            <?php if ($order['status'] == 'processing'): ?>
                                                <button class="btn btn-sm btn-info" onclick="testCompleteOrder(<?php echo $order['id']; ?>)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                            
                                            <a href="orders.php" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="card mt-4">
                <div class="card-body text-center">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد طلبات اليوم</h5>
                    <p class="text-muted">أضف طلبات تجريبية لاختبار النظام</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    خطوات الاختبار
                </h6>
            </div>
            <div class="card-body">
                <ol class="small">
                    <li><strong>أضف الطلبات التجريبية</strong> باستخدام الزر أعلاه</li>
                    <li><strong>اذهب لصفحة الطلبات</strong> لرؤية الطلبات</li>
                    <li><strong>اختبر زر "بدء التحضير"</strong> للطلبات المعلقة</li>
                    <li><strong>اختبر زر "إكمال الطلب"</strong> للطلبات قيد التحضير</li>
                    <li><strong>تحقق من تحديث الحالة</strong> بعد كل عملية</li>
                </ol>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">روابط سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="orders.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-list me-2"></i>عرض الطلبات
                    </a>
                    <a href="test_complete_order.php" class="btn btn-success btn-sm">
                        <i class="fas fa-vial me-2"></i>اختبار إكمال الطلبات
                    </a>
                    <a href="add_order.php?table_id=1" class="btn btn-info btn-sm">
                        <i class="fas fa-plus me-2"></i>إضافة طلب جديد
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0">معلومات النظام</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span>الطلبات اليوم:</span>
                        <strong><?php echo count($orders); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>المعلقة:</span>
                        <strong><?php echo count(array_filter($orders, fn($o) => $o['status'] == 'pending')); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>قيد التحضير:</span>
                        <strong><?php echo count(array_filter($orders, fn($o) => $o['status'] == 'processing')); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>المكتملة:</span>
                        <strong><?php echo count(array_filter($orders, fn($o) => $o['status'] == 'completed')); ?></strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// نسخ دوال الاختبار
function testStartProcessing(orderId) {
    if (confirm('هل تريد بدء تحضير هذا الطلب؟')) {
        testUpdateOrderStatus(orderId, 'processing');
    }
}

function testCompleteOrder(orderId) {
    if (confirm('هل تم الانتهاء من تحضير هذا الطلب؟')) {
        testUpdateOrderStatus(orderId, 'completed');
    }
}

function testUpdateOrderStatus(orderId, newStatus) {
    const data = {
        order_id: orderId,
        status: newStatus
    };
    
    fetch('ajax/update_order_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تحديث حالة الطلب بنجاح: ' + data.status_text);
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 صفحة إضافة طلبات تجريبية جاهزة');
    console.log('📋 عدد الطلبات الحالية: <?php echo count($orders); ?>');
});
</script>

<?php include 'includes/footer.php'; ?>
