<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('../login.php');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = trim($_POST['name']);
    
    if (empty($name)) {
        showMessage('يرجى إدخال اسم الفئة', 'error');
        redirect('../products.php');
    }
    
    try {
        // التحقق من عدم وجود فئة بنفس الاسم
        $stmt = $pdo->prepare("SELECT id FROM categories WHERE name = ?");
        $stmt->execute([$name]);
        
        if ($stmt->fetch()) {
            showMessage('اسم الفئة موجود بالفعل', 'error');
            redirect('../products.php');
        }
        
        // إضافة الفئة الجديدة
        $stmt = $pdo->prepare("INSERT INTO categories (name) VALUES (?)");
        $stmt->execute([$name]);
        
        showMessage('تم إضافة الفئة بنجاح', 'success');
        
    } catch (PDOException $e) {
        showMessage('حدث خطأ في إضافة الفئة', 'error');
    }
}

redirect('../products.php');
?>
