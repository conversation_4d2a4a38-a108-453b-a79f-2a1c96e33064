<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('../login.php');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $table_number = trim($_POST['table_number']);
    $capacity = intval($_POST['capacity']);
    
    if (empty($table_number) || $capacity < 1) {
        showMessage('يرجى إدخال بيانات صحيحة', 'error');
        redirect('../tables.php');
    }
    
    try {
        // التحقق من عدم وجود طاولة بنفس الرقم
        $stmt = $pdo->prepare("SELECT id FROM tables WHERE table_number = ?");
        $stmt->execute([$table_number]);
        
        if ($stmt->fetch()) {
            showMessage('رقم الطاولة موجود بالفعل', 'error');
            redirect('../tables.php');
        }
        
        // إضافة الطاولة الجديدة
        $stmt = $pdo->prepare("INSERT INTO tables (table_number, capacity) VALUES (?, ?)");
        $stmt->execute([$table_number, $capacity]);
        
        showMessage('تم إضافة الطاولة بنجاح', 'success');
        
    } catch (PDOException $e) {
        showMessage('حدث خطأ في إضافة الطاولة', 'error');
    }
}

redirect('../tables.php');
?>
