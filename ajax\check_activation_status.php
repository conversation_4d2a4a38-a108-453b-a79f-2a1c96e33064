<?php
/**
 * Check Activation Status API
 * فحص حالة التفعيل
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../config/database.php';

try {
    // التحقق من تسجيل الدخول
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً');
    }
    
    // فحص وجود جداول النظام
    $tables_exist = checkSystemTables();
    
    if (!$tables_exist) {
        echo json_encode([
            'success' => true,
            'status' => 'not_setup',
            'message' => 'النظام غير مُعد - يحتاج إعداد أولي',
            'can_use' => false,
            'setup_required' => true
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // جلب إعدادات النظام
    $settings = getSystemSettings();
    
    if (empty($settings)) {
        echo json_encode([
            'success' => true,
            'status' => 'not_setup',
            'message' => 'إعدادات النظام غير موجودة',
            'can_use' => false,
            'setup_required' => true
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // فحص حالة التفعيل
    $activation_status = checkActivationStatus($settings);
    
    echo json_encode($activation_status, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'status' => 'error',
        'message' => $e->getMessage(),
        'can_use' => false
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * فحص وجود جداول النظام
 */
function checkSystemTables() {
    global $pdo;
    
    try {
        $required_tables = [
            'system_settings',
            'activation_licenses',
            'system_usage',
            'activation_log'
        ];
        
        foreach ($required_tables as $table) {
            $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            
            if (!$stmt->fetch()) {
                return false;
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * جلب إعدادات النظام
 */
function getSystemSettings() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM system_settings");
        $stmt->execute();
        
        $settings = [];
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
        
    } catch (Exception $e) {
        return [];
    }
}

/**
 * فحص حالة التفعيل
 */
function checkActivationStatus($settings) {
    global $pdo;
    
    $status = $settings['system_status'] ?? 'not_setup';
    $current_orders = intval($settings['current_orders_count'] ?? 0);
    $max_orders = intval($settings['trial_orders_limit'] ?? 10);
    $license_key = $settings['license_key'] ?? null;
    
    // إحصائيات إضافية
    $usage_stats = getUsageStats();
    
    switch ($status) {
        case 'trial':
            return checkTrialStatus($current_orders, $max_orders, $license_key, $usage_stats);
            
        case 'active':
            return checkActiveStatus($license_key, $usage_stats);
            
        case 'expired':
            return [
                'success' => true,
                'status' => 'expired',
                'message' => 'انتهت صلاحية النظام',
                'can_use' => false,
                'current_orders' => $current_orders,
                'max_orders' => $max_orders,
                'usage_stats' => $usage_stats
            ];
            
        default:
            return [
                'success' => true,
                'status' => 'not_setup',
                'message' => 'النظام غير مُعد',
                'can_use' => false,
                'setup_required' => true
            ];
    }
}

/**
 * فحص حالة النسخة التجريبية
 */
function checkTrialStatus($current_orders, $max_orders, $license_key, $usage_stats) {
    global $pdo;
    
    // فحص عدد الطلبات
    if ($current_orders >= $max_orders) {
        return [
            'success' => true,
            'status' => 'trial_expired',
            'message' => "تم استنفاد الطلبات التجريبية ({$current_orders}/{$max_orders})",
            'can_use' => false,
            'current_orders' => $current_orders,
            'max_orders' => $max_orders,
            'remaining_orders' => 0,
            'usage_stats' => $usage_stats,
            'upgrade_required' => true
        ];
    }
    
    // فحص تاريخ انتهاء التجربة
    if ($license_key) {
        try {
            $stmt = $pdo->prepare("SELECT expires_at FROM activation_licenses WHERE license_key = ?");
            $stmt->execute([$license_key]);
            $license = $stmt->fetch();
            
            if ($license && $license['expires_at'] && strtotime($license['expires_at']) < time()) {
                return [
                    'success' => true,
                    'status' => 'trial_expired',
                    'message' => 'انتهت مدة التجربة المجانية',
                    'can_use' => false,
                    'current_orders' => $current_orders,
                    'max_orders' => $max_orders,
                    'remaining_orders' => $max_orders - $current_orders,
                    'expired_at' => $license['expires_at'],
                    'usage_stats' => $usage_stats,
                    'upgrade_required' => true
                ];
            }
        } catch (Exception $e) {
            // تجاهل الخطأ واستمر
        }
    }
    
    $remaining_orders = $max_orders - $current_orders;
    
    return [
        'success' => true,
        'status' => 'trial_active',
        'message' => "النسخة التجريبية نشطة ({$current_orders}/{$max_orders})",
        'can_use' => true,
        'current_orders' => $current_orders,
        'max_orders' => $max_orders,
        'remaining_orders' => $remaining_orders,
        'is_trial' => true,
        'usage_stats' => $usage_stats,
        'warning_level' => getWarningLevel($remaining_orders)
    ];
}

/**
 * فحص حالة النسخة المفعلة
 */
function checkActiveStatus($license_key, $usage_stats) {
    global $pdo;
    
    if (!$license_key) {
        return [
            'success' => true,
            'status' => 'no_license',
            'message' => 'لا يوجد ترخيص نشط',
            'can_use' => false,
            'usage_stats' => $usage_stats
        ];
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM activation_licenses 
            WHERE license_key = ? AND is_active = 1
        ");
        $stmt->execute([$license_key]);
        $license = $stmt->fetch();
        
        if (!$license) {
            return [
                'success' => true,
                'status' => 'invalid_license',
                'message' => 'ترخيص غير صحيح',
                'can_use' => false,
                'usage_stats' => $usage_stats
            ];
        }
        
        // فحص انتهاء الصلاحية
        if ($license['expires_at'] && strtotime($license['expires_at']) < time()) {
            return [
                'success' => true,
                'status' => 'license_expired',
                'message' => 'انتهت صلاحية الترخيص',
                'can_use' => false,
                'expired_at' => $license['expires_at'],
                'usage_stats' => $usage_stats,
                'renewal_required' => true
            ];
        }
        
        return [
            'success' => true,
            'status' => 'active',
            'message' => 'النظام مفعل بالكامل',
            'can_use' => true,
            'license_type' => $license['license_type'],
            'max_orders' => $license['max_orders'],
            'expires_at' => $license['expires_at'],
            'activated_at' => $license['activated_at'],
            'usage_stats' => $usage_stats,
            'unlimited' => $license['max_orders'] == 0
        ];
        
    } catch (Exception $e) {
        return [
            'success' => true,
            'status' => 'check_error',
            'message' => 'خطأ في فحص الترخيص',
            'can_use' => false,
            'error' => $e->getMessage(),
            'usage_stats' => $usage_stats
        ];
    }
}

/**
 * جلب إحصائيات الاستخدام
 */
function getUsageStats() {
    global $pdo;
    
    try {
        $stats = [];
        
        // إجمالي الطلبات
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_usage WHERE usage_type = 'order'");
        $stmt->execute();
        $stats['total_orders'] = $stmt->fetchColumn();
        
        // طلبات اليوم
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM system_usage 
            WHERE usage_type = 'order' AND DATE(created_at) = CURDATE()
        ");
        $stmt->execute();
        $stats['today_orders'] = $stmt->fetchColumn();
        
        // طلبات الأسبوع
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM system_usage 
            WHERE usage_type = 'order' AND created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        ");
        $stmt->execute();
        $stats['weekly_orders'] = $stmt->fetchColumn();
        
        // إجمالي المستخدمين
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users");
        $stmt->execute();
        $stats['total_users'] = $stmt->fetchColumn();
        
        // آخر نشاط
        $stmt = $pdo->prepare("
            SELECT created_at FROM system_usage 
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute();
        $last_activity = $stmt->fetchColumn();
        $stats['last_activity'] = $last_activity;
        
        return $stats;
        
    } catch (Exception $e) {
        return [
            'total_orders' => 0,
            'today_orders' => 0,
            'weekly_orders' => 0,
            'total_users' => 0,
            'last_activity' => null,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * تحديد مستوى التحذير
 */
function getWarningLevel($remaining_orders) {
    if ($remaining_orders <= 1) {
        return 'critical'; // أحمر
    } elseif ($remaining_orders <= 3) {
        return 'high'; // برتقالي
    } elseif ($remaining_orders <= 5) {
        return 'medium'; // أصفر
    } else {
        return 'low'; // أخضر
    }
}
?>
