<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['order_id'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$order_id = intval($input['order_id']);
$payment_method = $input['payment_method'] ?? 'cash';
$received_amount = floatval($input['received_amount'] ?? 0);
$change_amount = floatval($input['change_amount'] ?? 0);

try {
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // التحقق من وجود الطلب
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('الطلب غير موجود');
    }
    
    if ($order['status'] == 'completed') {
        throw new Exception('الطلب مكتمل بالفعل');
    }
    
    // التحقق من المبلغ المستلم
    if ($received_amount < $order['total_amount']) {
        throw new Exception('المبلغ المستلم أقل من إجمالي الطلب');
    }
    
    // تحديث حالة الطلب
    $stmt = $pdo->prepare("UPDATE orders SET status = 'completed' WHERE id = ?");
    $stmt->execute([$order_id]);
    
    // تحديث حالة جميع عناصر الطلب
    $stmt = $pdo->prepare("UPDATE order_items SET status = 'served' WHERE order_id = ?");
    $stmt->execute([$order_id]);
    
    // تحديث حالة الطاولة
    $stmt = $pdo->prepare("UPDATE tables SET status = 'available' WHERE id = ?");
    $stmt->execute([$order['table_id']]);
    
    // إدخال بيانات الدفع (يمكن إضافة جدول للمدفوعات لاحقاً)
    // هنا يمكن إضافة سجل في جدول المدفوعات إذا كان موجوداً
    
    // تأكيد المعاملة
    $pdo->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم إتمام الطلب بنجاح',
        'order_id' => $order_id,
        'total_amount' => $order['total_amount'],
        'received_amount' => $received_amount,
        'change_amount' => $change_amount
    ]);
    
} catch (Exception $e) {
    // إلغاء المعاملة
    $pdo->rollBack();
    
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
