<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['table_id']) || !isset($input['items']) || empty($input['items'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$table_id = intval($input['table_id']);
$items = $input['items'];

try {
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // التحقق من أن الطاولة متاحة
    $stmt = $pdo->prepare("SELECT * FROM tables WHERE id = ? AND status = 'available'");
    $stmt->execute([$table_id]);
    $table = $stmt->fetch();
    
    if (!$table) {
        throw new Exception('الطاولة غير متاحة');
    }
    
    // حساب إجمالي الطلب
    $total_amount = 0;
    $valid_items = [];
    
    foreach ($items as $item) {
        $product_id = intval($item['id']);
        $quantity = intval($item['quantity']);
        
        if ($quantity < 1) continue;
        
        // جلب بيانات المنتج
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch();
        
        if (!$product) continue;
        
        $item_total = $product['price'] * $quantity;
        $total_amount += $item_total;
        
        $valid_items[] = [
            'product_id' => $product_id,
            'quantity' => $quantity,
            'price' => $product['price']
        ];
    }
    
    if (empty($valid_items)) {
        throw new Exception('لا توجد منتجات صحيحة في الطلب');
    }
    
    // إنشاء الطلب
    $stmt = $pdo->prepare("INSERT INTO orders (table_id, user_id, total_amount, status) VALUES (?, ?, ?, 'pending')");
    $stmt->execute([$table_id, $_SESSION['user_id'], $total_amount]);
    $order_id = $pdo->lastInsertId();
    
    // إضافة عناصر الطلب
    $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    
    foreach ($valid_items as $item) {
        $stmt->execute([
            $order_id,
            $item['product_id'],
            $item['quantity'],
            $item['price']
        ]);
    }
    
    // تحديث حالة الطاولة
    $stmt = $pdo->prepare("UPDATE tables SET status = 'occupied' WHERE id = ?");
    $stmt->execute([$table_id]);
    
    // تأكيد المعاملة
    $pdo->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم إنشاء الطلب بنجاح',
        'order_id' => $order_id
    ]);
    
} catch (Exception $e) {
    // إلغاء المعاملة
    $pdo->rollBack();
    
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
