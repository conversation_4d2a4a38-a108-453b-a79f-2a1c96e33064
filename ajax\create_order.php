<?php
require_once '../config/database.php';
require_once '../includes/printer_functions.php';

// إعداد headers للاستجابة JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تسجيل الطلب للتشخيص
error_log('Create Order Request: ' . file_get_contents('php://input'));

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['table_id']) || !isset($input['items']) || empty($input['items'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$table_id = intval($input['table_id']);
$items = $input['items'];

try {
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // التحقق من أن الطاولة متاحة
    $stmt = $pdo->prepare("SELECT * FROM tables WHERE id = ? AND status = 'available'");
    $stmt->execute([$table_id]);
    $table = $stmt->fetch();
    
    if (!$table) {
        throw new Exception('الطاولة غير متاحة');
    }
    
    // حساب إجمالي الطلب
    $total_amount = 0;
    $valid_items = [];
    
    foreach ($items as $item) {
        $product_id = intval($item['id']);
        $quantity = intval($item['quantity']);
        
        if ($quantity < 1) continue;
        
        // جلب بيانات المنتج
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch();
        
        if (!$product) continue;
        
        $item_total = $product['price'] * $quantity;
        $total_amount += $item_total;
        
        $valid_items[] = [
            'product_id' => $product_id,
            'quantity' => $quantity,
            'price' => $product['price']
        ];
    }
    
    if (empty($valid_items)) {
        throw new Exception('لا توجد منتجات صحيحة في الطلب');
    }
    
    // إنشاء الطلب
    $stmt = $pdo->prepare("INSERT INTO orders (table_id, user_id, total_amount, status) VALUES (?, ?, ?, 'pending')");
    $stmt->execute([$table_id, $_SESSION['user_id'], $total_amount]);
    $order_id = $pdo->lastInsertId();
    
    // إضافة عناصر الطلب
    $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
    
    foreach ($valid_items as $item) {
        $stmt->execute([
            $order_id,
            $item['product_id'],
            $item['quantity'],
            $item['price']
        ]);
    }
    
    // تحديث حالة الطاولة
    $stmt = $pdo->prepare("UPDATE tables SET status = 'occupied' WHERE id = ?");
    $stmt->execute([$table_id]);
    
    // تأكيد المعاملة
    $pdo->commit();

    // إعداد بيانات الطباعة
    $print_data = [
        'order_id' => $order_id,
        'table_number' => 'طاولة ' . $table_id,
        'user_name' => $_SESSION['user_name'] ?? 'غير محدد',
        'created_at' => date('Y-m-d H:i:s'),
        'total_amount' => $total_amount,
        'items' => []
    ];

    // جلب تفاصيل المنتجات للطباعة
    foreach ($valid_items as $item) {
        $stmt = $pdo->prepare("SELECT name FROM products WHERE id = ?");
        $stmt->execute([$item['product_id']]);
        $product = $stmt->fetch();

        $print_data['items'][] = [
            'id' => $item['product_id'],
            'name' => $product['name'] ?? 'منتج غير محدد',
            'quantity' => $item['quantity'],
            'price' => $item['price'],
            'notes' => '' // يمكن إضافة الملاحظات لاحقاً
        ];
    }

    // محاولة الطباعة التلقائية
    $print_success = false;
    $print_message = '';

    try {
        // قراءة إعدادات الطابعة
        $settings_file = '../config/printer_settings.json';
        if (file_exists($settings_file)) {
            $settings = json_decode(file_get_contents($settings_file), true);

            // طباعة للمطبخ إذا كانت مفعلة
            if ($settings['kitchen_printer']['enabled']) {
                $kitchen_printer = new RestaurantPrinter(
                    $settings['kitchen_printer']['ip'],
                    $settings['kitchen_printer']['port'],
                    'kitchen'
                );
                $kitchen_result = $kitchen_printer->printKitchenOrder($print_data);

                if ($kitchen_result) {
                    $print_message .= 'تم طباعة طلب المطبخ. ';
                    $print_success = true;
                }
            }

            // طباعة للكاشير إذا كانت مفعلة
            if ($settings['cashier_printer']['enabled']) {
                $cashier_printer = new RestaurantPrinter(
                    $settings['cashier_printer']['ip'],
                    $settings['cashier_printer']['port'],
                    'cashier'
                );
                $cashier_result = $cashier_printer->printCashierReceipt($print_data);

                if ($cashier_result) {
                    $print_message .= 'تم طباعة فاتورة الكاشير. ';
                    $print_success = true;
                }
            }
        }

    } catch (Exception $print_error) {
        // تسجيل خطأ الطباعة دون إيقاف العملية
        error_log('خطأ في الطباعة التلقائية: ' . $print_error->getMessage());
        $print_message = 'تم إنشاء الطلب لكن فشلت الطباعة التلقائية';
    }

    // إرسال الاستجابة
    $response = [
        'success' => true,
        'message' => 'تم إنشاء الطلب بنجاح',
        'order_id' => $order_id,
        'print_success' => $print_success,
        'print_message' => $print_message
    ];

    echo json_encode($response);
    
} catch (Exception $e) {
    // إلغاء المعاملة
    $pdo->rollBack();
    
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
