<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('../login.php');
}

if (isset($_GET['id'])) {
    $table_id = intval($_GET['id']);
    
    if ($table_id < 1) {
        showMessage('معرف الطاولة غير صحيح', 'error');
        redirect('../tables.php');
    }
    
    try {
        // التحقق من عدم وجود طلبات نشطة على الطاولة
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE table_id = ? AND status IN ('pending', 'processing')");
        $stmt->execute([$table_id]);
        $active_orders = $stmt->fetchColumn();
        
        if ($active_orders > 0) {
            showMessage('لا يمكن حذف الطاولة لوجود طلبات نشطة عليها', 'error');
            redirect('../tables.php');
        }
        
        // حذف الطاولة
        $stmt = $pdo->prepare("DELETE FROM tables WHERE id = ?");
        $stmt->execute([$table_id]);
        
        if ($stmt->rowCount() > 0) {
            showMessage('تم حذف الطاولة بنجاح', 'success');
        } else {
            showMessage('الطاولة غير موجودة', 'error');
        }
        
    } catch (PDOException $e) {
        showMessage('حدث خطأ في حذف الطاولة', 'error');
    }
}

redirect('../tables.php');
?>
