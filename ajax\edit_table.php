<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('../login.php');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $table_id = intval($_POST['table_id']);
    $table_number = trim($_POST['table_number']);
    $capacity = intval($_POST['capacity']);
    
    if ($table_id < 1 || empty($table_number) || $capacity < 1) {
        showMessage('يرجى إدخال بيانات صحيحة', 'error');
        redirect('../tables.php');
    }
    
    try {
        // التحقق من عدم وجود طاولة أخرى بنفس الرقم
        $stmt = $pdo->prepare("SELECT id FROM tables WHERE table_number = ? AND id != ?");
        $stmt->execute([$table_number, $table_id]);
        
        if ($stmt->fetch()) {
            showMessage('رقم الطاولة موجود بالفعل', 'error');
            redirect('../tables.php');
        }
        
        // تحديث بيانات الطاولة
        $stmt = $pdo->prepare("UPDATE tables SET table_number = ?, capacity = ? WHERE id = ?");
        $stmt->execute([$table_number, $capacity, $table_id]);
        
        showMessage('تم تحديث بيانات الطاولة بنجاح', 'success');
        
    } catch (PDOException $e) {
        showMessage('حدث خطأ في تحديث الطاولة', 'error');
    }
}

redirect('../tables.php');
?>
