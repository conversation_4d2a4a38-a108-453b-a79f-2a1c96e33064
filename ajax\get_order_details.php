<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    echo '<div class="alert alert-danger">غير مصرح لك بهذا الإجراء</div>';
    exit;
}

$order_id = intval($_GET['order_id'] ?? 0);

if ($order_id < 1) {
    echo '<div class="alert alert-danger">معرف الطلب غير صحيح</div>';
    exit;
}

try {
    // جلب بيانات الطلب
    $stmt = $pdo->prepare("
        SELECT o.*, t.table_number, u.full_name as user_name
        FROM orders o
        JOIN tables t ON o.table_id = t.id
        JOIN users u ON o.user_id = u.id
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo '<div class="alert alert-danger">الطلب غير موجود</div>';
        exit;
    }
    
    // جلب عناصر الطلب
    $stmt = $pdo->prepare("
        SELECT oi.*, p.name as product_name, p.description
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?
        ORDER BY oi.id
    ");
    $stmt->execute([$order_id]);
    $items = $stmt->fetchAll();
    
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">حدث خطأ في جلب البيانات</div>';
    exit;
}

$status_text = [
    'pending' => 'معلق',
    'processing' => 'قيد التحضير',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي'
];

$item_status_text = [
    'pending' => 'معلق',
    'preparing' => 'قيد التحضير',
    'ready' => 'جاهز',
    'served' => 'تم التقديم'
];
?>

<div class="row mb-3">
    <div class="col-md-6">
        <h6>معلومات الطلب</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>رقم الطلب:</strong></td>
                <td>#<?php echo $order['id']; ?></td>
            </tr>
            <tr>
                <td><strong>الطاولة:</strong></td>
                <td><?php echo $order['table_number']; ?></td>
            </tr>
            <tr>
                <td><strong>الموظف:</strong></td>
                <td><?php echo $order['user_name']; ?></td>
            </tr>
            <tr>
                <td><strong>الوقت:</strong></td>
                <td><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></td>
            </tr>
            <tr>
                <td><strong>الحالة:</strong></td>
                <td>
                    <span class="badge bg-<?php 
                        echo $order['status'] == 'completed' ? 'success' : 
                            ($order['status'] == 'processing' ? 'warning' : 'secondary'); 
                    ?>">
                        <?php echo $status_text[$order['status']]; ?>
                    </span>
                </td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <h6>ملخص الطلب</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>عدد العناصر:</strong></td>
                <td><?php echo count($items); ?></td>
            </tr>
            <tr>
                <td><strong>الإجمالي:</strong></td>
                <td><strong class="text-success"><?php echo formatPrice($order['total_amount']); ?></strong></td>
            </tr>
        </table>
    </div>
</div>

<h6>تفاصيل العناصر</h6>
<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>المنتج</th>
                <th>الوصف</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>الإجمالي</th>
                <th>الحالة</th>
                <th>الملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($items as $item): ?>
                <tr>
                    <td><?php echo $item['product_name']; ?></td>
                    <td><?php echo $item['description'] ?: '-'; ?></td>
                    <td><?php echo $item['quantity']; ?></td>
                    <td><?php echo formatPrice($item['price']); ?></td>
                    <td><?php echo formatPrice($item['price'] * $item['quantity']); ?></td>
                    <td>
                        <span class="badge bg-<?php 
                            echo $item['status'] == 'served' ? 'success' : 
                                ($item['status'] == 'ready' ? 'info' : 
                                ($item['status'] == 'preparing' ? 'warning' : 'secondary')); 
                        ?>">
                            <?php echo $item_status_text[$item['status']]; ?>
                        </span>
                    </td>
                    <td><?php echo $item['notes'] ?: '-'; ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot>
            <tr class="table-success">
                <th colspan="4">الإجمالي النهائي</th>
                <th><?php echo formatPrice($order['total_amount']); ?></th>
                <th colspan="2"></th>
            </tr>
        </tfoot>
    </table>
</div>
