<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

$order_id = intval($_GET['order_id'] ?? 0);

if ($order_id < 1) {
    echo json_encode(['success' => false, 'message' => 'معرف الطلب غير صحيح']);
    exit;
}

try {
    // جلب بيانات الطلب
    $stmt = $pdo->prepare("
        SELECT o.*, t.table_number
        FROM orders o
        JOIN tables t ON o.table_id = t.id
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo json_encode(['success' => false, 'message' => 'الطلب غير موجود']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'order_id' => $order['id'],
        'table_number' => $order['table_number'],
        'total' => $order['total_amount'],
        'total_formatted' => formatPrice($order['total_amount'])
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في جلب بيانات الطلب'
    ]);
}
?>
