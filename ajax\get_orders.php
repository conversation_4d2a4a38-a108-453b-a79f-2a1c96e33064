<?php
/**
 * Get Orders API
 * جلب الطلبات مع تفاصيل كاملة
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../config/database.php';

try {
    // التحقق من تسجيل الدخول
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً');
    }
    
    // جلب الطلبات مع تفاصيل كاملة
    $stmt = $pdo->prepare("
        SELECT 
            o.id,
            o.table_id,
            o.total_amount,
            o.status,
            o.created_at,
            o.updated_at,
            t.table_number,
            u.username as user_name,
            COUNT(oi.id) as items_count,
            GROUP_CONCAT(CONCAT(p.name, ' (', oi.quantity, ')') SEPARATOR ', ') as items_summary
        FROM orders o
        LEFT JOIN tables t ON o.table_id = t.id
        LEFT JOIN users u ON o.user_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE DATE(o.created_at) = CURDATE()
        GROUP BY o.id
        ORDER BY 
            CASE o.status 
                WHEN 'pending' THEN 1 
                WHEN 'processing' THEN 2 
                WHEN 'completed' THEN 3 
                WHEN 'cancelled' THEN 4 
            END,
            o.created_at DESC
    ");
    $stmt->execute();
    $orders = $stmt->fetchAll();
    
    // إحصائيات الطلبات
    $stats = [
        'pending' => 0,
        'processing' => 0,
        'completed' => 0,
        'cancelled' => 0,
        'total' => count($orders)
    ];
    
    foreach ($orders as $order) {
        $stats[$order['status']] = ($stats[$order['status']] ?? 0) + 1;
    }
    
    echo json_encode([
        'success' => true,
        'orders' => $orders,
        'statistics' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
