<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('kitchen')) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['order_id'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$order_id = intval($input['order_id']);

try {
    // التحقق من وجود الطلب
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo json_encode(['success' => false, 'message' => 'الطلب غير موجود']);
        exit;
    }
    
    // تحديث حالة جميع عناصر الطلب إلى جاهز
    $stmt = $pdo->prepare("UPDATE order_items SET status = 'ready' WHERE order_id = ? AND status != 'served'");
    $stmt->execute([$order_id]);
    
    // تحديث حالة الطلب إلى قيد التحضير (جاهز للتقديم)
    $stmt = $pdo->prepare("UPDATE orders SET status = 'processing' WHERE id = ?");
    $stmt->execute([$order_id]);
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم إشعار الكاشير بأن الطلب جاهز'
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في تحديث حالة الطلب'
    ]);
}
?>
