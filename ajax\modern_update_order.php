<?php
/**
 * Modern Order Update API
 * نظام تحديث الطلبات العصري مع معالجة شاملة للأخطاء
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../config/database.php';

// تسجيل الطلب للتشخيص
error_log('Modern Update Order - Request: ' . file_get_contents('php://input'));

try {
    // التحقق من تسجيل الدخول
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً');
    }
    
    // التحقق من الصلاحيات
    if (!hasRole('admin') && !hasRole('cashier')) {
        throw new Exception('ليس لديك صلاحية لتحديث الطلبات');
    }
    
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }
    
    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('بيانات JSON غير صحيحة');
    }
    
    // التحقق من البيانات المطلوبة
    if (!isset($data['order_id']) || !isset($data['status'])) {
        throw new Exception('بيانات مفقودة: order_id أو status');
    }
    
    $order_id = intval($data['order_id']);
    $new_status = trim($data['status']);
    
    // التحقق من صحة معرف الطلب
    if ($order_id <= 0) {
        throw new Exception('معرف الطلب غير صحيح');
    }
    
    // التحقق من صحة الحالة
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled'];
    if (!in_array($new_status, $valid_statuses)) {
        throw new Exception('حالة الطلب غير صحيحة');
    }
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // جلب بيانات الطلب الحالية
    $stmt = $pdo->prepare("
        SELECT o.*, t.table_number 
        FROM orders o 
        LEFT JOIN tables t ON o.table_id = t.id 
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('الطلب غير موجود');
    }
    
    // التحقق من إمكانية تغيير الحالة
    $current_status = $order['status'];
    
    // قواعد تغيير الحالة
    $status_transitions = [
        'pending' => ['processing', 'cancelled'],
        'processing' => ['completed', 'cancelled'],
        'completed' => [], // لا يمكن تغيير الطلب المكتمل
        'cancelled' => []  // لا يمكن تغيير الطلب الملغي
    ];
    
    if (!in_array($new_status, $status_transitions[$current_status])) {
        throw new Exception("لا يمكن تغيير حالة الطلب من '{$current_status}' إلى '{$new_status}'");
    }
    
    // تحديث حالة الطلب
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET status = ?, updated_at = NOW() 
        WHERE id = ?
    ");
    $stmt->execute([$new_status, $order_id]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('فشل في تحديث حالة الطلب');
    }
    
    // تحديث حالة الطاولة حسب حالة الطلب
    $table_status = 'occupied'; // افتراضي
    
    switch ($new_status) {
        case 'completed':
        case 'cancelled':
            // تحرير الطاولة
            $table_status = 'available';
            break;
        case 'processing':
        case 'pending':
            // الطاولة محجوزة
            $table_status = 'occupied';
            break;
    }
    
    $stmt = $pdo->prepare("UPDATE tables SET status = ? WHERE id = ?");
    $stmt->execute([$table_status, $order['table_id']]);
    
    // تحديث حالة عناصر الطلب
    $item_status = 'pending'; // افتراضي
    
    switch ($new_status) {
        case 'processing':
            $item_status = 'preparing';
            break;
        case 'completed':
            $item_status = 'served';
            break;
        case 'cancelled':
            $item_status = 'cancelled';
            break;
    }
    
    $stmt = $pdo->prepare("
        UPDATE order_items 
        SET status = ?, updated_at = NOW() 
        WHERE order_id = ?
    ");
    $stmt->execute([$item_status, $order_id]);
    
    // تسجيل العملية في سجل النشاطات
    $stmt = $pdo->prepare("
        INSERT INTO activity_log (user_id, action, description, created_at) 
        VALUES (?, ?, ?, NOW())
    ");
    $stmt->execute([
        $_SESSION['user_id'],
        'update_order_status',
        "تحديث حالة الطلب #{$order_id} من '{$current_status}' إلى '{$new_status}'"
    ]);
    
    // تأكيد المعاملة
    $pdo->commit();
    
    // ترجمة الحالات للعربية
    $status_translations = [
        'pending' => 'معلق',
        'processing' => 'قيد التحضير',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];
    
    $status_text = $status_translations[$new_status] ?? $new_status;
    
    // جلب بيانات الطلب المحدثة
    $stmt = $pdo->prepare("
        SELECT 
            o.*,
            t.table_number,
            u.username as user_name,
            COUNT(oi.id) as items_count,
            GROUP_CONCAT(CONCAT(p.name, ' (', oi.quantity, ')') SEPARATOR ', ') as items_summary
        FROM orders o
        LEFT JOIN tables t ON o.table_id = t.id
        LEFT JOIN users u ON o.user_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE o.id = ?
        GROUP BY o.id
    ");
    $stmt->execute([$order_id]);
    $updated_order = $stmt->fetch();
    
    // إرسال الاستجابة
    $response = [
        'success' => true,
        'message' => "تم تحديث حالة الطلب #{$order_id} إلى: {$status_text}",
        'data' => [
            'order_id' => $order_id,
            'old_status' => $current_status,
            'new_status' => $new_status,
            'status_text' => $status_text,
            'table_status' => $table_status,
            'updated_at' => date('Y-m-d H:i:s'),
            'order' => $updated_order
        ]
    ];
    
    // تسجيل النجاح
    error_log("Modern Update Order - Success: Order #{$order_id} updated to {$new_status}");
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // تسجيل الخطأ
    error_log('Modern Update Order - Error: ' . $e->getMessage());
    
    // إرسال رسالة الخطأ
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'UPDATE_ORDER_FAILED',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    // خطأ في قاعدة البيانات
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log('Modern Update Order - Database Error: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات',
        'error_code' => 'DATABASE_ERROR',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Throwable $e) {
    // أي خطأ آخر
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log('Modern Update Order - Unexpected Error: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ غير متوقع',
        'error_code' => 'UNEXPECTED_ERROR',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
