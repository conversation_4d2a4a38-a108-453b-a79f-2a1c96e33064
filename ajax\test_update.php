<?php
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// قراءة البيانات
$input = json_decode(file_get_contents("php://input"), true);
if (!$input) {
    $input = $_POST;
}

// تسجيل الطلب
error_log("AJAX Request: " . json_encode($input));

try {
    // محاكاة معالجة البيانات
    $response = [
        "success" => true,
        "message" => "تم التحديث بنجاح",
        "received_data" => $input,
        "timestamp" => date("Y-m-d H:i:s")
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}
?>