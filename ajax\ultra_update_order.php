<?php
/**
 * Ultra Modern Order Update API v2.0
 * نظام تحديث الطلبات الفائق مع تشخيص متقدم ومعالجة شاملة
 */

// Headers متقدمة
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');
header('X-Powered-By: Ultra Modern Orders System v2.0');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../config/database.php';

// تسجيل مفصل للطلب
$request_id = uniqid('req_');
$start_time = microtime(true);
$request_data = file_get_contents('php://input');

error_log("[$request_id] Ultra Update Order - Start: " . $request_data);

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة. يجب استخدام POST');
    }
    
    // التحقق من تسجيل الدخول
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً');
    }
    
    // التحقق من الصلاحيات
    if (!hasRole('admin') && !hasRole('cashier')) {
        throw new Exception('ليس لديك صلاحية لتحديث الطلبات');
    }
    
    // قراءة وتحليل البيانات
    if (empty($request_data)) {
        throw new Exception('لم يتم إرسال أي بيانات');
    }
    
    $data = json_decode($request_data, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('بيانات JSON غير صحيحة: ' . json_last_error_msg());
    }
    
    // التحقق من البيانات المطلوبة
    $required_fields = ['order_id', 'status'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            throw new Exception("حقل مطلوب مفقود: {$field}");
        }
    }
    
    $order_id = intval($data['order_id']);
    $new_status = trim($data['status']);
    $timestamp = $data['timestamp'] ?? time();
    $user_agent = $data['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    
    // التحقق من صحة معرف الطلب
    if ($order_id <= 0) {
        throw new Exception('معرف الطلب غير صحيح');
    }
    
    // التحقق من صحة الحالة
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled'];
    if (!in_array($new_status, $valid_statuses)) {
        throw new Exception("حالة الطلب غير صحيحة: {$new_status}");
    }
    
    error_log("[$request_id] Validation passed - Order: $order_id, Status: $new_status");
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // جلب بيانات الطلب الحالية مع قفل للقراءة
    $stmt = $pdo->prepare("
        SELECT o.*, t.table_number, u.username 
        FROM orders o 
        LEFT JOIN tables t ON o.table_id = t.id 
        LEFT JOIN users u ON o.user_id = u.id 
        WHERE o.id = ? 
        FOR UPDATE
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception("الطلب غير موجود: {$order_id}");
    }
    
    $current_status = $order['status'];
    
    // التحقق من إمكانية تغيير الحالة
    $status_transitions = [
        'pending' => ['processing', 'cancelled'],
        'processing' => ['completed', 'cancelled'],
        'completed' => [], // لا يمكن تغيير الطلب المكتمل
        'cancelled' => []  // لا يمكن تغيير الطلب الملغي
    ];
    
    if (!in_array($new_status, $status_transitions[$current_status])) {
        throw new Exception("لا يمكن تغيير حالة الطلب من '{$current_status}' إلى '{$new_status}'");
    }
    
    // تحديث حالة الطلب
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET status = ?, updated_at = NOW(), updated_by = ? 
        WHERE id = ?
    ");
    $result = $stmt->execute([$new_status, $_SESSION['user_id'], $order_id]);
    
    if (!$result || $stmt->rowCount() === 0) {
        throw new Exception('فشل في تحديث حالة الطلب في قاعدة البيانات');
    }
    
    error_log("[$request_id] Order status updated successfully");
    
    // تحديث حالة الطاولة
    $table_status = 'occupied'; // افتراضي
    
    switch ($new_status) {
        case 'completed':
        case 'cancelled':
            // تحرير الطاولة
            $table_status = 'available';
            break;
        case 'processing':
        case 'pending':
            // الطاولة محجوزة
            $table_status = 'occupied';
            break;
    }
    
    $stmt = $pdo->prepare("UPDATE tables SET status = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$table_status, $order['table_id']]);
    
    error_log("[$request_id] Table status updated to: $table_status");
    
    // تحديث حالة عناصر الطلب
    $item_status_map = [
        'pending' => 'pending',
        'processing' => 'preparing',
        'completed' => 'served',
        'cancelled' => 'cancelled'
    ];
    
    $item_status = $item_status_map[$new_status];
    
    $stmt = $pdo->prepare("
        UPDATE order_items 
        SET status = ?, updated_at = NOW() 
        WHERE order_id = ?
    ");
    $stmt->execute([$item_status, $order_id]);
    
    error_log("[$request_id] Order items status updated to: $item_status");
    
    // تسجيل العملية في سجل النشاطات
    $stmt = $pdo->prepare("
        INSERT INTO activity_log (
            user_id, action, description, 
            related_table, related_id, 
            ip_address, user_agent, 
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $description = "تحديث حالة الطلب #{$order_id} من '{$current_status}' إلى '{$new_status}'";
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    
    $stmt->execute([
        $_SESSION['user_id'],
        'update_order_status',
        $description,
        'orders',
        $order_id,
        $ip_address,
        $user_agent
    ]);
    
    // تأكيد المعاملة
    $pdo->commit();
    
    $processing_time = round((microtime(true) - $start_time) * 1000, 2);
    
    error_log("[$request_id] Transaction committed successfully in {$processing_time}ms");
    
    // ترجمة الحالات للعربية
    $status_translations = [
        'pending' => 'معلق',
        'processing' => 'قيد التحضير',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];
    
    $status_text = $status_translations[$new_status] ?? $new_status;
    
    // جلب بيانات الطلب المحدثة
    $stmt = $pdo->prepare("
        SELECT 
            o.*,
            t.table_number,
            u.username as user_name,
            COUNT(oi.id) as items_count,
            GROUP_CONCAT(CONCAT(p.name, ' (', oi.quantity, ')') SEPARATOR ', ') as items_summary
        FROM orders o
        LEFT JOIN tables t ON o.table_id = t.id
        LEFT JOIN users u ON o.user_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE o.id = ?
        GROUP BY o.id
    ");
    $stmt->execute([$order_id]);
    $updated_order = $stmt->fetch();
    
    // إعداد الاستجابة النهائية
    $response = [
        'success' => true,
        'message' => "تم تحديث حالة الطلب #{$order_id} إلى: {$status_text}",
        'data' => [
            'order_id' => $order_id,
            'old_status' => $current_status,
            'new_status' => $new_status,
            'status_text' => $status_text,
            'table_status' => $table_status,
            'processing_time_ms' => $processing_time,
            'timestamp' => date('Y-m-d H:i:s'),
            'order' => $updated_order
        ],
        'meta' => [
            'request_id' => $request_id,
            'api_version' => '2.0',
            'server_time' => date('c'),
            'user_id' => $_SESSION['user_id'],
            'user_name' => $_SESSION['user_name'] ?? 'Unknown'
        ]
    ];
    
    // إرسال الاستجابة
    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
    error_log("[$request_id] Success response sent");
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
        error_log("[$request_id] Transaction rolled back");
    }
    
    $processing_time = round((microtime(true) - $start_time) * 1000, 2);
    
    // تسجيل الخطأ
    error_log("[$request_id] Error after {$processing_time}ms: " . $e->getMessage());
    
    // تحديد نوع الخطأ ورمز HTTP المناسب
    $error_codes = [
        'يجب تسجيل الدخول' => 401,
        'ليس لديك صلاحية' => 403,
        'الطلب غير موجود' => 404,
        'طريقة الطلب غير مدعومة' => 405
    ];
    
    $http_code = 400; // افتراضي
    foreach ($error_codes as $pattern => $code) {
        if (strpos($e->getMessage(), $pattern) !== false) {
            $http_code = $code;
            break;
        }
    }
    
    // إرسال رسالة الخطأ
    http_response_code($http_code);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error' => [
            'code' => $http_code,
            'type' => 'UPDATE_ORDER_FAILED',
            'processing_time_ms' => $processing_time,
            'timestamp' => date('Y-m-d H:i:s')
        ],
        'meta' => [
            'request_id' => $request_id ?? uniqid('err_'),
            'api_version' => '2.0',
            'server_time' => date('c')
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // خطأ في قاعدة البيانات
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $processing_time = round((microtime(true) - $start_time) * 1000, 2);
    
    error_log("[$request_id] Database Error after {$processing_time}ms: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات',
        'error' => [
            'code' => 500,
            'type' => 'DATABASE_ERROR',
            'processing_time_ms' => $processing_time,
            'timestamp' => date('Y-m-d H:i:s')
        ],
        'meta' => [
            'request_id' => $request_id ?? uniqid('db_err_'),
            'api_version' => '2.0',
            'server_time' => date('c')
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Throwable $e) {
    // أي خطأ آخر غير متوقع
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $processing_time = round((microtime(true) - $start_time) * 1000, 2);
    
    error_log("[$request_id] Unexpected Error after {$processing_time}ms: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ غير متوقع في النظام',
        'error' => [
            'code' => 500,
            'type' => 'UNEXPECTED_ERROR',
            'processing_time_ms' => $processing_time,
            'timestamp' => date('Y-m-d H:i:s')
        ],
        'meta' => [
            'request_id' => $request_id ?? uniqid('fatal_err_'),
            'api_version' => '2.0',
            'server_time' => date('c')
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
