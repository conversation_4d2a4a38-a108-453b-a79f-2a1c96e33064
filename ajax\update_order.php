<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['order_id']) || !isset($input['items'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$order_id = intval($input['order_id']);
$items = $input['items'];

try {
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // التحقق من وجود الطلب وأنه قابل للتعديل
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ? AND status IN ('pending', 'processing')");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('الطلب غير موجود أو غير قابل للتعديل');
    }
    
    // جلب العناصر الحالية
    $stmt = $pdo->prepare("SELECT * FROM order_items WHERE order_id = ?");
    $stmt->execute([$order_id]);
    $current_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $current_items_by_id = array_column($current_items, null, 'id');
    
    $new_total = 0;
    $processed_items = [];
    
    foreach ($items as $item) {
        $item_id = $item['id'];
        $quantity = intval($item['quantity']);
        $price = floatval($item['price']);
        
        if ($quantity < 1) continue;
        
        // إذا كان العنصر جديد
        if (isset($item['is_new']) && $item['is_new']) {
            $product_id = intval($item['product_id']);
            
            // التحقق من صحة المنتج والسعر
            $stmt = $pdo->prepare("SELECT price FROM products WHERE id = ?");
            $stmt->execute([$product_id]);
            $product = $stmt->fetch();
            
            if (!$product) {
                throw new Exception('منتج غير صحيح');
            }
            
            // إضافة العنصر الجديد
            $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
            $stmt->execute([$order_id, $product_id, $quantity, $product['price']]);
            
            $new_total += $product['price'] * $quantity;
            
        } else {
            // تحديث عنصر موجود
            if (isset($current_items_by_id[$item_id])) {
                $stmt = $pdo->prepare("UPDATE order_items SET quantity = ? WHERE id = ?");
                $stmt->execute([$quantity, $item_id]);
                
                $new_total += $current_items_by_id[$item_id]['price'] * $quantity;
                $processed_items[] = $item_id;
            }
        }
    }
    
    // حذف العناصر التي لم تعد موجودة
    $items_to_remove = array_diff(array_keys($current_items_by_id), $processed_items);
    if (!empty($items_to_remove)) {
        $placeholders = str_repeat('?,', count($items_to_remove) - 1) . '?';
        $stmt = $pdo->prepare("DELETE FROM order_items WHERE id IN ($placeholders)");
        $stmt->execute($items_to_remove);
    }
    
    // تحديث إجمالي الطلب
    $stmt = $pdo->prepare("UPDATE orders SET total_amount = ? WHERE id = ?");
    $stmt->execute([$new_total, $order_id]);
    
    // تأكيد المعاملة
    $pdo->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم تحديث الطلب بنجاح',
        'new_total' => $new_total
    ]);
    
} catch (Exception $e) {
    // إلغاء المعاملة
    $pdo->rollBack();
    
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
