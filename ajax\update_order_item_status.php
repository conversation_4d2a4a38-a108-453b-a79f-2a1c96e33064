<?php
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('kitchen')) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['item_id']) || !isset($input['status'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$item_id = intval($input['item_id']);
$status = $input['status'];

// التحقق من صحة الحالة
$valid_statuses = ['pending', 'preparing', 'ready', 'served'];
if (!in_array($status, $valid_statuses)) {
    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة']);
    exit;
}

try {
    // التحقق من وجود العنصر
    $stmt = $pdo->prepare("SELECT * FROM order_items WHERE id = ?");
    $stmt->execute([$item_id]);
    $item = $stmt->fetch();
    
    if (!$item) {
        echo json_encode(['success' => false, 'message' => 'العنصر غير موجود']);
        exit;
    }
    
    // تحديث حالة العنصر
    $stmt = $pdo->prepare("UPDATE order_items SET status = ? WHERE id = ?");
    $stmt->execute([$status, $item_id]);
    
    // إذا كان العنصر جاهز، تحقق من جميع عناصر الطلب
    if ($status == 'ready') {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total_items,
                   SUM(CASE WHEN status = 'ready' THEN 1 ELSE 0 END) as ready_items
            FROM order_items 
            WHERE order_id = ?
        ");
        $stmt->execute([$item['order_id']]);
        $order_status = $stmt->fetch();
        
        // إذا كانت جميع العناصر جاهزة، تحديث حالة الطلب
        if ($order_status['total_items'] == $order_status['ready_items']) {
            $stmt = $pdo->prepare("UPDATE orders SET status = 'processing' WHERE id = ?");
            $stmt->execute([$item['order_id']]);
        }
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم تحديث حالة العنصر بنجاح'
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في تحديث حالة العنصر'
    ]);
}
?>
