<?php
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تسجيل الطلب للتشخيص
error_log('Update Order Status Request: ' . file_get_contents('php://input'));

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['order_id']) || !isset($input['status'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$order_id = intval($input['order_id']);
$status = $input['status'];

// التحقق من صحة الحالة
$valid_statuses = ['pending', 'processing', 'completed', 'cancelled'];
if (!in_array($status, $valid_statuses)) {
    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة']);
    exit;
}

try {
    // التحقق من وجود الطلب
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo json_encode(['success' => false, 'message' => 'الطلب غير موجود']);
        exit;
    }
    
    // تحديث حالة الطلب
    $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$status, $order_id]);

    // تحديث حالة الطاولة حسب حالة الطلب
    if ($status == 'completed' || $status == 'cancelled') {
        // تحرير الطاولة
        $stmt = $pdo->prepare("UPDATE tables SET status = 'available' WHERE id = ?");
        $stmt->execute([$order['table_id']]);
    } elseif ($status == 'processing') {
        // تأكيد أن الطاولة محجوزة
        $stmt = $pdo->prepare("UPDATE tables SET status = 'occupied' WHERE id = ?");
        $stmt->execute([$order['table_id']]);
    }

    // تحديث حالة عناصر الطلب
    if ($status == 'processing') {
        $stmt = $pdo->prepare("UPDATE order_items SET status = 'preparing' WHERE order_id = ? AND status = 'pending'");
        $stmt->execute([$order_id]);
    } elseif ($status == 'completed') {
        $stmt = $pdo->prepare("UPDATE order_items SET status = 'served' WHERE order_id = ?");
        $stmt->execute([$order_id]);
    }

    // ترجمة الحالات للعربية
    $status_text = [
        'pending' => 'معلق',
        'processing' => 'قيد التحضير',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];

    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث حالة الطلب إلى: ' . $status_text[$status],
        'new_status' => $status,
        'status_text' => $status_text[$status],
        'order_id' => $order_id
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في تحديث حالة الطلب'
    ]);
}
?>
