<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل المتصفح</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🔧 إصلاح مشاكل المتصفح للأزرار + و -</h1>
        
        <!-- تشخيص JavaScript -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">1. فحص JavaScript</h5>
            </div>
            <div class="card-body">
                <div id="js-status" class="alert alert-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    جاري فحص JavaScript...
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>اختبار الدوال الأساسية:</h6>
                        <ul id="js-functions" class="list-unstyled">
                            <li><i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>اختبار المكتبات:</h6>
                        <ul id="js-libraries" class="list-unstyled">
                            <li><i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- فحص Console -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">2. فحص Console للأخطاء</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>كيفية فتح Console:</h6>
                    <ol>
                        <li><strong>اضغط F12</strong> أو <strong>Ctrl+Shift+I</strong></li>
                        <li><strong>اذهب لتبويب "Console"</strong></li>
                        <li><strong>ابحث عن رسائل حمراء</strong> (أخطاء)</li>
                        <li><strong>اضغط + أو -</strong> وراقب الرسائل</li>
                    </ol>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>أخطاء شائعة:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">❌ <code>updateQuantity is not defined</code></li>
                            <li class="list-group-item">❌ <code>orderItems is not defined</code></li>
                            <li class="list-group-item">❌ <code>Cannot read property of undefined</code></li>
                            <li class="list-group-item">❌ <code>Syntax error</code></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>رسائل صحيحة:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">✅ <code>تحديث الكمية: 1 -1</code></li>
                            <li class="list-group-item">✅ <code>حالة السلة: {...}</code></li>
                            <li class="list-group-item">✅ <code>تم تحديث العرض</code></li>
                            <li class="list-group-item">✅ <code>الإجمالي: 1000</code></li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-warning" onclick="testConsoleLogging()">
                        <i class="fas fa-bug me-2"></i>
                        اختبار Console
                    </button>
                </div>
            </div>
        </div>
        
        <!-- مسح التخزين المؤقت -->
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">3. مسح التخزين المؤقت</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>خطوات مسح التخزين المؤقت:</h6>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>Chrome/Edge</h6>
                            </div>
                            <div class="card-body">
                                <ol class="small">
                                    <li>اضغط <kbd>Ctrl+Shift+Delete</kbd></li>
                                    <li>اختر "الصور والملفات المخزنة مؤقتاً"</li>
                                    <li>اضغط "مسح البيانات"</li>
                                </ol>
                                <p class="small text-muted">أو اضغط <kbd>Ctrl+F5</kbd> لإعادة تحميل قوية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>Firefox</h6>
                            </div>
                            <div class="card-body">
                                <ol class="small">
                                    <li>اضغط <kbd>Ctrl+Shift+Delete</kbd></li>
                                    <li>اختر "Cache"</li>
                                    <li>اضغط "Clear Now"</li>
                                </ol>
                                <p class="small text-muted">أو اضغط <kbd>Ctrl+Shift+R</kbd></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>Safari</h6>
                            </div>
                            <div class="card-body">
                                <ol class="small">
                                    <li>اضغط <kbd>Cmd+Option+E</kbd></li>
                                    <li>أو من القائمة: Develop > Empty Caches</li>
                                </ol>
                                <p class="small text-muted">أو اضغط <kbd>Cmd+Shift+R</kbd></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-danger" onclick="forceClearCache()">
                        <i class="fas fa-trash me-2"></i>
                        مسح تخزين الصفحة
                    </button>
                    <button class="btn btn-warning ms-2" onclick="location.reload(true)">
                        <i class="fas fa-sync me-2"></i>
                        إعادة تحميل قوية
                    </button>
                </div>
            </div>
        </div>
        
        <!-- اختبار الأزرار -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">4. اختبار الأزرار مباشرة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-play me-2"></i>اختبار تفاعلي:</h6>
                    <p>إذا عملت الأزرار هنا، فالمشكلة في الصفحة الأصلية</p>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <div id="test-cart-area">
                            <div class="border p-3 rounded mb-3">
                                <h6>شاي عراقي - 1000 د.ع</h6>
                                <div class="d-flex align-items-center gap-2">
                                    <button class="btn btn-sm btn-outline-danger" onclick="testUpdateQuantity(1, -1)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <span id="qty-1" class="badge bg-secondary px-3">1</span>
                                    <button class="btn btn-sm btn-outline-success" onclick="testUpdateQuantity(1, 1)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <span class="ms-3">المجموع: <strong id="total-1">1000 د.ع</strong></span>
                                </div>
                            </div>
                            
                            <div class="border p-3 rounded mb-3">
                                <h6>قهوة عربية - 1500 د.ع</h6>
                                <div class="d-flex align-items-center gap-2">
                                    <button class="btn btn-sm btn-outline-danger" onclick="testUpdateQuantity(2, -1)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <span id="qty-2" class="badge bg-secondary px-3">1</span>
                                    <button class="btn btn-sm btn-outline-success" onclick="testUpdateQuantity(2, 1)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <span class="ms-3">المجموع: <strong id="total-2">1500 د.ع</strong></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <h5>الإجمالي الكلي: <span id="grand-total" class="text-success">2500 د.ع</span></h5>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>سجل الاختبار</h6>
                            </div>
                            <div class="card-body">
                                <div id="test-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
                                <button class="btn btn-sm btn-secondary mt-2 w-100" onclick="clearTestLog()">مسح</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- حلول إضافية -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">5. حلول إضافية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>إعدادات المتصفح:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                تأكد من تفعيل JavaScript
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                تعطيل مانع الإعلانات مؤقتاً
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-lock text-danger me-2"></i>
                                تعطيل الوضع الآمن
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-plug text-info me-2"></i>
                                تعطيل الإضافات مؤقتاً
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>اختبار متصفحات أخرى:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="testBrowserCompatibility()">
                                <i class="fab fa-chrome me-2"></i>
                                اختبار Chrome
                            </button>
                            <button class="btn btn-outline-warning" onclick="testBrowserCompatibility()">
                                <i class="fab fa-firefox me-2"></i>
                                اختبار Firefox
                            </button>
                            <button class="btn btn-outline-info" onclick="testBrowserCompatibility()">
                                <i class="fab fa-edge me-2"></i>
                                اختبار Edge
                            </button>
                            <button class="btn btn-outline-secondary" onclick="openIncognito()">
                                <i class="fas fa-user-secret me-2"></i>
                                وضع التصفح الخفي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط الاختبار -->
        <div class="text-center">
            <h5>اختبار الصفحات:</h5>
            <div class="btn-group" role="group">
                <a href="test_cart.php" class="btn btn-primary">سلة مبسطة</a>
                <a href="add_order.php?table_id=1" class="btn btn-success">الصفحة الحقيقية</a>
                <a href="debug_buttons.php" class="btn btn-warning">أداة التشخيص</a>
                <a href="dashboard.php" class="btn btn-secondary">لوحة التحكم</a>
            </div>
        </div>
        
        <!-- تعليمات نهائية -->
        <div class="alert alert-primary mt-4">
            <h5><i class="fas fa-lightbulb me-2"></i>تعليمات نهائية:</h5>
            <ol>
                <li><strong>اختبر الأزرار أعلاه</strong> - إذا عملت فالمشكلة في الصفحة الأصلية</li>
                <li><strong>امسح التخزين المؤقت</strong> واعد تحميل الصفحة</li>
                <li><strong>افتح Console (F12)</strong> وراقب الأخطاء</li>
                <li><strong>جرب متصفح آخر</strong> أو الوضع الخفي</li>
                <li><strong>تأكد من تفعيل JavaScript</strong> في إعدادات المتصفح</li>
            </ol>
        </div>
    </div>

    <script>
        // بيانات الاختبار
        const testItems = {
            1: { name: 'شاي عراقي', price: 1000, quantity: 1 },
            2: { name: 'قهوة عربية', price: 1500, quantity: 1 }
        };
        
        // دالة تسجيل الاختبار
        function testLog(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            if (!logDiv) return;
            
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearTestLog() {
            const logDiv = document.getElementById('test-log');
            if (logDiv) {
                logDiv.innerHTML = '';
                testLog('تم مسح السجل', 'info');
            }
        }
        
        // اختبار تحديث الكمية
        function testUpdateQuantity(itemId, change) {
            testLog(`🔄 تحديث كمية المنتج ${itemId} بـ ${change}`, 'info');
            
            try {
                if (!testItems[itemId]) {
                    testLog(`❌ منتج غير موجود: ${itemId}`, 'error');
                    return;
                }
                
                testItems[itemId].quantity += change;
                
                if (testItems[itemId].quantity < 1) {
                    testItems[itemId].quantity = 1;
                    testLog(`⚠️ الكمية لا يمكن أن تكون أقل من 1`, 'warning');
                }
                
                // تحديث العرض
                const qtyElement = document.getElementById(`qty-${itemId}`);
                const totalElement = document.getElementById(`total-${itemId}`);
                
                if (qtyElement && totalElement) {
                    qtyElement.textContent = testItems[itemId].quantity;
                    const itemTotal = testItems[itemId].price * testItems[itemId].quantity;
                    totalElement.textContent = itemTotal + ' د.ع';
                    
                    // تحديث الإجمالي الكلي
                    updateGrandTotal();
                    
                    testLog(`✅ تم تحديث الكمية إلى ${testItems[itemId].quantity}`, 'success');
                } else {
                    testLog(`❌ عناصر HTML مفقودة`, 'error');
                }
                
            } catch (error) {
                testLog(`❌ خطأ في JavaScript: ${error.message}`, 'error');
            }
        }
        
        function updateGrandTotal() {
            const grandTotal = Object.values(testItems).reduce((sum, item) => {
                return sum + (item.price * item.quantity);
            }, 0);
            
            const grandTotalElement = document.getElementById('grand-total');
            if (grandTotalElement) {
                grandTotalElement.textContent = grandTotal + ' د.ع';
            }
        }
        
        // فحص JavaScript
        function checkJavaScript() {
            const statusDiv = document.getElementById('js-status');
            const functionsDiv = document.getElementById('js-functions');
            const librariesDiv = document.getElementById('js-libraries');
            
            // فحص حالة JavaScript
            if (typeof console !== 'undefined' && typeof document !== 'undefined') {
                statusDiv.className = 'alert alert-success';
                statusDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>JavaScript يعمل بشكل صحيح!';
            } else {
                statusDiv.className = 'alert alert-danger';
                statusDiv.innerHTML = '<i class="fas fa-times-circle me-2"></i>JavaScript معطل أو لا يعمل!';
            }
            
            // فحص الدوال
            const functions = [
                'console.log',
                'document.getElementById',
                'JSON.stringify',
                'fetch',
                'addEventListener'
            ];
            
            let functionsHtml = '';
            functions.forEach(func => {
                try {
                    const parts = func.split('.');
                    let obj = window;
                    for (const part of parts) {
                        obj = obj[part];
                    }
                    if (typeof obj === 'function') {
                        functionsHtml += `<li><i class="fas fa-check text-success me-2"></i>${func}</li>`;
                    } else {
                        functionsHtml += `<li><i class="fas fa-times text-danger me-2"></i>${func}</li>`;
                    }
                } catch (e) {
                    functionsHtml += `<li><i class="fas fa-times text-danger me-2"></i>${func}</li>`;
                }
            });
            functionsDiv.innerHTML = functionsHtml;
            
            // فحص المكتبات
            const libraries = [
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
                { name: 'jQuery', check: () => typeof $ !== 'undefined' },
                { name: 'FontAwesome', check: () => document.querySelector('.fas') !== null }
            ];
            
            let librariesHtml = '';
            libraries.forEach(lib => {
                if (lib.check()) {
                    librariesHtml += `<li><i class="fas fa-check text-success me-2"></i>${lib.name}</li>`;
                } else {
                    librariesHtml += `<li><i class="fas fa-times text-warning me-2"></i>${lib.name} (اختياري)</li>`;
                }
            });
            librariesDiv.innerHTML = librariesHtml;
        }
        
        function testConsoleLogging() {
            console.log('🧪 اختبار Console - هذه رسالة اختبار');
            console.warn('⚠️ اختبار تحذير');
            console.error('❌ اختبار خطأ (هذا طبيعي)');
            console.info('ℹ️ اختبار معلومات');
            
            alert('تم إرسال رسائل اختبار إلى Console.\nافتح Developer Tools (F12) وتحقق من تبويب Console.');
        }
        
        function forceClearCache() {
            // مسح localStorage و sessionStorage
            try {
                localStorage.clear();
                sessionStorage.clear();
                testLog('تم مسح التخزين المحلي', 'success');
            } catch (e) {
                testLog('فشل في مسح التخزين المحلي', 'error');
            }
            
            // إعادة تحميل الصفحة
            setTimeout(() => {
                location.reload(true);
            }, 1000);
        }
        
        function testBrowserCompatibility() {
            const userAgent = navigator.userAgent;
            let browser = 'غير معروف';
            
            if (userAgent.includes('Chrome')) browser = 'Chrome';
            else if (userAgent.includes('Firefox')) browser = 'Firefox';
            else if (userAgent.includes('Safari')) browser = 'Safari';
            else if (userAgent.includes('Edge')) browser = 'Edge';
            
            alert(`المتصفح الحالي: ${browser}\nإصدار JavaScript: ${navigator.appVersion}\nCookies مفعلة: ${navigator.cookieEnabled}`);
        }
        
        function openIncognito() {
            alert('افتح نافذة تصفح خفي:\n\nChrome/Edge: Ctrl+Shift+N\nFirefox: Ctrl+Shift+P\nSafari: Cmd+Shift+N\n\nثم اذهب إلى نفس الصفحة واختبر الأزرار.');
        }
        
        // تشغيل الفحوصات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkJavaScript();
            testLog('🚀 بدء اختبار المتصفح', 'info');
            testLog('💡 اختبر الأزرار أعلاه', 'info');
        });
    </script>
</body>
</html>
