<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$page_title = 'الكاشير';

try {
    // جلب الطلبات النشطة
    $stmt = $pdo->query("
        SELECT o.*, t.table_number, u.full_name as user_name,
               COUNT(oi.id) as items_count
        FROM orders o
        JOIN tables t ON o.table_id = t.id
        JOIN users u ON o.user_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.status IN ('pending', 'processing')
        GROUP BY o.id
        ORDER BY o.created_at ASC
    ");
    $active_orders = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب البيانات';
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-cash-register me-2"></i>الكاشير</h2>
    <div class="text-muted">
        <i class="fas fa-clock me-1"></i>
        <span id="current-time"></span>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
    </div>
<?php endif; ?>

<?php if (empty($active_orders)): ?>
    <div class="text-center py-5">
        <i class="fas fa-cash-register fa-5x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد طلبات نشطة</h4>
        <p class="text-muted">جميع الطلبات مكتملة أو لا توجد طلبات جديدة</p>
        <a href="tables.php" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة طلب جديد
        </a>
    </div>
<?php else: ?>
    <div class="row">
        <?php foreach ($active_orders as $order): ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-<?php echo $order['status'] == 'pending' ? 'warning' : 'info'; ?> text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">طلب #<?php echo $order['id']; ?></h6>
                            <span class="badge bg-light text-dark">
                                طاولة <?php echo $order['table_number']; ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">الموظف:</span>
                                <span><?php echo $order['user_name']; ?></span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">عدد العناصر:</span>
                                <span><?php echo $order['items_count']; ?></span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">الوقت:</span>
                                <span><?php echo date('H:i', strtotime($order['created_at'])); ?></span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="text-muted">الحالة:</span>
                                <span class="badge bg-<?php echo $order['status'] == 'pending' ? 'warning' : 'info'; ?>">
                                    <?php echo $order['status'] == 'pending' ? 'معلق' : 'قيد التحضير'; ?>
                                </span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>الإجمالي:</strong>
                                <strong class="text-success"><?php echo formatPrice($order['total_amount']); ?></strong>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-sm" onclick="viewOrderDetails(<?php echo $order['id']; ?>)">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </button>
                            
                            <?php if ($order['status'] == 'pending'): ?>
                                <button class="btn btn-info btn-sm" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'processing')">
                                    <i class="fas fa-play me-2"></i>
                                    بدء التحضير
                                </button>
                            <?php endif; ?>
                            
                            <button class="btn btn-success btn-sm" onclick="completeOrder(<?php echo $order['id']; ?>)">
                                <i class="fas fa-check me-2"></i>
                                إتمام الطلب
                            </button>
                            
                            <button class="btn btn-outline-secondary btn-sm" onclick="printInvoice(<?php echo $order['id']; ?>)">
                                <i class="fas fa-print me-2"></i>
                                طباعة الفاتورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- نافذة تفاصيل الطلب -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printInvoice(currentOrderId)">
                    <i class="fas fa-print me-2"></i>
                    طباعة الفاتورة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إتمام الطلب -->
<div class="modal fade" id="completeOrderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إتمام الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="completeOrderContent">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
                
                <div class="mb-3">
                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="payment_method">
                        <option value="cash">نقدي</option>
                        <option value="card">بطاقة</option>
                        <option value="transfer">تحويل</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="received_amount" class="form-label">المبلغ المستلم</label>
                    <input type="number" class="form-control" id="received_amount" step="0.01" min="0">
                </div>
                
                <div class="mb-3">
                    <label for="change_amount" class="form-label">المبلغ المرتجع</label>
                    <input type="number" class="form-control" id="change_amount" readonly>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="confirmCompleteOrder()">
                    <i class="fas fa-check me-2"></i>
                    تأكيد الإتمام
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentOrderId = null;
let currentOrderTotal = 0;

function viewOrderDetails(orderId) {
    currentOrderId = orderId;
    
    fetch(`ajax/get_order_details.php?order_id=${orderId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('orderDetailsContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('orderDetailsModal')).show();
        })
        .catch(error => {
            alert('حدث خطأ في جلب تفاصيل الطلب');
        });
}

function completeOrder(orderId) {
    currentOrderId = orderId;
    
    fetch(`ajax/get_order_summary.php?order_id=${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentOrderTotal = parseFloat(data.total);
                document.getElementById('completeOrderContent').innerHTML = `
                    <div class="alert alert-info">
                        <h6>طلب #${orderId}</h6>
                        <p class="mb-0">الإجمالي: <strong>${data.total_formatted}</strong></p>
                    </div>
                `;
                document.getElementById('received_amount').value = currentOrderTotal;
                calculateChange();
                new bootstrap.Modal(document.getElementById('completeOrderModal')).show();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في جلب بيانات الطلب');
        });
}

function calculateChange() {
    const receivedAmount = parseFloat(document.getElementById('received_amount').value) || 0;
    const changeAmount = receivedAmount - currentOrderTotal;
    document.getElementById('change_amount').value = changeAmount.toFixed(2);
}

// حساب المبلغ المرتجع عند تغيير المبلغ المستلم
document.getElementById('received_amount').addEventListener('input', calculateChange);

function confirmCompleteOrder() {
    const paymentMethod = document.getElementById('payment_method').value;
    const receivedAmount = parseFloat(document.getElementById('received_amount').value);
    const changeAmount = parseFloat(document.getElementById('change_amount').value);
    
    if (receivedAmount < currentOrderTotal) {
        alert('المبلغ المستلم أقل من إجمالي الطلب');
        return;
    }
    
    const data = {
        order_id: currentOrderId,
        payment_method: paymentMethod,
        received_amount: receivedAmount,
        change_amount: changeAmount
    };
    
    fetch('ajax/complete_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إتمام الطلب بنجاح');
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في إتمام الطلب');
    });
}

function updateOrderStatus(orderId, status) {
    fetch('ajax/update_order_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_id: orderId,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في تحديث حالة الطلب');
    });
}
</script>

<?php include 'includes/footer.php'; ?>
