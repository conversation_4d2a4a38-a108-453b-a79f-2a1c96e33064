<?php
// ملف فحص النظام والتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 فحص النظام</h1>";
echo "<hr>";

// 1. فحص PHP
echo "<h2>1. معلومات PHP</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><td><strong>إصدار PHP:</strong></td><td>" . phpversion() . "</td></tr>";
echo "<tr><td><strong>الذاكرة المتاحة:</strong></td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td><strong>حد رفع الملفات:</strong></td><td>" . ini_get('upload_max_filesize') . "</td></tr>";
echo "<tr><td><strong>وقت التنفيذ الأقصى:</strong></td><td>" . ini_get('max_execution_time') . " ثانية</td></tr>";
echo "<tr><td><strong>عرض الأخطاء:</strong></td><td>" . (ini_get('display_errors') ? 'مفعل' : 'معطل') . "</td></tr>";
echo "</table><br>";

// 2. فحص الإضافات المطلوبة
echo "<h2>2. الإضافات المطلوبة</h2>";
$extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json'];
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ متوفر' : '❌ مفقود';
    echo "<tr><td><strong>$ext:</strong></td><td>$status</td></tr>";
}
echo "</table><br>";

// 3. فحص الملفات المطلوبة
echo "<h2>3. الملفات المطلوبة</h2>";
$files = [
    'database.sql' => 'ملف قاعدة البيانات',
    'login.php' => 'صفحة تسجيل الدخول',
    'dashboard.php' => 'لوحة التحكم',
    'tables.php' => 'إدارة الطاولات',
    'cashier.php' => 'شاشة الكاشير',
    'kitchen.php' => 'شاشة المطبخ'
];

echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
foreach ($files as $file => $desc) {
    $status = file_exists($file) ? '✅ موجود' : '❌ مفقود';
    echo "<tr><td><strong>$file:</strong></td><td>$desc</td><td>$status</td></tr>";
}
echo "</table><br>";

// 4. فحص المجلدات
echo "<h2>4. المجلدات المطلوبة</h2>";
$dirs = ['config', 'includes', 'ajax'];
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
foreach ($dirs as $dir) {
    $status = is_dir($dir) ? '✅ موجود' : '❌ مفقود';
    $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
    echo "<tr><td><strong>$dir/:</strong></td><td>$status</td><td>$writable</td></tr>";
}
echo "</table><br>";

// 5. اختبار قاعدة البيانات
echo "<h2>5. اختبار قاعدة البيانات</h2>";
if (file_exists('config/database.php')) {
    try {
        include_once 'config/database.php';
        echo "✅ <strong>الاتصال بقاعدة البيانات ناجح</strong><br>";
        
        // فحص الجداول
        $tables = ['users', 'tables', 'categories', 'products', 'orders', 'order_items'];
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>الجدول</th><th>الحالة</th><th>عدد السجلات</th></tr>";
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "<tr><td>$table</td><td>✅ موجود</td><td>$count</td></tr>";
            } catch (Exception $e) {
                echo "<tr><td>$table</td><td>❌ مفقود</td><td>-</td></tr>";
            }
        }
        echo "</table><br>";
        
    } catch (Exception $e) {
        echo "❌ <strong>خطأ في قاعدة البيانات:</strong> " . htmlspecialchars($e->getMessage()) . "<br><br>";
    }
} else {
    echo "⚠️ <strong>ملف الإعداد غير موجود</strong> - يجب تشغيل التثبيت أولاً<br><br>";
}

// 6. اختبار الجلسات
echo "<h2>6. اختبار الجلسات</h2>";
if (session_status() == PHP_SESSION_ACTIVE) {
    echo "✅ <strong>الجلسات تعمل بشكل صحيح</strong><br>";
    echo "معرف الجلسة: " . session_id() . "<br><br>";
} else {
    session_start();
    if (session_status() == PHP_SESSION_ACTIVE) {
        echo "✅ <strong>تم بدء الجلسة بنجاح</strong><br>";
        echo "معرف الجلسة: " . session_id() . "<br><br>";
    } else {
        echo "❌ <strong>مشكلة في الجلسات</strong><br><br>";
    }
}

// 7. اختبار الكتابة
echo "<h2>7. اختبار صلاحيات الكتابة</h2>";
$test_file = 'test_write.tmp';
if (file_put_contents($test_file, 'test')) {
    echo "✅ <strong>الكتابة في المجلد الحالي ممكنة</strong><br>";
    unlink($test_file);
} else {
    echo "❌ <strong>لا يمكن الكتابة في المجلد الحالي</strong><br>";
}

if (!is_dir('config')) {
    if (mkdir('config', 0755, true)) {
        echo "✅ <strong>تم إنشاء مجلد config</strong><br>";
    } else {
        echo "❌ <strong>لا يمكن إنشاء مجلد config</strong><br>";
    }
} else {
    echo "✅ <strong>مجلد config موجود</strong><br>";
}
echo "<br>";

// 8. معلومات الخادم
echo "<h2>8. معلومات الخادم</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><td><strong>نظام التشغيل:</strong></td><td>" . PHP_OS . "</td></tr>";
echo "<tr><td><strong>خادم الويب:</strong></td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . "</td></tr>";
echo "<tr><td><strong>المجلد الحالي:</strong></td><td>" . __DIR__ . "</td></tr>";
echo "<tr><td><strong>المجلد المؤقت:</strong></td><td>" . sys_get_temp_dir() . "</td></tr>";
echo "</table><br>";

// 9. التوصيات
echo "<h2>9. التوصيات</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b8daff;'>";

if (!file_exists('config/database.php')) {
    echo "🔧 <strong>يجب تشغيل التثبيت:</strong><br>";
    echo "• <a href='simple_install.php'>التثبيت المبسط</a><br>";
    echo "• <a href='install.php'>التثبيت المتقدم</a><br>";
    echo "• <a href='setup.php'>معالج الإعداد</a><br><br>";
}

if (phpversion() < '7.4') {
    echo "⚠️ <strong>يُنصح بترقية PHP إلى 7.4 أو أحدث</strong><br><br>";
}

if (!extension_loaded('pdo_mysql')) {
    echo "❌ <strong>يجب تفعيل إضافة PDO MySQL</strong><br><br>";
}

echo "💡 <strong>نصائح للأداء:</strong><br>";
echo "• استخدم PHP 8.0+ للحصول على أفضل أداء<br>";
echo "• تأكد من تفعيل OPcache<br>";
echo "• استخدم MySQL 8.0+ أو MariaDB 10.4+<br>";

echo "</div><br>";

// 10. روابط مفيدة
echo "<h2>10. روابط مفيدة</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "🔗 <strong>روابط التثبيت:</strong><br>";
echo "• <a href='simple_install.php'>التثبيت السريع</a><br>";
echo "• <a href='install.php'>التثبيت العادي</a><br>";
echo "• <a href='setup.php'>معالج الإعداد</a><br><br>";

echo "🔗 <strong>روابط الاختبار:</strong><br>";
echo "• <a href='test_connection.php'>اختبار الاتصال</a><br>";
echo "• <a href='login.php'>تسجيل الدخول</a><br>";
echo "• <a href='dashboard.php'>لوحة التحكم</a><br>";
echo "</div>";

echo "<hr>";
echo "<p><small>تم إنشاء التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

table {
    width: 100%;
    max-width: 800px;
    background: white;
    margin-bottom: 20px;
}

th {
    background-color: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
