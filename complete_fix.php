<?php
// إصلاح شامل لجميع المشاكل
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح شامل للنظام</h1>";
echo "<hr>";

if (!file_exists('config/database.php')) {
    echo "<div style='color: red;'>❌ ملف الإعداد غير موجود</div>";
    exit;
}

try {
    require_once 'config/database.php';
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

$fixes = [];

// 1. إضافة عمود image_path بقوة
echo "<h2>1. إصلاح عمود image_path</h2>";
try {
    // حذف العمود إذا كان موجود ثم إعادة إضافته
    try {
        $pdo->exec("ALTER TABLE products DROP COLUMN image_path");
    } catch (Exception $e) {
        // العمود غير موجود، لا مشكلة
    }
    
    // إضافة العمود
    $pdo->exec("ALTER TABLE products ADD COLUMN image_path VARCHAR(500) NULL");
    echo "<div style='color: green;'>✅ تم إضافة عمود image_path بنجاح</div>";
    $fixes[] = "إضافة عمود image_path";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في إضافة العمود: " . $e->getMessage() . "</div>";
}

// 2. إنشاء مجلد الصور
echo "<h2>2. إنشاء مجلد الصور</h2>";
$upload_dir = 'uploads/products/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0777, true)) {
        echo "<div style='color: green;'>✅ تم إنشاء مجلد الصور</div>";
        $fixes[] = "إنشاء مجلد الصور";
    }
} else {
    echo "<div style='color: green;'>✅ مجلد الصور موجود</div>";
}

// تعديل صلاحيات المجلد
chmod($upload_dir, 0777);

// 3. إضافة فئات افتراضية
echo "<h2>3. إضافة فئات افتراضية</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $categories = [
            'المشروبات الساخنة',
            'المشروبات الباردة',
            'الوجبات الرئيسية',
            'المقبلات',
            'الحلويات',
            'السلطات'
        ];
        
        foreach ($categories as $cat) {
            $stmt = $pdo->prepare("INSERT INTO categories (name) VALUES (?)");
            $stmt->execute([$cat]);
        }
        
        echo "<div style='color: green;'>✅ تم إضافة " . count($categories) . " فئة</div>";
        $fixes[] = "إضافة فئات افتراضية";
    } else {
        echo "<div style='color: green;'>✅ الفئات موجودة ($count فئة)</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في الفئات: " . $e->getMessage() . "</div>";
}

// 4. إنشاء ملف edit_order.php إذا لم يكن موجود
echo "<h2>4. إنشاء ملف edit_order.php</h2>";
if (!file_exists('edit_order.php')) {
    $edit_order_content = '<?php
require_once \'config/database.php\';

if (!isLoggedIn() || (!hasRole(\'admin\') && !hasRole(\'cashier\'))) {
    redirect(\'login.php\');
}

$order_id = intval($_GET[\'id\'] ?? 0);
if ($order_id < 1) {
    showMessage(\'معرف الطلب غير صحيح\', \'error\');
    redirect(\'orders.php\');
}

try {
    $stmt = $pdo->prepare("SELECT o.*, t.table_number FROM orders o JOIN tables t ON o.table_id = t.id WHERE o.id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        showMessage(\'الطلب غير موجود\', \'error\');
        redirect(\'orders.php\');
    }
    
    if ($order[\'status\'] == \'completed\' || $order[\'status\'] == \'cancelled\') {
        showMessage(\'لا يمكن تعديل طلب مكتمل أو ملغي\', \'error\');
        redirect(\'orders.php\');
    }
    
} catch (PDOException $e) {
    showMessage(\'حدث خطأ في جلب البيانات\', \'error\');
    redirect(\'orders.php\');
}

$page_title = \'تعديل الطلب #\' . $order[\'id\'];
include \'includes/header.php\';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>تعديل الطلب #<?php echo $order[\'id\']; ?> - طاولة <?php echo $order[\'table_number\']; ?></h2>
        <a href="orders.php" class="btn btn-secondary">العودة للطلبات</a>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        ميزة تعديل الطلبات قيد التطوير. يمكنك إلغاء الطلب وإنشاء طلب جديد.
    </div>
    
    <div class="card">
        <div class="card-body">
            <h5>معلومات الطلب:</h5>
            <p><strong>رقم الطلب:</strong> <?php echo $order[\'id\']; ?></p>
            <p><strong>الطاولة:</strong> <?php echo $order[\'table_number\']; ?></p>
            <p><strong>الإجمالي:</strong> <?php echo formatPrice($order[\'total_amount\']); ?></p>
            <p><strong>الحالة:</strong> <?php echo $order[\'status\']; ?></p>
        </div>
    </div>
</div>

<?php include \'includes/footer.php\'; ?>';
    
    file_put_contents('edit_order.php', $edit_order_content);
    echo "<div style='color: green;'>✅ تم إنشاء ملف edit_order.php</div>";
    $fixes[] = "إنشاء ملف edit_order.php";
} else {
    echo "<div style='color: green;'>✅ ملف edit_order.php موجود</div>";
}

// 5. إنشاء مجلد ajax إذا لم يكن موجود
echo "<h2>5. إنشاء مجلد ajax</h2>";
if (!is_dir('ajax')) {
    mkdir('ajax', 0777, true);
    echo "<div style='color: green;'>✅ تم إنشاء مجلد ajax</div>";
    $fixes[] = "إنشاء مجلد ajax";
} else {
    echo "<div style='color: green;'>✅ مجلد ajax موجود</div>";
}

// 6. إنشاء ملف ajax/create_order.php محسن
echo "<h2>6. إنشاء ملف ajax/create_order.php</h2>";
$create_order_content = '<?php
require_once \'../config/database.php\';

header(\'Content-Type: application/json\');

if (!isLoggedIn() || (!hasRole(\'admin\') && !hasRole(\'cashier\'))) {
    echo json_encode([\'success\' => false, \'message\' => \'غير مصرح لك بهذا الإجراء\']);
    exit;
}

$input = json_decode(file_get_contents(\'php://input\'), true);

if (!$input || !isset($input[\'table_id\']) || !isset($input[\'items\'])) {
    echo json_encode([\'success\' => false, \'message\' => \'بيانات غير صحيحة\']);
    exit;
}

$table_id = intval($input[\'table_id\']);
$items = $input[\'items\'];

if (empty($items)) {
    echo json_encode([\'success\' => false, \'message\' => \'لا توجد عناصر في الطلب\']);
    exit;
}

try {
    $pdo->beginTransaction();
    
    $stmt = $pdo->prepare("SELECT * FROM tables WHERE id = ? AND status = \'available\'");
    $stmt->execute([$table_id]);
    $table = $stmt->fetch();
    
    if (!$table) {
        throw new Exception(\'الطاولة غير متاحة\');
    }
    
    $total_amount = 0;
    $valid_items = [];
    
    foreach ($items as $item) {
        $product_id = intval($item[\'id\']);
        $quantity = intval($item[\'quantity\']);
        
        if ($quantity < 1) continue;
        
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch();
        
        if (!$product) {
            throw new Exception(\'منتج غير صحيح: \' . $product_id);
        }
        
        $item_total = $product[\'price\'] * $quantity;
        $total_amount += $item_total;
        
        $valid_items[] = [
            \'product_id\' => $product_id,
            \'quantity\' => $quantity,
            \'price\' => $product[\'price\']
        ];
    }
    
    if (empty($valid_items)) {
        throw new Exception(\'لا توجد عناصر صحيحة في الطلب\');
    }
    
    $stmt = $pdo->prepare("INSERT INTO orders (table_id, user_id, total_amount, status) VALUES (?, ?, ?, \'pending\')");
    $stmt->execute([$table_id, $_SESSION[\'user_id\'], $total_amount]);
    
    $order_id = $pdo->lastInsertId();
    
    foreach ($valid_items as $item) {
        $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $order_id,
            $item[\'product_id\'],
            $item[\'quantity\'],
            $item[\'price\']
        ]);
    }
    
    $stmt = $pdo->prepare("UPDATE tables SET status = \'occupied\' WHERE id = ?");
    $stmt->execute([$table_id]);
    
    $pdo->commit();
    
    echo json_encode([
        \'success\' => true,
        \'message\' => \'تم إنشاء الطلب بنجاح\',
        \'order_id\' => $order_id,
        \'total_amount\' => $total_amount
    ]);
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo json_encode([\'success\' => false, \'message\' => $e->getMessage()]);
}
?>';

file_put_contents('ajax/create_order.php', $create_order_content);
echo "<div style='color: green;'>✅ تم إنشاء ملف ajax/create_order.php</div>";
$fixes[] = "إنشاء ملف ajax/create_order.php";

// 7. اختبار إضافة منتج مع صورة
echo "<h2>7. اختبار إضافة منتج</h2>";
if (isset($_POST['test_product'])) {
    try {
        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id, image_path) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            'منتج تجريبي ' . date('H:i:s'),
            'وصف تجريبي للمنتج',
            5000,
            1,
            null
        ]);
        echo "<div style='color: green;'>✅ تم إضافة منتج تجريبي بنجاح!</div>";
        $fixes[] = "اختبار إضافة منتج نجح";
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ فشل في إضافة المنتج: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<form method='POST'>";
    echo "<button type='submit' name='test_product' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>اختبار إضافة منتج مع عمود الصورة</button>";
    echo "</form>";
}

// عرض النتائج
echo "<hr>";
echo "<h2>📋 ملخص الإصلاحات:</h2>";

if (empty($fixes)) {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ النظام يعمل بشكل صحيح!</h3>";
    echo "</div>";
} else {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ تم تطبيق الإصلاحات التالية:</h3>";
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// فحص النظام
echo "<h2>🔍 فحص النظام:</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $categories_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $products_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'image_path'");
    $has_image_column = $stmt->rowCount() > 0;
    
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; background: white;'>";
    echo "<tr><th>العنصر</th><th>الحالة</th></tr>";
    echo "<tr><td>عمود image_path</td><td>" . ($has_image_column ? '✅ موجود' : '❌ مفقود') . "</td></tr>";
    echo "<tr><td>مجلد الصور</td><td>" . (is_dir('uploads/products') ? '✅ موجود' : '❌ مفقود') . "</td></tr>";
    echo "<tr><td>الفئات</td><td>$categories_count فئة</td></tr>";
    echo "<tr><td>المنتجات</td><td>$products_count منتج</td></tr>";
    echo "<tr><td>ملف edit_order.php</td><td>" . (file_exists('edit_order.php') ? '✅ موجود' : '❌ مفقود') . "</td></tr>";
    echo "<tr><td>ملف ajax/create_order.php</td><td>" . (file_exists('ajax/create_order.php') ? '✅ موجود' : '❌ مفقود') . "</td></tr>";
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في الفحص: " . $e->getMessage() . "</div>";
}

echo "<h2>🔗 روابط الاختبار:</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار المنتجات</a>";
echo "<a href='tables.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الطاولات</a>";
echo "<a href='orders.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الطلبات</a>";
echo "<a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
echo "</div>";

echo "<hr>";
echo "<p><small>تم الإصلاح في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

table {
    margin: 10px 0;
}

th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
