<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

$page_title = 'لوحة التحكم';

// إحصائيات سريعة
try {
    // عدد الطاولات المتاحة والمشغولة
    $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM tables GROUP BY status");
    $table_stats = [];
    while ($row = $stmt->fetch()) {
        $table_stats[$row['status']] = $row['count'];
    }
    
    // عدد الطلبات اليوم
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = CURDATE()");
    $today_orders = $stmt->fetchColumn();
    
    // إجمالي المبيعات اليوم
    $stmt = $pdo->query("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE DATE(created_at) = CURDATE() AND status = 'completed'");
    $today_sales = $stmt->fetchColumn();
    
    // الطلبات المعلقة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE status IN ('pending', 'processing')");
    $pending_orders = $stmt->fetchColumn();
    
    // أحدث الطلبات
    $stmt = $pdo->query("
        SELECT o.*, t.table_number, u.full_name as user_name 
        FROM orders o 
        JOIN tables t ON o.table_id = t.id 
        JOIN users u ON o.user_id = u.id 
        ORDER BY o.created_at DESC 
        LIMIT 5
    ");
    $recent_orders = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب البيانات';
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
    <div class="text-muted">
        <i class="fas fa-clock me-1"></i>
        <span id="current-time"></span>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?php echo $table_stats['available'] ?? 0; ?></h4>
                        <p class="mb-0">طاولات متاحة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-table fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?php echo $table_stats['occupied'] ?? 0; ?></h4>
                        <p class="mb-0">طاولات مشغولة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?php echo $today_orders; ?></h4>
                        <p class="mb-0">طلبات اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?php echo formatPrice($today_sales); ?></h4>
                        <p class="mb-0">مبيعات اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الطلبات المعلقة -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الطلبات المعلقة (<?php echo $pending_orders; ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if ($pending_orders > 0): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        يوجد <?php echo $pending_orders; ?> طلب معلق يحتاج إلى معالجة
                    </div>
                    <div class="text-center">
                        <a href="orders.php" class="btn btn-warning">
                            <i class="fas fa-eye me-2"></i>
                            عرض الطلبات المعلقة
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>لا توجد طلبات معلقة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- أحدث الطلبات -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    أحدث الطلبات
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_orders)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_orders as $order): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">طلب #<?php echo $order['id']; ?></h6>
                                    <small class="text-muted">
                                        طاولة <?php echo $order['table_number']; ?> - 
                                        <?php echo $order['user_name']; ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-<?php 
                                        echo $order['status'] == 'completed' ? 'success' : 
                                            ($order['status'] == 'processing' ? 'warning' : 'secondary'); 
                                    ?>">
                                        <?php 
                                        $status_text = [
                                            'pending' => 'معلق',
                                            'processing' => 'قيد التحضير',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي'
                                        ];
                                        echo $status_text[$order['status']];
                                        ?>
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo formatPrice($order['total_amount']); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="orders.php" class="btn btn-outline-info">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع الطلبات
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>لا توجد طلبات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (hasRole('admin') || hasRole('cashier')): ?>
                    <div class="col-md-3 mb-3">
                        <a href="tables.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-table fa-2x mb-2"></i>
                            إدارة الطاولات
                        </a>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <a href="cashier.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-cash-register fa-2x mb-2"></i>
                            الكاشير
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (hasRole('kitchen')): ?>
                    <div class="col-md-3 mb-3">
                        <a href="kitchen.php" class="btn btn-outline-danger w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-fire fa-2x mb-2"></i>
                            المطبخ
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (hasRole('admin')): ?>
                    <div class="col-md-3 mb-3">
                        <a href="reports.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            التقارير
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
