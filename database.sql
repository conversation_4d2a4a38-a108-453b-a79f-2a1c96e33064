-- إن<PERSON>ا<PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS restaurant_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE restaurant_management_system;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'cashier', 'kitchen') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الطاولات
CREATE TABLE IF NOT EXISTS tables (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_number VARCHAR(10) NOT NULL UNIQUE,
    capacity INT NOT NULL,
    status ENUM('available', 'occupied') NOT NULL DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول فئات المنتجات
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الطلبات
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_id INT NOT NULL,
    user_id INT NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
    total_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES tables(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تفاصيل الطلبات
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    status ENUM('pending', 'preparing', 'ready', 'served') NOT NULL DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات المستخدمين الافتراضية
INSERT INTO users (username, password, full_name, role) VALUES
('admin', '$2y$10$8zUkfYIIXdvwFOkt5Xo6dO2OzFxIZZHpXpFMBV6HjU4kKQwQlGjnS', 'مدير النظام', 'admin'),
('cashier', '$2y$10$8zUkfYIIXdvwFOkt5Xo6dO2OzFxIZZHpXpFMBV6HjU4kKQwQlGjnS', 'موظف الكاشير', 'cashier'),
('kitchen', '$2y$10$8zUkfYIIXdvwFOkt5Xo6dO2OzFxIZZHpXpFMBV6HjU4kKQwQlGjnS', 'موظف المطبخ', 'kitchen');
-- كلمة المرور لجميع المستخدمين هي: password123

-- إدخال بيانات الطاولات الافتراضية
INSERT INTO tables (table_number, capacity, status) VALUES
('1', 4, 'available'),
('2', 4, 'available'),
('3', 6, 'available'),
('4', 2, 'available'),
('5', 8, 'available');

-- إدخال بيانات فئات المنتجات الافتراضية
INSERT INTO categories (name) VALUES
('مشروبات ساخنة'),
('مشروبات باردة'),
('وجبات رئيسية'),
('مقبلات'),
('حلويات');

-- إدخال بيانات المنتجات الافتراضية
INSERT INTO products (name, description, price, category_id) VALUES
('قهوة عربية', 'قهوة عربية تقليدية', 10.00, 1),
('شاي', 'شاي أسود مع النعناع', 8.00, 1),
('كابتشينو', 'قهوة إيطالية مع الحليب', 15.00, 1),
('عصير برتقال', 'عصير برتقال طازج', 12.00, 2),
('عصير ليمون', 'عصير ليمون منعش', 12.00, 2),
('مياه معدنية', 'زجاجة مياه معدنية', 5.00, 2),
('برجر لحم', 'برجر لحم مع البطاطا المقلية', 45.00, 3),
('دجاج مشوي', 'نصف دجاجة مشوية مع الأرز', 55.00, 3),
('سلطة سيزر', 'سلطة خضراء مع صوص السيزر', 30.00, 4),
('بطاطا مقلية', 'طبق من البطاطا المقلية', 20.00, 4),
('كيك الشوكولاتة', 'كيك الشوكولاتة الساخن', 25.00, 5),
('آيس كريم', 'آيس كريم بنكهات متنوعة', 18.00, 5);
