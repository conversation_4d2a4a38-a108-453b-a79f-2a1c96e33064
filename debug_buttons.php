<?php
// تشخيص مشكلة أزرار + و - في الطلبات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص مشكلة أزرار + و -</h1>";
echo "<hr>";

// 1. فحص ملفات AJAX
echo "<h2>1. فحص ملفات AJAX</h2>";
$ajax_files = [
    'ajax/update_order_item.php',
    'ajax/update_order.php',
    'ajax/create_order.php'
];

foreach ($ajax_files as $file) {
    if (file_exists($file)) {
        echo "<div style='color: green;'>✅ موجود: $file</div>";
        
        // فحص محتوى الملف
        $content = file_get_contents($file);
        if (strpos($content, 'header(') !== false) {
            echo "<div style='color: blue;'>  📄 يحتوي على headers</div>";
        } else {
            echo "<div style='color: orange;'>  ⚠️ لا يحتوي على headers</div>";
        }
        
        if (strpos($content, 'json_encode') !== false) {
            echo "<div style='color: blue;'>  📄 يحتوي على JSON response</div>";
        } else {
            echo "<div style='color: orange;'>  ⚠️ لا يحتوي على JSON response</div>";
        }
    } else {
        echo "<div style='color: red;'>❌ مفقود: $file</div>";
    }
}

// 2. اختبار AJAX مباشر
echo "<h2>2. اختبار AJAX مباشر</h2>";
if (isset($_POST['test_ajax'])) {
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>نتيجة اختبار AJAX:</h4>";
    
    // محاكاة طلب AJAX
    $test_data = [
        'action' => 'update_quantity',
        'order_id' => 1,
        'product_id' => 1,
        'quantity' => 2
    ];
    
    // إنشاء ملف مؤقت لاختبار AJAX
    $temp_file = 'test_ajax_response.php';
    $ajax_test_code = '<?php
require_once "config/database.php";
header("Content-Type: application/json");

try {
    echo json_encode([
        "success" => true,
        "message" => "اختبار AJAX نجح",
        "data" => $_POST
    ]);
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}
?>';
    
    file_put_contents($temp_file, $ajax_test_code);
    
    // اختبار الطلب
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/' . $temp_file);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($test_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<strong>HTTP Code:</strong> $http_code<br>";
    echo "<strong>Response:</strong> <pre>$response</pre>";
    
    // حذف الملف المؤقت
    unlink($temp_file);
    
    echo "</div>";
}

// 3. فحص JavaScript
echo "<h2>3. فحص JavaScript في add_order.php</h2>";
if (file_exists('add_order.php')) {
    $content = file_get_contents('add_order.php');
    
    // البحث عن دوال JavaScript المهمة
    $js_functions = [
        'updateQuantity',
        'addProductToOrder',
        'updateOrderDisplay',
        'submitOrder'
    ];
    
    foreach ($js_functions as $func) {
        if (strpos($content, "function $func") !== false) {
            echo "<div style='color: green;'>✅ دالة موجودة: $func</div>";
        } else {
            echo "<div style='color: red;'>❌ دالة مفقودة: $func</div>";
        }
    }
    
    // فحص استدعاءات AJAX
    if (strpos($content, 'fetch(') !== false) {
        echo "<div style='color: green;'>✅ يستخدم fetch() للـ AJAX</div>";
    } elseif (strpos($content, '$.ajax') !== false || strpos($content, '$.post') !== false) {
        echo "<div style='color: green;'>✅ يستخدم jQuery للـ AJAX</div>";
    } else {
        echo "<div style='color: red;'>❌ لا يوجد استدعاءات AJAX</div>";
    }
} else {
    echo "<div style='color: red;'>❌ ملف add_order.php غير موجود</div>";
}

// 4. إنشاء صفحة اختبار مبسطة
echo "<h2>4. اختبار مبسط للأزرار</h2>";
?>

<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
    <h4>اختبار أزرار + و - مباشرة:</h4>
    
    <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
        <div style="display: flex; align-items: center; gap: 10px;">
            <span>منتج تجريبي:</span>
            <button onclick="testUpdateQuantity(-1)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">-</button>
            <span id="test-quantity" style="background: #f8f9fa; padding: 5px 15px; border: 1px solid #ddd; border-radius: 3px;">1</span>
            <button onclick="testUpdateQuantity(1)" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px;">+</button>
            <span style="margin-left: 20px;">الإجمالي: <strong id="test-total">5000 د.ع</strong></span>
        </div>
    </div>
    
    <div id="test-log" style="background: #fff; border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px; height: 150px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    
    <button onclick="clearLog()" style="background: #6c757d; color: white; border: none; padding: 8px 15px; border-radius: 3px;">مسح السجل</button>
</div>

<script>
let testQuantity = 1;
const testPrice = 5000;

function log(message) {
    const logDiv = document.getElementById('test-log');
    const time = new Date().toLocaleTimeString();
    logDiv.innerHTML += `[${time}] ${message}\n`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function testUpdateQuantity(change) {
    log(`🔄 محاولة تحديث الكمية بـ ${change}`);
    
    try {
        testQuantity += change;
        
        if (testQuantity < 1) {
            testQuantity = 1;
            log(`⚠️ الكمية لا يمكن أن تكون أقل من 1`);
        }
        
        // تحديث العرض
        document.getElementById('test-quantity').textContent = testQuantity;
        document.getElementById('test-total').textContent = (testQuantity * testPrice) + ' د.ع';
        
        log(`✅ تم تحديث الكمية إلى ${testQuantity}`);
        log(`💰 الإجمالي الجديد: ${testQuantity * testPrice} د.ع`);
        
        // محاكاة طلب AJAX
        simulateAjaxRequest(change);
        
    } catch (error) {
        log(`❌ خطأ في JavaScript: ${error.message}`);
    }
}

function simulateAjaxRequest(change) {
    log(`📡 محاكاة طلب AJAX...`);
    
    const data = {
        action: 'update_quantity',
        product_id: 1,
        quantity: testQuantity,
        change: change
    };
    
    log(`📤 البيانات المرسلة: ${JSON.stringify(data)}`);
    
    // محاكاة تأخير الشبكة
    setTimeout(() => {
        // محاكاة استجابة ناجحة
        const response = {
            success: true,
            message: 'تم التحديث بنجاح',
            new_quantity: testQuantity,
            new_total: testQuantity * testPrice
        };
        
        log(`📥 الاستجابة: ${JSON.stringify(response)}`);
        log(`✅ طلب AJAX نجح`);
    }, 500);
}

function clearLog() {
    document.getElementById('test-log').innerHTML = '';
    log('🧹 تم مسح السجل');
}

// تسجيل بداية الاختبار
log('🚀 بدء اختبار أزرار + و -');
log('💡 اضغط على الأزرار لاختبار الوظائف');
</script>

<?php
// 5. نماذج الاختبار
echo "<h2>5. أدوات الاختبار</h2>";

if (!isset($_POST['test_ajax'])) {
    echo "<form method='POST' style='margin: 20px 0;'>";
    echo "<button type='submit' name='test_ajax' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;'>اختبار AJAX</button>";
    echo "</form>";
}

// 6. إنشاء ملف AJAX مبسط للاختبار
echo "<h2>6. إنشاء ملف AJAX للاختبار</h2>";
if (isset($_POST['create_test_ajax'])) {
    $test_ajax_content = '<?php
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// قراءة البيانات
$input = json_decode(file_get_contents("php://input"), true);
if (!$input) {
    $input = $_POST;
}

// تسجيل الطلب
error_log("AJAX Request: " . json_encode($input));

try {
    // محاكاة معالجة البيانات
    $response = [
        "success" => true,
        "message" => "تم التحديث بنجاح",
        "received_data" => $input,
        "timestamp" => date("Y-m-d H:i:s")
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}
?>';
    
    file_put_contents('ajax/test_update.php', $test_ajax_content);
    echo "<div style='color: green;'>✅ تم إنشاء ملف ajax/test_update.php للاختبار</div>";
}

if (!isset($_POST['create_test_ajax'])) {
    echo "<form method='POST' style='margin: 20px 0;'>";
    echo "<button type='submit' name='create_test_ajax' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;'>إنشاء ملف AJAX للاختبار</button>";
    echo "</form>";
}

// 7. روابط الاختبار
echo "<h2>7. روابط الاختبار المباشر</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='add_order.php?table_id=1' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار إضافة طلب</a>";
echo "<a href='orders.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار تعديل طلب</a>";
echo "<a href='tables.php' target='_blank' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار من الطاولات</a>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b8daff; margin: 20px 0;'>";
echo "<h4>💡 خطوات التشخيص:</h4>";
echo "<ol>";
echo "<li><strong>اختبر الأزرار أعلاه</strong> - إذا عملت فالمشكلة في AJAX</li>";
echo "<li><strong>افتح Developer Tools (F12)</strong> - تبويب Console</li>";
echo "<li><strong>اضغط + أو -</strong> في الصفحة الحقيقية</li>";
echo "<li><strong>ابحث عن رسائل خطأ</strong> في Console</li>";
echo "<li><strong>اذهب لتبويب Network</strong> - راقب طلبات AJAX</li>";
echo "<li><strong>تحقق من الاستجابة</strong> - يجب أن تكون JSON صحيح</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><strong>🔍 تم فحص النظام في: " . date('Y-m-d H:i:s') . "</strong></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

button {
    cursor: pointer;
}

button:hover {
    opacity: 0.8;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
