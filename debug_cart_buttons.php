<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص أزرار سلة الطلب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="alert alert-danger">
            <h4><i class="fas fa-bug me-2"></i>تشخيص مشكلة أزرار + و - في سلة الطلب</h4>
            <p><strong>المشكلة:</strong> الأزرار موجودة لكن لا تعمل عند الضغط عليها</p>
        </div>
        
        <div class="row">
            <!-- محاكاة سلة الطلب -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            محاكاة سلة الطلب - طاولة 1
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- منتجات للإضافة -->
                        <div class="mb-4">
                            <h6>إضافة منتجات:</h6>
                            <div class="d-flex gap-2 flex-wrap">
                                <button class="btn btn-sm btn-outline-primary" onclick="addToCart(1, 'شاي عراقي', 1000)">
                                    شاي عراقي (1000 د.ع)
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="addToCart(2, 'قهوة عربية', 1500)">
                                    قهوة عربية (1500 د.ع)
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="addToCart(12, 'كباب عراقي', 8000)">
                                    كباب عراقي (8000 د.ع)
                                </button>
                            </div>
                        </div>
                        
                        <!-- سلة الطلب -->
                        <div id="order-items">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                <p>لم يتم إضافة أي منتجات بعد</p>
                                <p class="small">أضف منتجات من الأزرار أعلاه</p>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <strong>الإجمالي:</strong>
                            <strong id="total-amount" class="text-success">0 د.ع</strong>
                        </div>
                        
                        <button type="button" class="btn btn-success w-100 mt-3" id="submit-order" onclick="submitOrder()" disabled>
                            <i class="fas fa-check me-2"></i>
                            تأكيد الطلب
                        </button>
                    </div>
                </div>
                
                <!-- اختبار مباشر للدوال -->
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">اختبار مباشر للدوال</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-sm btn-danger" onclick="testUpdateQuantity(12, -1)">
                                اختبار updateQuantity(12, -1)
                            </button>
                            <button class="btn btn-sm btn-success" onclick="testUpdateQuantity(12, 1)">
                                اختبار updateQuantity(12, 1)
                            </button>
                            <button class="btn btn-sm btn-info" onclick="testUpdateOrderDisplay()">
                                اختبار updateOrderDisplay()
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="showOrderItems()">
                                عرض محتويات orderItems
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- سجل التشخيص -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            سجل التشخيص
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="debug-log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-secondary w-100" onclick="clearLog()">مسح السجل</button>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات النظام -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">معلومات النظام</h6>
                    </div>
                    <div class="card-body">
                        <div id="system-info">
                            <div><strong>JavaScript:</strong> <span id="js-status">فحص...</span></div>
                            <div><strong>orderItems:</strong> <span id="orderitems-status">فحص...</span></div>
                            <div><strong>updateQuantity:</strong> <span id="updatequantity-status">فحص...</span></div>
                            <div><strong>updateOrderDisplay:</strong> <span id="updatedisplay-status">فحص...</span></div>
                            <div><strong>عناصر HTML:</strong> <span id="html-status">فحص...</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط الاختبار -->
        <div class="text-center mt-4">
            <a href="add_order.php?table_id=1" class="btn btn-primary me-2">
                <i class="fas fa-external-link-alt me-2"></i>
                الصفحة الحقيقية
            </a>
            <a href="test_add_order.php" class="btn btn-warning me-2">
                <i class="fas fa-flask me-2"></i>
                صفحة اختبار أخرى
            </a>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-home me-2"></i>
                لوحة التحكم
            </a>
        </div>
    </div>

    <style>
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quantity-display {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            min-width: 50px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }
        
        .order-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
    </style>

    <script>
        // متغيرات النظام (نفس المتغيرات من add_order.php)
        let orderItems = {};
        
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${icon} ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            // تحديث معلومات النظام
            updateSystemInfo();
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }
        
        // إضافة منتج للسلة
        function addToCart(productId, productName, productPrice) {
            log(`➕ إضافة منتج: ${productName} (ID: ${productId})`, 'info');
            
            try {
                if (orderItems[productId]) {
                    orderItems[productId].quantity++;
                    log(`📈 زيادة كمية ${productName} إلى ${orderItems[productId].quantity}`, 'success');
                } else {
                    orderItems[productId] = {
                        id: productId,
                        name: productName,
                        price: productPrice,
                        quantity: 1
                    };
                    log(`✅ تم إضافة ${productName} للسلة`, 'success');
                }
                
                log(`📋 حالة orderItems: ${JSON.stringify(orderItems)}`, 'info');
                updateOrderDisplay();
                
            } catch (error) {
                log(`❌ خطأ في إضافة المنتج: ${error.message}`, 'error');
            }
        }
        
        // دالة updateQuantity (نفس الدالة من add_order.php)
        function updateQuantity(productId, change) {
            log(`🔄 استدعاء updateQuantity(${productId}, ${change})`, 'info');
            
            if (!orderItems[productId]) {
                log(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
                alert('خطأ: المنتج غير موجود في السلة');
                return;
            }

            try {
                if (typeof change === 'string') {
                    // إذا كان التغيير من input field
                    const newQuantity = parseInt(change);
                    if (newQuantity > 0) {
                        orderItems[productId].quantity = newQuantity;
                        log(`✅ تم تحديث الكمية من input: ${newQuantity}`, 'success');
                    } else {
                        delete orderItems[productId];
                        log(`🗑️ تم حذف المنتج (كمية 0)`, 'warning');
                    }
                } else {
                    // إذا كان التغيير من الأزرار + أو -
                    const oldQuantity = orderItems[productId].quantity;
                    orderItems[productId].quantity += change;
                    
                    log(`📊 الكمية: ${oldQuantity} → ${orderItems[productId].quantity}`, 'info');

                    if (orderItems[productId].quantity <= 0) {
                        log(`🗑️ حذف المنتج من السلة (كمية <= 0)`, 'warning');
                        delete orderItems[productId];
                    }
                }

                log(`📋 حالة السلة بعد التحديث: ${JSON.stringify(orderItems)}`, 'info');
                updateOrderDisplay();
                
            } catch (error) {
                log(`❌ خطأ في تحديث الكمية: ${error.message}`, 'error');
                alert('حدث خطأ في تحديث الكمية: ' + error.message);
            }
        }
        
        // دالة updateOrderDisplay (نفس الدالة من add_order.php)
        function updateOrderDisplay() {
            log('🔄 تحديث عرض السلة...', 'info');

            const container = document.getElementById('order-items');
            const totalElement = document.getElementById('total-amount');
            const submitButton = document.getElementById('submit-order');

            // التحقق من وجود العناصر
            if (!container) {
                log('❌ عنصر order-items غير موجود', 'error');
                alert('خطأ: عنصر السلة غير موجود في الصفحة');
                return;
            }
            
            if (!totalElement) {
                log('❌ عنصر total-amount غير موجود', 'error');
                return;
            }
            
            if (!submitButton) {
                log('❌ عنصر submit-order غير موجود', 'error');
                return;
            }

            log(`📋 عناصر السلة الحالية: ${JSON.stringify(orderItems)}`, 'info');
            
            if (Object.keys(orderItems).length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>لم يتم إضافة أي منتجات بعد</p>
                        <p class="small">أضف منتجات من الأزرار أعلاه</p>
                    </div>
                `;
                totalElement.textContent = '0 د.ع';
                submitButton.disabled = true;
                log('📭 السلة فارغة', 'info');
                return;
            }
            
            let html = '';
            let total = 0;
            
            for (const item of Object.values(orderItems)) {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                
                html += `
                    <div class="order-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="mb-1">${item.name}</h6>
                                <small class="text-muted">${Math.round(item.price)} د.ع</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(${item.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="quantity-controls">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span class="quantity-display">${item.quantity}</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <strong>${Math.round(itemTotal)} د.ع</strong>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            totalElement.textContent = Math.round(total) + ' د.ع';
            submitButton.disabled = false;

            log('✅ تم تحديث العرض بنجاح', 'success');
            log(`💰 الإجمالي: ${total} د.ع`, 'info');
            log(`📦 عدد العناصر: ${Object.keys(orderItems).length}`, 'info');
        }
        
        // حذف عنصر من السلة
        function removeItem(productId) {
            log(`🗑️ حذف منتج ${productId}`, 'warning');
            
            if (orderItems[productId]) {
                const productName = orderItems[productId].name;
                delete orderItems[productId];
                log(`✅ تم حذف ${productName} من السلة`, 'success');
                updateOrderDisplay();
            } else {
                log(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
            }
        }
        
        // تأكيد الطلب
        function submitOrder() {
            log('🛒 تأكيد الطلب...', 'info');
            
            if (Object.keys(orderItems).length === 0) {
                alert('يرجى إضافة منتجات للطلب');
                log('❌ محاولة تأكيد طلب فارغ', 'error');
                return;
            }

            const orderData = {
                table_id: 1,
                items: Object.values(orderItems)
            };

            log(`📋 بيانات الطلب: ${JSON.stringify(orderData)}`, 'info');
            
            const total = Object.values(orderItems).reduce((sum, item) => sum + (item.price * item.quantity), 0);
            alert(`تم تأكيد الطلب!\nالعناصر: ${Object.keys(orderItems).length}\nالإجمالي: ${total} د.ع`);
            
            log('✅ تم تأكيد الطلب بنجاح', 'success');
            
            // مسح السلة
            orderItems = {};
            updateOrderDisplay();
        }
        
        // اختبار مباشر للدوال
        function testUpdateQuantity(productId, change) {
            log(`🧪 اختبار مباشر: updateQuantity(${productId}, ${change})`, 'warning');
            
            // التأكد من وجود المنتج أولاً
            if (!orderItems[productId]) {
                log(`⚠️ المنتج ${productId} غير موجود، سيتم إضافته أولاً`, 'warning');
                addToCart(productId, `منتج تجريبي ${productId}`, 5000);
            }
            
            // الآن اختبار updateQuantity
            updateQuantity(productId, change);
        }
        
        function testUpdateOrderDisplay() {
            log('🧪 اختبار مباشر: updateOrderDisplay()', 'warning');
            updateOrderDisplay();
        }
        
        function showOrderItems() {
            log(`📋 محتويات orderItems: ${JSON.stringify(orderItems, null, 2)}`, 'info');
            
            if (Object.keys(orderItems).length === 0) {
                alert('السلة فارغة');
            } else {
                let message = 'محتويات السلة:\n\n';
                for (const item of Object.values(orderItems)) {
                    message += `${item.name}: ${item.quantity} × ${item.price} = ${item.quantity * item.price} د.ع\n`;
                }
                alert(message);
            }
        }
        
        // تحديث معلومات النظام
        function updateSystemInfo() {
            // حالة JavaScript
            document.getElementById('js-status').textContent = 'يعمل';
            document.getElementById('js-status').className = 'text-success';
            
            // حالة orderItems
            const orderItemsStatus = document.getElementById('orderitems-status');
            if (typeof orderItems !== 'undefined') {
                orderItemsStatus.textContent = `موجود (${Object.keys(orderItems).length} عنصر)`;
                orderItemsStatus.className = 'text-success';
            } else {
                orderItemsStatus.textContent = 'غير موجود';
                orderItemsStatus.className = 'text-danger';
            }
            
            // حالة updateQuantity
            const updateQuantityStatus = document.getElementById('updatequantity-status');
            if (typeof updateQuantity === 'function') {
                updateQuantityStatus.textContent = 'موجود';
                updateQuantityStatus.className = 'text-success';
            } else {
                updateQuantityStatus.textContent = 'غير موجود';
                updateQuantityStatus.className = 'text-danger';
            }
            
            // حالة updateOrderDisplay
            const updateDisplayStatus = document.getElementById('updatedisplay-status');
            if (typeof updateOrderDisplay === 'function') {
                updateDisplayStatus.textContent = 'موجود';
                updateDisplayStatus.className = 'text-success';
            } else {
                updateDisplayStatus.textContent = 'غير موجود';
                updateDisplayStatus.className = 'text-danger';
            }
            
            // حالة عناصر HTML
            const htmlStatus = document.getElementById('html-status');
            const elements = ['order-items', 'total-amount', 'submit-order'];
            const foundElements = elements.filter(id => document.getElementById(id));
            htmlStatus.textContent = `${foundElements.length}/${elements.length} موجود`;
            htmlStatus.className = foundElements.length === elements.length ? 'text-success' : 'text-warning';
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء تشخيص أزرار سلة الطلب', 'info');
            log('💡 أضف منتجات واختبر أزرار + و -', 'info');
            
            // تحديث معلومات النظام
            updateSystemInfo();
            
            // تحديث عرض السلة
            updateOrderDisplay();
            
            log('✅ تم تحميل الصفحة بنجاح', 'success');
        });
        
        // التقاط الأخطاء العامة
        window.onerror = function(msg, url, line, col, error) {
            log(`❌ خطأ JavaScript: ${msg} في السطر ${line}`, 'error');
            return false;
        };
        
        // التقاط أخطاء الوعود
        window.addEventListener('unhandledrejection', function(event) {
            log(`❌ خطأ Promise: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
