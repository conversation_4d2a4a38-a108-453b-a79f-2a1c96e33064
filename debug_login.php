<?php
// ملف تشخيص مشاكل تسجيل الدخول
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص مشاكل تسجيل الدخول</h1>";
echo "<hr>";

// 1. فحص ملف الإعداد
echo "<h2>1. فحص ملف الإعداد</h2>";
if (file_exists('config/database.php')) {
    echo "✅ ملف config/database.php موجود<br>";
    
    try {
        include_once 'config/database.php';
        echo "✅ تم تحميل ملف الإعداد بنجاح<br>";
        
        if (isset($pdo)) {
            echo "✅ متغير \$pdo متوفر<br>";
        } else {
            echo "❌ متغير \$pdo غير متوفر<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في تحميل ملف الإعداد: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ ملف config/database.php غير موجود<br>";
    echo "<a href='simple_install.php'>إعادة التثبيت</a><br>";
}
echo "<br>";

// 2. فحص قاعدة البيانات
echo "<h2>2. فحص قاعدة البيانات</h2>";
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $count = $stmt->fetchColumn();
        echo "✅ جدول المستخدمين موجود ويحتوي على $count مستخدم<br>";
        
        // عرض المستخدمين
        $stmt = $pdo->query("SELECT id, username, full_name, role FROM users");
        $users = $stmt->fetchAll();
        
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse; margin-top: 10px;'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الدور</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['full_name'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
        
    } catch (Exception $e) {
        echo "❌ خطأ في الوصول لجدول المستخدمين: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ لا يمكن الوصول لقاعدة البيانات<br>";
}
echo "<br>";

// 3. اختبار كلمات المرور
echo "<h2>3. اختبار كلمات المرور</h2>";
if (isset($pdo)) {
    try {
        $test_password = 'password123';
        $hashed = password_hash($test_password, PASSWORD_DEFAULT);
        
        echo "كلمة المرور الاختبارية: $test_password<br>";
        echo "كلمة المرور المشفرة: $hashed<br>";
        echo "اختبار التحقق: " . (password_verify($test_password, $hashed) ? "✅ نجح" : "❌ فشل") . "<br>";
        
        // فحص كلمات المرور في قاعدة البيانات
        $stmt = $pdo->query("SELECT username, password FROM users");
        $users = $stmt->fetchAll();
        
        echo "<h4>فحص كلمات المرور المحفوظة:</h4>";
        foreach ($users as $user) {
            $verify_result = password_verify('password123', $user['password']);
            echo "المستخدم {$user['username']}: " . ($verify_result ? "✅ صحيحة" : "❌ خاطئة") . "<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في اختبار كلمات المرور: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ لا يمكن اختبار كلمات المرور<br>";
}
echo "<br>";

// 4. اختبار الجلسات
echo "<h2>4. اختبار الجلسات</h2>";
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (session_status() == PHP_SESSION_ACTIVE) {
    echo "✅ الجلسات تعمل بشكل صحيح<br>";
    echo "معرف الجلسة: " . session_id() . "<br>";
    
    // اختبار حفظ واسترجاع البيانات
    $_SESSION['test'] = 'test_value';
    if (isset($_SESSION['test']) && $_SESSION['test'] == 'test_value') {
        echo "✅ حفظ واسترجاع بيانات الجلسة يعمل<br>";
        unset($_SESSION['test']);
    } else {
        echo "❌ مشكلة في حفظ بيانات الجلسة<br>";
    }
} else {
    echo "❌ مشكلة في الجلسات<br>";
}
echo "<br>";

// 5. اختبار الدوال
echo "<h2>5. اختبار الدوال المطلوبة</h2>";
$functions = ['isLoggedIn', 'hasRole', 'redirect', 'showMessage', 'formatPrice'];

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "✅ الدالة $func متوفرة<br>";
    } else {
        echo "❌ الدالة $func غير متوفرة<br>";
    }
}
echo "<br>";

// 6. محاولة تسجيل دخول تجريبية
echo "<h2>6. اختبار تسجيل الدخول</h2>";
if (isset($pdo)) {
    try {
        $username = 'admin';
        $password = 'password123';
        
        $stmt = $pdo->prepare("SELECT id, username, password, full_name, role FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "✅ تم العثور على المستخدم: {$user['full_name']}<br>";
            
            if (password_verify($password, $user['password'])) {
                echo "✅ كلمة المرور صحيحة<br>";
                echo "✅ تسجيل الدخول سينجح<br>";
            } else {
                echo "❌ كلمة المرور خاطئة<br>";
                echo "كلمة المرور المحفوظة: " . substr($user['password'], 0, 20) . "...<br>";
            }
        } else {
            echo "❌ لم يتم العثور على المستخدم<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في اختبار تسجيل الدخول: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ لا يمكن اختبار تسجيل الدخول<br>";
}
echo "<br>";

// 7. التوصيات
echo "<h2>7. التوصيات</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b8daff;'>";

if (!file_exists('config/database.php')) {
    echo "🔧 <strong>يجب إعادة التثبيت:</strong><br>";
    echo "• <a href='simple_install.php'>التثبيت السريع</a><br><br>";
}

if (!isset($pdo)) {
    echo "🔧 <strong>مشكلة في قاعدة البيانات:</strong><br>";
    echo "• تحقق من إعدادات MySQL<br>";
    echo "• تأكد من تشغيل خدمة MySQL<br><br>";
}

echo "💡 <strong>روابط مفيدة:</strong><br>";
echo "• <a href='login.php'>صفحة تسجيل الدخول</a><br>";
echo "• <a href='login_safe.php'>صفحة تسجيل الدخول الآمنة</a><br>";
echo "• <a href='simple_install.php'>إعادة التثبيت</a><br>";
echo "• <a href='check_system.php'>فحص النظام الكامل</a><br>";

echo "</div><br>";

// 8. إصلاح سريع لكلمات المرور
if (isset($pdo) && isset($_GET['fix_passwords'])) {
    echo "<h2>8. إصلاح كلمات المرور</h2>";
    try {
        $users_to_fix = [
            ['username' => 'admin', 'password' => 'password123'],
            ['username' => 'cashier', 'password' => 'password123'],
            ['username' => 'kitchen', 'password' => 'password123']
        ];
        
        foreach ($users_to_fix as $user_data) {
            $hashed = password_hash($user_data['password'], PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = ?");
            $stmt->execute([$hashed, $user_data['username']]);
            echo "✅ تم إصلاح كلمة مرور {$user_data['username']}<br>";
        }
        
        echo "<div class='alert alert-success'>تم إصلاح جميع كلمات المرور!</div>";
        
    } catch (Exception $e) {
        echo "❌ خطأ في إصلاح كلمات المرور: " . $e->getMessage() . "<br>";
    }
} else {
    echo "<h2>8. إصلاح كلمات المرور</h2>";
    echo "<a href='?fix_passwords=1' class='btn btn-warning'>إصلاح كلمات المرور</a><br>";
    echo "<small>هذا سيعيد تعيين كلمات المرور لجميع المستخدمين إلى password123</small><br>";
}

echo "<hr>";
echo "<p><small>تم إنشاء التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

table {
    background: white;
    margin: 10px 0;
}

th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

.btn {
    background: #007bff;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
    margin: 5px 0;
}

.btn-warning {
    background: #ffc107;
    color: #000;
}

.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
