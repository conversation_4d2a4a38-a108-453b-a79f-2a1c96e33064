<?php
// ملف تشخيص مشاكل إضافة المنتجات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص مشاكل إضافة المنتجات</h1>";
echo "<hr>";

// التحقق من تسجيل الدخول
if (!file_exists('config/database.php')) {
    echo "<div style='color: red;'>❌ ملف الإعداد غير موجود - <a href='simple_install.php'>إعادة التثبيت</a></div>";
    exit;
}

try {
    require_once 'config/database.php';
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في تحميل الإعداد: " . $e->getMessage() . "</div>";
    exit;
}

// 1. فحص جدول الفئات
echo "<h2>1. فحص جدول الفئات</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
    
    if (empty($categories)) {
        echo "<div style='color: orange;'>⚠️ لا توجد فئات - سيتم إضافة فئات افتراضية</div>";
        
        // إضافة فئات افتراضية
        $default_categories = [
            'المشروبات الساخنة',
            'المشروبات الباردة', 
            'الوجبات الرئيسية',
            'المقبلات',
            'الحلويات'
        ];
        
        foreach ($default_categories as $cat_name) {
            $stmt = $pdo->prepare("INSERT INTO categories (name) VALUES (?)");
            $stmt->execute([$cat_name]);
        }
        
        echo "<div style='color: green;'>✅ تم إضافة الفئات الافتراضية</div>";
        
        // إعادة جلب الفئات
        $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
        $categories = $stmt->fetchAll();
    }
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>اسم الفئة</th></tr>";
    foreach ($categories as $category) {
        echo "<tr><td>{$category['id']}</td><td>{$category['name']}</td></tr>";
    }
    echo "</table>";
    echo "<div style='color: green;'>✅ جدول الفئات يعمل بشكل صحيح</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في جدول الفئات: " . $e->getMessage() . "</div>";
}

echo "<br>";

// 2. فحص جدول المنتجات
echo "<h2>2. فحص جدول المنتجات</h2>";
try {
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // التحقق من وجود عمود image_path
    $has_image_column = false;
    foreach ($columns as $column) {
        if ($column['Field'] == 'image_path') {
            $has_image_column = true;
            break;
        }
    }
    
    if (!$has_image_column) {
        echo "<div style='color: orange;'>⚠️ عمود image_path مفقود - سيتم إضافته</div>";
        $pdo->exec("ALTER TABLE products ADD COLUMN image_path VARCHAR(500)");
        echo "<div style='color: green;'>✅ تم إضافة عمود image_path</div>";
    } else {
        echo "<div style='color: green;'>✅ عمود image_path موجود</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في جدول المنتجات: " . $e->getMessage() . "</div>";
}

echo "<br>";

// 3. فحص مجلد الصور
echo "<h2>3. فحص مجلد الصور</h2>";
$upload_dir = 'uploads/products/';

if (!is_dir($upload_dir)) {
    echo "<div style='color: orange;'>⚠️ مجلد الصور غير موجود - سيتم إنشاؤه</div>";
    if (mkdir($upload_dir, 0755, true)) {
        echo "<div style='color: green;'>✅ تم إنشاء مجلد الصور</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل في إنشاء مجلد الصور</div>";
    }
} else {
    echo "<div style='color: green;'>✅ مجلد الصور موجود</div>";
}

if (is_writable($upload_dir)) {
    echo "<div style='color: green;'>✅ مجلد الصور قابل للكتابة</div>";
} else {
    echo "<div style='color: red;'>❌ مجلد الصور غير قابل للكتابة</div>";
    echo "<div>جرب: chmod 755 uploads/products/</div>";
}

echo "<br>";

// 4. اختبار إضافة منتج
echo "<h2>4. اختبار إضافة منتج</h2>";

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_add'])) {
    try {
        $name = 'منتج تجريبي ' . date('H:i:s');
        $description = 'وصف تجريبي للمنتج';
        $price = 5000;
        $category_id = $categories[0]['id']; // أول فئة متاحة
        
        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id) VALUES (?, ?, ?, ?)");
        $stmt->execute([$name, $description, $price, $category_id]);
        
        echo "<div style='color: green;'>✅ تم إضافة المنتج التجريبي بنجاح!</div>";
        echo "<div>اسم المنتج: $name</div>";
        echo "<div>السعر: " . formatPrice($price) . "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ فشل في إضافة المنتج: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<form method='POST'>";
    echo "<button type='submit' name='test_add' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>اختبار إضافة منتج</button>";
    echo "</form>";
}

echo "<br>";

// 5. عرض المنتجات الموجودة
echo "<h2>5. المنتجات الموجودة</h2>";
try {
    $stmt = $pdo->query("
        SELECT p.*, c.name as category_name 
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        ORDER BY p.id DESC 
        LIMIT 10
    ");
    $products = $stmt->fetchAll();
    
    if (empty($products)) {
        echo "<div style='color: orange;'>⚠️ لا توجد منتجات</div>";
    } else {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>السعر</th><th>الفئة</th><th>الصورة</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>{$product['name']}</td>";
            echo "<td>" . formatPrice($product['price']) . "</td>";
            echo "<td>{$product['category_name']}</td>";
            echo "<td>" . ($product['image_path'] ? 'موجودة' : 'لا توجد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في جلب المنتجات: " . $e->getMessage() . "</div>";
}

echo "<br>";

// 6. فحص إعدادات PHP
echo "<h2>6. إعدادات PHP</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>الإعداد</th><th>القيمة</th></tr>";
echo "<tr><td>upload_max_filesize</td><td>" . ini_get('upload_max_filesize') . "</td></tr>";
echo "<tr><td>post_max_size</td><td>" . ini_get('post_max_size') . "</td></tr>";
echo "<tr><td>max_file_uploads</td><td>" . ini_get('max_file_uploads') . "</td></tr>";
echo "<tr><td>file_uploads</td><td>" . (ini_get('file_uploads') ? 'مفعل' : 'معطل') . "</td></tr>";
echo "</table>";

echo "<br>";

// 7. نصائح الإصلاح
echo "<h2>7. نصائح الإصلاح</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b8daff;'>";
echo "<h4>إذا كانت هناك مشاكل:</h4>";
echo "<ul>";
echo "<li><strong>خطأ في إضافة المنتج:</strong> تحقق من وجود الفئات أولاً</li>";
echo "<li><strong>مشكلة في الصور:</strong> تحقق من صلاحيات مجلد uploads</li>";
echo "<li><strong>خطأ في قاعدة البيانات:</strong> تحقق من وجود عمود image_path</li>";
echo "<li><strong>مشكلة في العملة:</strong> تم تغييرها إلى الدينار العراقي (د.ع)</li>";
echo "</ul>";

echo "<h4>روابط مفيدة:</h4>";
echo "<ul>";
echo "<li><a href='products.php'>صفحة إدارة المنتجات</a></li>";
echo "<li><a href='simple_install.php'>إعادة التثبيت</a></li>";
echo "<li><a href='check_system.php'>فحص النظام الكامل</a></li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><small>تم إنشاء التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

table {
    background: white;
    margin: 10px 0;
}

th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
