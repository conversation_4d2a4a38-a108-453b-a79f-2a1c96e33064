<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$order_id = intval($_GET['id'] ?? 0);

if ($order_id < 1) {
    showMessage('معرف الطلب غير صحيح', 'error');
    redirect('orders.php');
}

try {
    // جلب بيانات الطلب
    $stmt = $pdo->prepare("
        SELECT o.*, t.table_number 
        FROM orders o 
        JOIN tables t ON o.table_id = t.id 
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        showMessage('الطلب غير موجود', 'error');
        redirect('orders.php');
    }
    
    if ($order['status'] == 'completed' || $order['status'] == 'cancelled') {
        showMessage('لا يمكن تعديل طلب مكتمل أو ملغي', 'error');
        redirect('orders.php');
    }
    
    // جلب عناصر الطلب
    $stmt = $pdo->prepare("
        SELECT oi.*, p.name as product_name, p.price as current_price
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?
        ORDER BY oi.id
    ");
    $stmt->execute([$order_id]);
    $order_items = $stmt->fetchAll();
    
    // جلب فئات المنتجات
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
    
    // جلب المنتجات
    $stmt = $pdo->query("
        SELECT p.*, c.name as category_name 
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        ORDER BY c.name, p.name
    ");
    $products = $stmt->fetchAll();
    
} catch (PDOException $e) {
    showMessage('حدث خطأ في جلب البيانات', 'error');
    redirect('orders.php');
}

$page_title = 'تعديل الطلب #' . $order['id'];
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-edit me-2"></i>
        تعديل الطلب #<?php echo $order['id']; ?> - طاولة <?php echo $order['table_number']; ?>
    </h2>
    <div>
        <a href="orders.php" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للطلبات
        </a>
        <button class="btn btn-success" onclick="saveOrder()">
            <i class="fas fa-save me-2"></i>
            حفظ التغييرات
        </button>
    </div>
</div>

<div class="row">
    <!-- عناصر الطلب الحالية -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    عناصر الطلب
                </h5>
            </div>
            <div class="card-body">
                <div id="order-items">
                    <?php foreach ($order_items as $item): ?>
                        <div class="order-item mb-3 p-3 border rounded" data-item-id="<?php echo $item['id']; ?>">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo $item['product_name']; ?></h6>
                                    <small class="text-muted">
                                        السعر: <?php echo formatPrice($item['price']); ?>
                                        <?php if ($item['price'] != $item['current_price']): ?>
                                            <span class="text-warning">(السعر الحالي: <?php echo formatPrice($item['current_price']); ?>)</span>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                
                                <div class="d-flex align-items-center">
                                    <div class="quantity-controls me-3">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(<?php echo $item['id']; ?>, -1)">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control form-control-sm mx-2" 
                                               style="width: 60px;" 
                                               value="<?php echo $item['quantity']; ?>" 
                                               min="1" 
                                               onchange="setQuantity(<?php echo $item['id']; ?>, this.value)">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(<?php echo $item['id']; ?>, 1)">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="text-end me-3">
                                        <strong class="item-total"><?php echo formatPrice($item['price'] * $item['quantity']); ?></strong>
                                    </div>
                                    
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(<?php echo $item['id']; ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between align-items-center">
                    <h5>الإجمالي:</h5>
                    <h5 id="total-amount" class="text-success"><?php echo formatPrice($order['total_amount']); ?></h5>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إضافة منتجات جديدة -->
    <div class="col-md-4">
        <div class="card sticky-top">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة منتجات
                </h5>
            </div>
            <div class="card-body">
                <!-- فلتر الفئات -->
                <div class="mb-3">
                    <select class="form-select" id="category-filter" onchange="filterProducts()">
                        <option value="">جميع الفئات</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- قائمة المنتجات -->
                <div id="products-list" style="max-height: 400px; overflow-y: auto;">
                    <?php foreach ($products as $product): ?>
                        <div class="product-item mb-2 p-2 border rounded" 
                             data-category="<?php echo $product['category_id']; ?>"
                             onclick="addProduct(<?php echo $product['id']; ?>, '<?php echo addslashes($product['name']); ?>', <?php echo $product['price']; ?>)">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?php echo $product['name']; ?></h6>
                                    <small class="text-muted"><?php echo $product['category_name']; ?></small>
                                </div>
                                <strong class="text-success"><?php echo formatPrice($product['price']); ?></strong>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.order-item {
    background: #f8f9fa;
    transition: all 0.3s;
}

.order-item:hover {
    background: #e9ecef;
}

.product-item {
    cursor: pointer;
    transition: all 0.3s;
}

.product-item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
}

.quantity-controls {
    display: flex;
    align-items: center;
}

.quantity-controls input {
    text-align: center;
}
</style>

<script>
let orderItems = <?php echo json_encode(array_column($order_items, null, 'id')); ?>;
let products = <?php echo json_encode(array_column($products, null, 'id')); ?>;

function filterProducts() {
    const categoryId = document.getElementById('category-filter').value;
    const productItems = document.querySelectorAll('.product-item');
    
    productItems.forEach(item => {
        if (categoryId === '' || item.dataset.category === categoryId) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

function addProduct(productId, productName, productPrice) {
    // التحقق من وجود المنتج في الطلب
    const existingItem = Object.values(orderItems).find(item => item.product_id == productId);
    
    if (existingItem) {
        updateQuantity(existingItem.id, 1);
        return;
    }
    
    // إضافة منتج جديد
    const newItemId = 'new_' + Date.now();
    orderItems[newItemId] = {
        id: newItemId,
        product_id: productId,
        product_name: productName,
        price: productPrice,
        quantity: 1,
        is_new: true
    };
    
    renderOrderItems();
    updateTotal();
}

function updateQuantity(itemId, change) {
    console.log('🔄 تحديث الكمية في تعديل الطلب:', itemId, 'التغيير:', change);

    if (!orderItems[itemId]) {
        console.error('❌ العنصر غير موجود:', itemId);
        alert('خطأ: العنصر غير موجود');
        return;
    }

    try {
        const oldQuantity = orderItems[itemId].quantity;
        orderItems[itemId].quantity += change;

        console.log(`📊 الكمية: ${oldQuantity} → ${orderItems[itemId].quantity}`);

        if (orderItems[itemId].quantity < 1) {
            console.log('🗑️ حذف العنصر (كمية < 1)');
            removeItem(itemId);
            return;
        }

        console.log('✅ تم تحديث الكمية بنجاح');
        renderOrderItems();
        updateTotal();

        console.log('📋 حالة الطلب بعد التحديث:', orderItems);

    } catch (error) {
        console.error('❌ خطأ في تحديث الكمية:', error);
        alert('حدث خطأ في تحديث الكمية: ' + error.message);
    }
}

function setQuantity(itemId, quantity) {
    quantity = parseInt(quantity);
    
    if (quantity < 1) {
        removeItem(itemId);
        return;
    }
    
    if (orderItems[itemId]) {
        orderItems[itemId].quantity = quantity;
        renderOrderItems();
        updateTotal();
    }
}

function removeItem(itemId) {
    if (orderItems[itemId]) {
        if (orderItems[itemId].is_new) {
            delete orderItems[itemId];
        } else {
            orderItems[itemId].quantity = 0;
            orderItems[itemId].removed = true;
        }
        
        renderOrderItems();
        updateTotal();
    }
}

function renderOrderItems() {
    const container = document.getElementById('order-items');
    let html = '';
    
    for (const item of Object.values(orderItems)) {
        if (item.removed || item.quantity < 1) continue;
        
        const itemTotal = item.price * item.quantity;
        const isNew = item.is_new ? ' border-success' : '';
        
        html += `
            <div class="order-item mb-3 p-3 border rounded${isNew}" data-item-id="${item.id}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${item.product_name}</h6>
                        <small class="text-muted">السعر: ${formatPrice(item.price)}</small>
                        ${item.is_new ? '<span class="badge bg-success ms-2">جديد</span>' : ''}
                    </div>
                    
                    <div class="d-flex align-items-center">
                        <div class="quantity-controls me-3">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity('${item.id}', -1)">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" class="form-control form-control-sm mx-2" 
                                   style="width: 60px;" 
                                   value="${item.quantity}" 
                                   min="1" 
                                   onchange="setQuantity('${item.id}', this.value)">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity('${item.id}', 1)">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        
                        <div class="text-end me-3">
                            <strong class="item-total">${formatPrice(itemTotal)}</strong>
                        </div>
                        
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem('${item.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

function updateTotal() {
    let total = 0;
    
    for (const item of Object.values(orderItems)) {
        if (!item.removed && item.quantity > 0) {
            total += item.price * item.quantity;
        }
    }
    
    document.getElementById('total-amount').textContent = formatPrice(total);
}

function formatPrice(price) {
    return Math.round(parseFloat(price)) + ' د.ع';
}

function saveOrder() {
    console.log('💾 حفظ تغييرات الطلب...');

    const orderData = {
        order_id: <?php echo $order_id; ?>,
        items: Object.values(orderItems).filter(item => !item.removed && item.quantity > 0)
    };

    console.log('📋 بيانات الطلب للحفظ:', orderData);

    if (orderData.items.length === 0) {
        alert('لا يمكن حفظ طلب فارغ');
        console.warn('⚠️ محاولة حفظ طلب فارغ');
        return;
    }

    fetch('ajax/update_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => {
        console.log('📥 استجابة HTTP:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📋 بيانات الاستجابة:', data);

        if (data.success) {
            alert('تم حفظ التغييرات بنجاح');
            console.log('✅ تم حفظ الطلب بنجاح');
            window.location.href = 'orders.php';
        } else {
            alert('حدث خطأ: ' + data.message);
            console.error('❌ خطأ في حفظ الطلب:', data.message);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في AJAX:', error);
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}
</script>

<?php include 'includes/footer.php'; ?>
