<?php
// إصلاح طارئ للمشاكل
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚨 إصلاح طارئ للمشاكل</h1>";
echo "<hr>";

if (!file_exists('config/database.php')) {
    echo "<div style='color: red;'>❌ ملف الإعداد غير موجود - <a href='simple_install.php'>إعادة التثبيت</a></div>";
    exit;
}

try {
    require_once 'config/database.php';
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

$fixes = [];

// 1. إصلاح مشكلة عمود image_path
echo "<h2>1. إصلاح عمود image_path</h2>";
try {
    // التحقق من وجود العمود
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'image_path'");
    if ($stmt->rowCount() == 0) {
        echo "<div style='color: orange;'>⚠️ عمود image_path غير موجود - سيتم إضافته</div>";
        $pdo->exec("ALTER TABLE products ADD COLUMN image_path VARCHAR(500) NULL");
        echo "<div style='color: green;'>✅ تم إضافة عمود image_path</div>";
        $fixes[] = "إضافة عمود image_path";
    } else {
        echo "<div style='color: green;'>✅ عمود image_path موجود</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في إضافة العمود: " . $e->getMessage() . "</div>";
}

// 2. إصلاح مشكلة الطاولات - التحقق من وجود ملف add_order.php
echo "<h2>2. إصلاح مشكلة الطاولات</h2>";
if (!file_exists('add_order.php')) {
    echo "<div style='color: orange;'>⚠️ ملف add_order.php غير موجود - سيتم إنشاؤه</div>";
    
    $add_order_content = '<?php
require_once \'config/database.php\';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole(\'admin\') && !hasRole(\'cashier\'))) {
    redirect(\'login.php\');
}

$table_id = intval($_GET[\'table_id\'] ?? 0);

if ($table_id < 1) {
    showMessage(\'معرف الطاولة غير صحيح\', \'error\');
    redirect(\'tables.php\');
}

try {
    // جلب بيانات الطاولة
    $stmt = $pdo->prepare("SELECT * FROM tables WHERE id = ?");
    $stmt->execute([$table_id]);
    $table = $stmt->fetch();
    
    if (!$table) {
        showMessage(\'الطاولة غير موجودة\', \'error\');
        redirect(\'tables.php\');
    }
    
    if ($table[\'status\'] != \'available\') {
        showMessage(\'الطاولة غير متاحة\', \'error\');
        redirect(\'tables.php\');
    }
    
    // جلب الفئات
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
    
    // جلب المنتجات
    $stmt = $pdo->query("
        SELECT p.*, c.name as category_name 
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        ORDER BY c.name, p.name
    ");
    $products = $stmt->fetchAll();
    
} catch (PDOException $e) {
    showMessage(\'حدث خطأ في جلب البيانات\', \'error\');
    redirect(\'tables.php\');
}

$page_title = \'إضافة طلب - طاولة \' . $table[\'table_number\'];
include \'includes/header.php\';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-plus me-2"></i>
        إضافة طلب - طاولة <?php echo $table[\'table_number\']; ?>
    </h2>
    <a href="tables.php" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للطاولات
    </a>
</div>

<div class="row">
    <!-- سلة الطلب -->
    <div class="col-md-4">
        <div class="card sticky-top">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    سلة الطلب
                </h5>
            </div>
            <div class="card-body">
                <div id="cart-items">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>السلة فارغة</p>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <strong>الإجمالي:</strong>
                    <strong id="total-amount" class="text-success">0 د.ع</strong>
                </div>
                
                <button id="submit-order" class="btn btn-success w-100" disabled onclick="submitOrder()">
                    <i class="fas fa-check me-2"></i>
                    تأكيد الطلب
                </button>
            </div>
        </div>
    </div>
    
    <!-- المنتجات -->
    <div class="col-md-8">
        <!-- فلتر الفئات -->
        <div class="mb-3">
            <select class="form-select" id="category-filter" onchange="filterProducts()">
                <option value="">جميع الفئات</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?php echo $category[\'id\']; ?>"><?php echo $category[\'name\']; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <!-- قائمة المنتجات -->
        <div class="row" id="products-list">
            <?php foreach ($products as $product): ?>
                <div class="col-md-6 mb-3 product-item" data-category="<?php echo $product[\'category_id\']; ?>">
                    <div class="card h-100 product-card" onclick="addToCart(<?php echo $product[\'id\']; ?>, \'<?php echo addslashes($product[\'name\']); ?>\', <?php echo $product[\'price\']; ?>)">
                        <div class="card-body">
                            <h6 class="card-title"><?php echo $product[\'name\']; ?></h6>
                            <p class="card-text text-muted small"><?php echo $product[\'description\']; ?></p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-secondary"><?php echo $product[\'category_name\']; ?></span>
                                <strong class="text-success"><?php echo formatPrice($product[\'price\']); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<style>
.product-card {
    cursor: pointer;
    transition: all 0.3s;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.cart-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

.cart-item:last-child {
    border-bottom: none;
}
</style>

<script>
let cart = {};
let tableId = <?php echo $table_id; ?>;

function filterProducts() {
    const categoryId = document.getElementById(\'category-filter\').value;
    const productItems = document.querySelectorAll(\'.product-item\');
    
    productItems.forEach(item => {
        if (categoryId === \'\' || item.dataset.category === categoryId) {
            item.style.display = \'block\';
        } else {
            item.style.display = \'none\';
        }
    });
}

function addToCart(productId, productName, productPrice) {
    if (cart[productId]) {
        cart[productId].quantity++;
    } else {
        cart[productId] = {
            id: productId,
            name: productName,
            price: productPrice,
            quantity: 1
        };
    }
    
    updateCartDisplay();
}

function removeFromCart(productId) {
    delete cart[productId];
    updateCartDisplay();
}

function updateQuantity(productId, change) {
    if (cart[productId]) {
        cart[productId].quantity += change;
        
        if (cart[productId].quantity <= 0) {
            removeFromCart(productId);
        } else {
            updateCartDisplay();
        }
    }
}

function updateCartDisplay() {
    const cartItems = document.getElementById(\'cart-items\');
    const totalAmount = document.getElementById(\'total-amount\');
    const submitButton = document.getElementById(\'submit-order\');
    
    if (Object.keys(cart).length === 0) {
        cartItems.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        totalAmount.textContent = \'0 د.ع\';
        submitButton.disabled = true;
        return;
    }
    
    let html = \'\';
    let total = 0;
    
    for (const item of Object.values(cart)) {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        html += `
            <div class="cart-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">${formatPrice(item.price)}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-2">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary" onclick="updateQuantity(${item.id}, -1)">-</button>
                        <span class="btn btn-outline-secondary">${item.quantity}</span>
                        <button class="btn btn-outline-secondary" onclick="updateQuantity(${item.id}, 1)">+</button>
                    </div>
                    <strong>${formatPrice(itemTotal)}</strong>
                </div>
            </div>
        `;
    }
    
    cartItems.innerHTML = html;
    totalAmount.textContent = formatPrice(total);
    submitButton.disabled = false;
}

function formatPrice(price) {
    return Math.round(price) + \' د.ع\';
}

function submitOrder() {
    if (Object.keys(cart).length === 0) {
        alert(\'السلة فارغة\');
        return;
    }
    
    const orderData = {
        table_id: tableId,
        items: Object.values(cart)
    };
    
    fetch(\'ajax/create_order.php\', {
        method: \'POST\',
        headers: {
            \'Content-Type\': \'application/json\',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(\'تم إنشاء الطلب بنجاح\');
            window.location.href = \'tables.php\';
        } else {
            alert(\'حدث خطأ: \' + data.message);
        }
    })
    .catch(error => {
        alert(\'حدث خطأ في الاتصال\');
    });
}
</script>

<?php include \'includes/footer.php\'; ?>';
    
    file_put_contents('add_order.php', $add_order_content);
    echo "<div style='color: green;'>✅ تم إنشاء ملف add_order.php</div>";
    $fixes[] = "إنشاء ملف add_order.php";
} else {
    echo "<div style='color: green;'>✅ ملف add_order.php موجود</div>";
}

// 3. إنشاء مجلد uploads إذا لم يكن موجود
echo "<h2>3. إنشاء مجلد الصور</h2>";
$upload_dir = 'uploads/products/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "<div style='color: green;'>✅ تم إنشاء مجلد الصور</div>";
        $fixes[] = "إنشاء مجلد الصور";
    } else {
        echo "<div style='color: red;'>❌ فشل في إنشاء مجلد الصور</div>";
    }
} else {
    echo "<div style='color: green;'>✅ مجلد الصور موجود</div>";
}

// 4. إضافة فئات افتراضية إذا لم تكن موجودة
echo "<h2>4. إضافة فئات افتراضية</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $categories = [
            'المشروبات الساخنة',
            'المشروبات الباردة',
            'الوجبات الرئيسية',
            'المقبلات',
            'الحلويات'
        ];
        
        foreach ($categories as $cat) {
            $stmt = $pdo->prepare("INSERT INTO categories (name) VALUES (?)");
            $stmt->execute([$cat]);
        }
        
        echo "<div style='color: green;'>✅ تم إضافة " . count($categories) . " فئة افتراضية</div>";
        $fixes[] = "إضافة فئات افتراضية";
    } else {
        echo "<div style='color: green;'>✅ الفئات موجودة ($count فئة)</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في إضافة الفئات: " . $e->getMessage() . "</div>";
}

// 5. اختبار إضافة منتج
echo "<h2>5. اختبار إضافة منتج</h2>";
if (isset($_POST['test_add'])) {
    try {
        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            'منتج تجريبي ' . date('H:i:s'),
            'وصف تجريبي',
            5000,
            1
        ]);
        echo "<div style='color: green;'>✅ تم إضافة منتج تجريبي بنجاح!</div>";
        $fixes[] = "اختبار إضافة منتج نجح";
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ فشل في إضافة المنتج: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<form method='POST'>";
    echo "<button type='submit' name='test_add' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>اختبار إضافة منتج</button>";
    echo "</form>";
}

// عرض النتائج
echo "<hr>";
echo "<h2>📋 ملخص الإصلاحات:</h2>";

if (empty($fixes)) {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ النظام يعمل بشكل صحيح!</h3>";
    echo "<p>لا توجد مشاكل تحتاج إلى إصلاح.</p>";
    echo "</div>";
} else {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ تم تطبيق الإصلاحات التالية:</h3>";
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h2>🔗 روابط الاختبار:</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار إدارة المنتجات</a>";
echo "<a href='tables.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الطاولات</a>";
echo "<a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
echo "</div>";

echo "<hr>";
echo "<p><small>تم الإصلاح في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
