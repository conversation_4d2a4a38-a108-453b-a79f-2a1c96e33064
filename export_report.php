<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('login.php');
}

$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-d');
$report_type = $_GET['report_type'] ?? 'sales';
$export_format = $_GET['export'] ?? 'csv';

// إعداد headers للتحميل
if ($export_format == 'csv') {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="report_' . $report_type . '_' . date('Y-m-d') . '.csv"');
    
    // إضافة BOM للدعم العربي في Excel
    echo "\xEF\xBB\xBF";
}

try {
    if ($report_type == 'sales') {
        // تقرير المبيعات التفصيلي
        $stmt = $pdo->prepare("
            SELECT 
                o.id as order_id,
                DATE(o.created_at) as order_date,
                TIME(o.created_at) as order_time,
                t.table_number,
                u.full_name as cashier,
                o.total_amount,
                o.status,
                p.name as product_name,
                oi.quantity,
                oi.price,
                (oi.quantity * oi.price) as item_total
            FROM orders o
            JOIN tables t ON o.table_id = t.id
            JOIN users u ON o.user_id = u.id
            JOIN order_items oi ON o.id = oi.order_id
            JOIN products p ON oi.product_id = p.id
            WHERE DATE(o.created_at) BETWEEN ? AND ?
            AND o.status = 'completed'
            ORDER BY o.created_at DESC, o.id, oi.id
        ");
        $stmt->execute([$start_date, $end_date]);
        $data = $stmt->fetchAll();
        
        if ($export_format == 'csv') {
            // رؤوس الأعمدة
            echo "رقم الطلب,التاريخ,الوقت,الطاولة,الكاشير,المنتج,الكمية,السعر,الإجمالي,إجمالي الطلب,الحالة\n";
            
            foreach ($data as $row) {
                echo '"' . $row['order_id'] . '",';
                echo '"' . $row['order_date'] . '",';
                echo '"' . $row['order_time'] . '",';
                echo '"' . $row['table_number'] . '",';
                echo '"' . $row['cashier'] . '",';
                echo '"' . $row['product_name'] . '",';
                echo '"' . $row['quantity'] . '",';
                echo '"' . number_format($row['price'], 2) . '",';
                echo '"' . number_format($row['item_total'], 2) . '",';
                echo '"' . number_format($row['total_amount'], 2) . '",';
                echo '"' . $row['status'] . '"';
                echo "\n";
            }
        }
        
    } elseif ($report_type == 'orders') {
        // تقرير الطلبات
        $stmt = $pdo->prepare("
            SELECT 
                o.id,
                DATE(o.created_at) as order_date,
                TIME(o.created_at) as order_time,
                t.table_number,
                u.full_name as cashier,
                o.total_amount,
                o.status,
                COUNT(oi.id) as items_count
            FROM orders o
            JOIN tables t ON o.table_id = t.id
            JOIN users u ON o.user_id = u.id
            LEFT JOIN order_items oi ON o.id = oi.order_id
            WHERE DATE(o.created_at) BETWEEN ? AND ?
            GROUP BY o.id
            ORDER BY o.created_at DESC
        ");
        $stmt->execute([$start_date, $end_date]);
        $data = $stmt->fetchAll();
        
        if ($export_format == 'csv') {
            echo "رقم الطلب,التاريخ,الوقت,الطاولة,الكاشير,عدد العناصر,الإجمالي,الحالة\n";
            
            foreach ($data as $row) {
                $status_text = [
                    'pending' => 'معلق',
                    'processing' => 'قيد التحضير',
                    'completed' => 'مكتمل',
                    'cancelled' => 'ملغي'
                ];
                
                echo '"' . $row['id'] . '",';
                echo '"' . $row['order_date'] . '",';
                echo '"' . $row['order_time'] . '",';
                echo '"' . $row['table_number'] . '",';
                echo '"' . $row['cashier'] . '",';
                echo '"' . $row['items_count'] . '",';
                echo '"' . number_format($row['total_amount'], 2) . '",';
                echo '"' . $status_text[$row['status']] . '"';
                echo "\n";
            }
        }
        
    } elseif ($report_type == 'staff') {
        // تقرير الموظفين
        $stmt = $pdo->prepare("
            SELECT 
                u.full_name,
                u.role,
                COUNT(o.id) as orders_count,
                COALESCE(SUM(o.total_amount), 0) as total_sales,
                COALESCE(AVG(o.total_amount), 0) as avg_order_value
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id 
                AND DATE(o.created_at) BETWEEN ? AND ?
                AND o.status = 'completed'
            WHERE u.role IN ('admin', 'cashier')
            GROUP BY u.id, u.full_name, u.role
            ORDER BY total_sales DESC
        ");
        $stmt->execute([$start_date, $end_date]);
        $data = $stmt->fetchAll();
        
        if ($export_format == 'csv') {
            echo "اسم الموظف,الدور,عدد الطلبات,إجمالي المبيعات,متوسط قيمة الطلب\n";
            
            foreach ($data as $row) {
                $role_text = $row['role'] == 'admin' ? 'مدير' : 'كاشير';
                
                echo '"' . $row['full_name'] . '",';
                echo '"' . $role_text . '",';
                echo '"' . $row['orders_count'] . '",';
                echo '"' . number_format($row['total_sales'], 2) . '",';
                echo '"' . number_format($row['avg_order_value'], 2) . '"';
                echo "\n";
            }
        }
    }
    
} catch (PDOException $e) {
    if ($export_format == 'csv') {
        echo "خطأ في تصدير البيانات\n";
    }
}
?>
