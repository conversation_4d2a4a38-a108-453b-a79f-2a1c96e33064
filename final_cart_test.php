<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي لأزرار سلة الطلب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="alert alert-success">
            <h4><i class="fas fa-tools me-2"></i>تم تحسين أزرار سلة الطلب بشكل شامل!</h4>
            <p><strong>التحسينات الجديدة:</strong></p>
            <ul class="mb-0">
                <li>إضافة تشخيص مفصل لكل عملية</li>
                <li>تحسين دالة updateQuantity مع معالجة أخطاء شاملة</li>
                <li>إضافة console.log مباشر في onclick للأزرار</li>
                <li>فحص شامل للعناصر HTML قبل الاستخدام</li>
                <li>دوال اختبار إضافية للتشخيص</li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>
                            خطوات الاختبار النهائي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">الخطوة 1: افتح الصفحة الحقيقية</h6>
                                <a href="add_order.php?table_id=1" class="btn btn-success mb-3" target="_blank">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    افتح add_order.php?table_id=1
                                </a>
                                
                                <h6 class="text-primary">الخطوة 2: افتح Developer Tools</h6>
                                <div class="alert alert-info">
                                    <strong>اضغط F12</strong> ثم اذهب لتبويب <strong>Console</strong>
                                </div>
                                
                                <h6 class="text-primary">الخطوة 3: أضف منتج للسلة</h6>
                                <p>اضغط على أي منتج من القائمة</p>
                                
                                <h6 class="text-primary">الخطوة 4: اختبر الأزرار</h6>
                                <p>اضغط أزرار + و - وراقب رسائل Console</p>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-success">رسائل Console المتوقعة:</h6>
                                <div class="bg-light p-3 rounded">
                                    <small>
                                        <strong>عند إضافة منتج:</strong><br>
                                        🚀 بدء تحميل صفحة إضافة الطلب<br>
                                        ✅ تم تحميل الصفحة بالكامل<br>
                                        🔄 بدء تحديث عرض السلة...<br><br>
                                        
                                        <strong>عند الضغط على +:</strong><br>
                                        ➕ زيادة كمية منتج 12<br>
                                        🔄 استدعاء updateQuantity: 12 التغيير: 1<br>
                                        📊 الكمية: 1 → 2<br>
                                        ✅ تم تحديث الكمية والعرض بنجاح<br><br>
                                        
                                        <strong>عند الضغط على -:</strong><br>
                                        ➖ تقليل كمية منتج 12<br>
                                        🔄 استدعاء updateQuantity: 12 التغيير: -1<br>
                                        📊 الكمية: 2 → 1<br>
                                        ✅ تم تحديث الكمية والعرض بنجاح
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-bug me-2"></i>
                            أدوات التشخيص
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>يمكنك استخدام هذه الأوامر في Console:</p>
                        
                        <div class="mb-3">
                            <strong>اختبار الأزرار:</strong>
                            <pre class="bg-light p-2 rounded small"><code>testCartButtons()</code></pre>
                        </div>
                        
                        <div class="mb-3">
                            <strong>عرض محتويات السلة:</strong>
                            <pre class="bg-light p-2 rounded small"><code>showCartContents()</code></pre>
                        </div>
                        
                        <div class="mb-3">
                            <strong>إجبار تحديث العرض:</strong>
                            <pre class="bg-light p-2 rounded small"><code>forceUpdateDisplay()</code></pre>
                        </div>
                        
                        <div class="mb-3">
                            <strong>اختبار دالة updateQuantity:</strong>
                            <pre class="bg-light p-2 rounded small"><code>updateQuantity(12, 1)</code></pre>
                        </div>
                        
                        <div class="mb-3">
                            <strong>فحص الدوال:</strong>
                            <pre class="bg-light p-2 rounded small"><code>typeof updateQuantity</code></pre>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">حل المشاكل</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <strong>إذا لم تظهر رسائل Console:</strong>
                            <small class="text-muted d-block">تأكد من فتح تبويب Console في F12</small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>إذا لم تعمل الأزرار:</strong>
                            <small class="text-muted d-block">امسح التخزين المؤقت (Ctrl+F5)</small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>إذا ظهرت أخطاء حمراء:</strong>
                            <small class="text-muted d-block">انسخ الرسالة وأرسلها للمطور</small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>للتأكد من التحديث:</strong>
                            <small class="text-muted d-block">يجب أن تظهر رسائل ➕ و ➖ عند الضغط</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار مباشر -->
        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-play me-2"></i>
                    اختبار مباشر (محاكاة)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="alert alert-info">
                            <strong>هذا اختبار محاكاة للتأكد من أن الكود يعمل:</strong>
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-primary me-2" onclick="simulateAddProduct()">
                                <i class="fas fa-plus me-1"></i>إضافة منتج تجريبي
                            </button>
                            <button class="btn btn-success me-2" onclick="simulateIncreaseQuantity()">
                                <i class="fas fa-plus me-1"></i>محاكاة زر +
                            </button>
                            <button class="btn btn-warning me-2" onclick="simulateDecreaseQuantity()">
                                <i class="fas fa-minus me-1"></i>محاكاة زر -
                            </button>
                            <button class="btn btn-secondary" onclick="clearSimulationLog()">
                                <i class="fas fa-eraser me-1"></i>مسح السجل
                            </button>
                        </div>
                        
                        <div id="simulation-cart" class="border p-3 rounded bg-light">
                            <div class="text-center text-muted">
                                لم يتم إضافة أي منتجات بعد
                            </div>
                        </div>
                        
                        <div class="mt-2">
                            <strong>الإجمالي: <span id="simulation-total">0 د.ع</span></strong>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>سجل المحاكاة:</h6>
                        <div id="simulation-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط إضافية -->
        <div class="text-center mt-4">
            <h5>روابط مفيدة:</h5>
            <div class="btn-group" role="group">
                <a href="add_order.php?table_id=1" class="btn btn-success" target="_blank">
                    <i class="fas fa-shopping-cart me-2"></i>الصفحة الحقيقية
                </a>
                <a href="debug_cart_buttons.php" class="btn btn-info">
                    <i class="fas fa-bug me-2"></i>تشخيص متقدم
                </a>
                <a href="test_add_order.php" class="btn btn-warning">
                    <i class="fas fa-flask me-2"></i>اختبار آخر
                </a>
                <a href="dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <script>
        // محاكاة نظام السلة للاختبار
        let simulationCart = {};
        let simulationProducts = [
            { id: 12, name: 'كباب عراقي', price: 8000 },
            { id: 1, name: 'شاي عراقي', price: 1000 },
            { id: 2, name: 'قهوة عربية', price: 1500 }
        ];
        
        function logSimulation(message, type = 'info') {
            const logDiv = document.getElementById('simulation-log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${icon} ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearSimulationLog() {
            document.getElementById('simulation-log').innerHTML = '';
            logSimulation('تم مسح السجل', 'info');
        }
        
        function simulateAddProduct() {
            const product = simulationProducts[0]; // كباب عراقي
            logSimulation(`➕ إضافة ${product.name} (ID: ${product.id})`, 'info');
            
            if (simulationCart[product.id]) {
                simulationCart[product.id].quantity++;
                logSimulation(`📈 زيادة كمية ${product.name} إلى ${simulationCart[product.id].quantity}`, 'success');
            } else {
                simulationCart[product.id] = {
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    quantity: 1
                };
                logSimulation(`✅ تم إضافة ${product.name} للسلة`, 'success');
            }
            
            updateSimulationDisplay();
        }
        
        function simulateIncreaseQuantity() {
            const productId = 12;
            logSimulation(`➕ زيادة كمية منتج ${productId}`, 'info');
            logSimulation(`🔄 استدعاء updateQuantity(${productId}, 1)`, 'info');
            
            if (!simulationCart[productId]) {
                logSimulation(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
                return;
            }
            
            const oldQuantity = simulationCart[productId].quantity;
            simulationCart[productId].quantity += 1;
            
            logSimulation(`📊 الكمية: ${oldQuantity} → ${simulationCart[productId].quantity}`, 'info');
            logSimulation(`✅ تم تحديث الكمية والعرض بنجاح`, 'success');
            
            updateSimulationDisplay();
        }
        
        function simulateDecreaseQuantity() {
            const productId = 12;
            logSimulation(`➖ تقليل كمية منتج ${productId}`, 'warning');
            logSimulation(`🔄 استدعاء updateQuantity(${productId}, -1)`, 'info');
            
            if (!simulationCart[productId]) {
                logSimulation(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
                return;
            }
            
            const oldQuantity = simulationCart[productId].quantity;
            simulationCart[productId].quantity -= 1;
            
            logSimulation(`📊 الكمية: ${oldQuantity} → ${simulationCart[productId].quantity}`, 'info');
            
            if (simulationCart[productId].quantity <= 0) {
                const productName = simulationCart[productId].name;
                delete simulationCart[productId];
                logSimulation(`🗑️ حذف ${productName} من السلة (كمية <= 0)`, 'warning');
            } else {
                logSimulation(`✅ تم تحديث الكمية والعرض بنجاح`, 'success');
            }
            
            updateSimulationDisplay();
        }
        
        function updateSimulationDisplay() {
            const container = document.getElementById('simulation-cart');
            const totalElement = document.getElementById('simulation-total');
            
            if (Object.keys(simulationCart).length === 0) {
                container.innerHTML = '<div class="text-center text-muted">لم يتم إضافة أي منتجات بعد</div>';
                totalElement.textContent = '0 د.ع';
                return;
            }
            
            let html = '';
            let total = 0;
            
            for (const item of Object.values(simulationCart)) {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                
                html += `
                    <div class="border p-2 rounded mb-2 bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${item.name}</strong><br>
                                <small class="text-muted">${item.price} د.ع</small>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <button class="btn btn-sm btn-outline-secondary" onclick="simulateDecreaseQuantity()">-</button>
                                <span class="px-2">${item.quantity}</span>
                                <button class="btn btn-sm btn-outline-secondary" onclick="simulateIncreaseQuantity()">+</button>
                                <strong class="ms-2">${itemTotal} د.ع</strong>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            totalElement.textContent = total + ' د.ع';
            
            logSimulation(`💰 الإجمالي: ${total} د.ع`, 'info');
            logSimulation(`📦 عدد العناصر: ${Object.keys(simulationCart).length}`, 'info');
        }
        
        // بداية الاختبار
        document.addEventListener('DOMContentLoaded', function() {
            logSimulation('🚀 بدء اختبار محاكاة أزرار السلة', 'info');
            logSimulation('💡 أضف منتج واختبر الأزرار', 'info');
            
            console.log('🎉 تم تحسين أزرار سلة الطلب!');
            console.log('🔗 افتح الصفحة الحقيقية: add_order.php?table_id=1');
            console.log('🧪 اختبر الأزرار وراقب رسائل Console');
        });
    </script>
</body>
</html>
