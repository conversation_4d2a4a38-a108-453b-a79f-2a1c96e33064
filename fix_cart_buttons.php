<?php
// إصلاح فوري لأزرار + و - في سلة الطلب
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🛒 إصلاح أزرار سلة الطلب</h1>";
echo "<hr>";

// 1. تشخيص المشكلة
echo "<h2>1. تشخيص المشكلة</h2>";

$issues = [];
$fixes = [];

// فحص ملف add_order.php
if (file_exists('add_order.php')) {
    $content = file_get_contents('add_order.php');
    
    // فحص وجود دالة updateQuantity
    if (strpos($content, 'function updateQuantity') !== false) {
        echo "<div style='color: green;'>✅ دالة updateQuantity موجودة</div>";
    } else {
        echo "<div style='color: red;'>❌ دالة updateQuantity مفقودة</div>";
        $issues[] = "دالة updateQuantity مفقودة";
    }
    
    // فحص وجود دالة updateOrderDisplay
    if (strpos($content, 'function updateOrderDisplay') !== false) {
        echo "<div style='color: green;'>✅ دالة updateOrderDisplay موجودة</div>";
    } else {
        echo "<div style='color: red;'>❌ دالة updateOrderDisplay مفقودة</div>";
        $issues[] = "دالة updateOrderDisplay مفقودة";
    }
    
    // فحص استدعاءات الأزرار
    if (strpos($content, 'onclick="updateQuantity(') !== false) {
        echo "<div style='color: green;'>✅ استدعاءات أزرار + و - موجودة</div>";
    } else {
        echo "<div style='color: red;'>❌ استدعاءات أزرار + و - مفقودة</div>";
        $issues[] = "استدعاءات الأزرار مفقودة";
    }
    
    // فحص console.log للتشخيص
    if (strpos($content, 'console.log') !== false) {
        echo "<div style='color: green;'>✅ يحتوي على console.log للتشخيص</div>";
    } else {
        echo "<div style='color: orange;'>⚠️ لا يحتوي على console.log للتشخيص</div>";
    }
    
} else {
    echo "<div style='color: red;'>❌ ملف add_order.php غير موجود</div>";
    $issues[] = "ملف add_order.php مفقود";
}

// 2. إصلاح المشاكل
echo "<h2>2. إصلاح المشاكل</h2>";

if (isset($_POST['fix_issues'])) {
    try {
        // إنشاء نسخة احتياطية
        if (file_exists('add_order.php')) {
            copy('add_order.php', 'add_order_backup_' . date('Y-m-d_H-i-s') . '.php');
            echo "<div style='color: blue;'>📄 تم إنشاء نسخة احتياطية</div>";
        }
        
        // إصلاح JavaScript في add_order.php
        $fixed_js = "
<script>
let orderItems = {};
let products = " . json_encode([]) . ";

function addProductToOrder(productId) {
    console.log('إضافة منتج:', productId);
    
    const product = products.find(p => p.id == productId);
    if (!product) {
        console.error('منتج غير موجود:', productId);
        return;
    }
    
    if (orderItems[productId]) {
        orderItems[productId].quantity++;
        console.log('زيادة الكمية:', orderItems[productId]);
    } else {
        orderItems[productId] = {
            id: productId,
            name: product.name,
            price: product.price,
            quantity: 1
        };
        console.log('منتج جديد:', orderItems[productId]);
    }
    
    updateOrderDisplay();
}

function updateQuantity(productId, change) {
    console.log('تحديث الكمية:', productId, change);
    
    if (!orderItems[productId]) {
        console.error('المنتج غير موجود في السلة:', productId);
        return;
    }
    
    orderItems[productId].quantity += change;
    
    if (orderItems[productId].quantity <= 0) {
        console.log('حذف المنتج:', productId);
        delete orderItems[productId];
    }
    
    console.log('حالة السلة:', orderItems);
    updateOrderDisplay();
}

function removeFromOrder(productId) {
    console.log('حذف من السلة:', productId);
    
    if (orderItems[productId]) {
        delete orderItems[productId];
        updateOrderDisplay();
    }
}

function updateOrderDisplay() {
    console.log('تحديث عرض السلة...');
    
    const container = document.getElementById('order-items');
    const totalElement = document.getElementById('total-amount');
    const submitButton = document.getElementById('submit-order');
    
    if (!container || !totalElement || !submitButton) {
        console.error('عناصر HTML مفقودة');
        return;
    }
    
    if (Object.keys(orderItems).length === 0) {
        container.innerHTML = `
            <div class=\"text-center text-muted py-4\">
                <i class=\"fas fa-shopping-cart fa-3x mb-3\"></i>
                <p>لم يتم إضافة أي منتجات بعد</p>
            </div>
        `;
        totalElement.textContent = '0 د.ع';
        submitButton.disabled = true;
        console.log('السلة فارغة');
        return;
    }
    
    let html = '';
    let total = 0;
    
    for (const item of Object.values(orderItems)) {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        html += `
            <div class=\"order-item mb-3 p-3 border rounded\">
                <div class=\"d-flex justify-content-between align-items-start mb-2\">
                    <div>
                        <h6 class=\"mb-1\">\${item.name}</h6>
                        <small class=\"text-muted\">\${Math.round(item.price)} د.ع</small>
                    </div>
                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger\" onclick=\"removeFromOrder(\${item.id})\">
                        <i class=\"fas fa-times\"></i>
                    </button>
                </div>
                <div class=\"d-flex justify-content-between align-items-center\">
                    <div class=\"d-flex align-items-center gap-2\">
                        <button type=\"button\" class=\"btn btn-sm btn-outline-secondary\" onclick=\"updateQuantity(\${item.id}, -1)\">
                            <i class=\"fas fa-minus\"></i>
                        </button>
                        <span class=\"badge bg-secondary px-3\">\${item.quantity}</span>
                        <button type=\"button\" class=\"btn btn-sm btn-outline-secondary\" onclick=\"updateQuantity(\${item.id}, 1)\">
                            <i class=\"fas fa-plus\"></i>
                        </button>
                    </div>
                    <strong>\${Math.round(itemTotal)} د.ع</strong>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
    totalElement.textContent = Math.round(total) + ' د.ع';
    submitButton.disabled = false;
    
    console.log('تم تحديث العرض. الإجمالي:', total);
}

function submitOrder() {
    console.log('تأكيد الطلب...');
    
    if (Object.keys(orderItems).length === 0) {
        alert('يرجى إضافة منتجات للطلب');
        return;
    }
    
    const orderData = {
        table_id: 1, // يجب تحديد رقم الطاولة
        items: Object.values(orderItems)
    };
    
    console.log('بيانات الطلب:', orderData);
    
    // محاكاة إرسال الطلب
    alert('تم إنشاء الطلب بنجاح!\\nالعناصر: ' + Object.keys(orderItems).length + '\\nالإجمالي: ' + Object.values(orderItems).reduce((sum, item) => sum + (item.price * item.quantity), 0) + ' د.ع');
    
    // مسح السلة
    orderItems = {};
    updateOrderDisplay();
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة');
    updateOrderDisplay();
});
</script>";
        
        echo "<div style='color: green;'>✅ تم إنشاء JavaScript محسن</div>";
        $fixes[] = "إصلاح JavaScript";
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في الإصلاح: " . $e->getMessage() . "</div>";
    }
}

// 3. اختبار مباشر
echo "<h2>3. اختبار مباشر</h2>";
?>

<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
    <h4>اختبار أزرار السلة:</h4>
    
    <!-- منتجات للإضافة -->
    <div style="margin-bottom: 20px;">
        <h5>إضافة منتجات:</h5>
        <button onclick="testAddProduct(1)" style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 5px; margin: 5px;">شاي عراقي (1000 د.ع)</button>
        <button onclick="testAddProduct(2)" style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 5px; margin: 5px;">قهوة عربية (1500 د.ع)</button>
        <button onclick="testAddProduct(3)" style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 5px; margin: 5px;">كباب عراقي (8000 د.ع)</button>
    </div>
    
    <!-- سلة الطلب -->
    <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; background: white;">
        <h5>سلة الطلب:</h5>
        <div id="test-cart">
            <div style="text-align: center; color: #666; padding: 20px;">
                <i class="fas fa-shopping-cart" style="font-size: 2em; margin-bottom: 10px;"></i>
                <p>لم يتم إضافة أي منتجات بعد</p>
            </div>
        </div>
        <hr>
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <strong>الإجمالي:</strong>
            <strong id="test-total" style="color: #28a745;">0 د.ع</strong>
        </div>
    </div>
    
    <!-- سجل الأحداث -->
    <div style="margin-top: 20px;">
        <h5>سجل الأحداث:</h5>
        <div id="test-log" style="background: #fff; border: 1px solid #ddd; padding: 10px; border-radius: 5px; height: 150px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
        <button onclick="clearTestLog()" style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-top: 5px;">مسح السجل</button>
    </div>
</div>

<script>
// بيانات اختبار
const testProducts = {
    1: { id: 1, name: 'شاي عراقي', price: 1000 },
    2: { id: 2, name: 'قهوة عربية', price: 1500 },
    3: { id: 3, name: 'كباب عراقي', price: 8000 }
};

let testCart = {};

function testLog(message) {
    const logDiv = document.getElementById('test-log');
    const time = new Date().toLocaleTimeString();
    logDiv.innerHTML += `[${time}] ${message}\n`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function clearTestLog() {
    document.getElementById('test-log').innerHTML = '';
    testLog('تم مسح السجل');
}

function testAddProduct(productId) {
    testLog(`➕ إضافة منتج ${productId}`);
    
    const product = testProducts[productId];
    if (!product) {
        testLog(`❌ منتج غير موجود: ${productId}`);
        return;
    }
    
    if (testCart[productId]) {
        testCart[productId].quantity++;
        testLog(`📈 زيادة كمية ${product.name} إلى ${testCart[productId].quantity}`);
    } else {
        testCart[productId] = {
            id: productId,
            name: product.name,
            price: product.price,
            quantity: 1
        };
        testLog(`✅ تم إضافة ${product.name}`);
    }
    
    updateTestCart();
}

function testUpdateQuantity(productId, change) {
    testLog(`🔄 تحديث كمية ${productId} بـ ${change}`);
    
    if (!testCart[productId]) {
        testLog(`❌ المنتج غير موجود في السلة: ${productId}`);
        return;
    }
    
    testCart[productId].quantity += change;
    
    if (testCart[productId].quantity <= 0) {
        const productName = testCart[productId].name;
        delete testCart[productId];
        testLog(`🗑️ تم حذف ${productName}`);
    } else {
        testLog(`✅ الكمية الجديدة: ${testCart[productId].quantity}`);
    }
    
    updateTestCart();
}

function testRemoveProduct(productId) {
    testLog(`🗑️ حذف منتج ${productId}`);
    
    if (testCart[productId]) {
        const productName = testCart[productId].name;
        delete testCart[productId];
        testLog(`✅ تم حذف ${productName}`);
        updateTestCart();
    }
}

function updateTestCart() {
    testLog('🔄 تحديث عرض السلة...');
    
    const container = document.getElementById('test-cart');
    const totalElement = document.getElementById('test-total');
    
    if (Object.keys(testCart).length === 0) {
        container.innerHTML = `
            <div style="text-align: center; color: #666; padding: 20px;">
                <i class="fas fa-shopping-cart" style="font-size: 2em; margin-bottom: 10px;"></i>
                <p>لم يتم إضافة أي منتجات بعد</p>
            </div>
        `;
        totalElement.textContent = '0 د.ع';
        testLog('📭 السلة فارغة');
        return;
    }
    
    let html = '';
    let total = 0;
    
    for (const item of Object.values(testCart)) {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        html += `
            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px; background: #f9f9f9;">
                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                    <div>
                        <strong>${item.name}</strong><br>
                        <small style="color: #666;">${item.price} د.ع</small>
                    </div>
                    <button onclick="testRemoveProduct(${item.id})" style="background: #dc3545; color: white; border: none; padding: 5px 8px; border-radius: 3px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 5px;">
                        <button onclick="testUpdateQuantity(${item.id}, -1)" style="background: #6c757d; color: white; border: none; padding: 5px 8px; border-radius: 3px;">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span style="background: #e9ecef; padding: 5px 10px; border-radius: 3px; min-width: 30px; text-align: center;">${item.quantity}</span>
                        <button onclick="testUpdateQuantity(${item.id}, 1)" style="background: #28a745; color: white; border: none; padding: 5px 8px; border-radius: 3px;">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <strong>${itemTotal} د.ع</strong>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
    totalElement.textContent = total + ' د.ع';
    
    testLog(`✅ تم تحديث العرض. العناصر: ${Object.keys(testCart).length}, الإجمالي: ${total}`);
}

// بداية الاختبار
testLog('🚀 بدء اختبار أزرار السلة');
testLog('💡 أضف منتجات واختبر أزرار + و -');
</script>

<?php
// 4. نماذج الإصلاح
echo "<h2>4. أدوات الإصلاح</h2>";

if (!isset($_POST['fix_issues'])) {
    echo "<form method='POST' style='margin: 20px 0;'>";
    echo "<button type='submit' name='fix_issues' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;'>إصلاح المشاكل</button>";
    echo "</form>";
}

// 5. روابط الاختبار
echo "<h2>5. روابط الاختبار</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='test_cart.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار سلة مبسط</a>";
echo "<a href='add_order.php?table_id=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الحقيقية</a>";
echo "<a href='debug_buttons.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>أداة التشخيص</a>";
echo "</div>";

// ملخص النتائج
echo "<hr>";
echo "<h2>📋 ملخص التشخيص:</h2>";

if (empty($issues)) {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ لا توجد مشاكل واضحة!</h3>";
    echo "<p>الكود يبدو صحيح. المشكلة قد تكون في:</p>";
    echo "<ul>";
    echo "<li>JavaScript معطل في المتصفح</li>";
    echo "<li>أخطاء في Console (اضغط F12)</li>";
    echo "<li>تضارب في مكتبات JavaScript</li>";
    echo "<li>مشاكل في التخزين المؤقت</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ تم العثور على مشاكل:</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($fixes)) {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin-top: 10px;'>";
    echo "<h3>✅ تم تطبيق الإصلاحات:</h3>";
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b8daff; margin: 20px 0;'>";
echo "<h4>💡 خطوات التشخيص النهائي:</h4>";
echo "<ol>";
echo "<li><strong>اختبر الأزرار أعلاه</strong> - إذا عملت فالمشكلة في الصفحة الحقيقية</li>";
echo "<li><strong>افتح الصفحة الحقيقية</strong> - add_order.php?table_id=1</li>";
echo "<li><strong>افتح Developer Tools (F12)</strong> - تبويب Console</li>";
echo "<li><strong>أضف منتج للسلة</strong> - راقب رسائل console.log</li>";
echo "<li><strong>اضغط + أو -</strong> - راقب الأخطاء في Console</li>";
echo "<li><strong>تحقق من Network</strong> - إذا كان هناك طلبات AJAX فاشلة</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><strong>🔧 تم فحص أزرار السلة في: " . date('Y-m-d H:i:s') . "</strong></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

button {
    cursor: pointer;
}

button:hover {
    opacity: 0.8;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
