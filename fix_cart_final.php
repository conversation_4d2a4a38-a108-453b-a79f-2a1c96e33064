<?php
// إصلاح نهائي لأزرار + و - في سلة الطلب
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🛠️ إصلاح نهائي لأزرار سلة الطلب</h1>";
echo "<hr>";

// 1. فحص ملف add_order.php
echo "<h2>1. فحص ملف add_order.php</h2>";

if (!file_exists('add_order.php')) {
    echo "<div style='color: red;'>❌ ملف add_order.php غير موجود</div>";
    exit;
}

$content = file_get_contents('add_order.php');

// فحص وجود دالة updateQuantity
if (strpos($content, 'function updateQuantity') !== false) {
    echo "<div style='color: green;'>✅ دالة updateQuantity موجودة</div>";
} else {
    echo "<div style='color: red;'>❌ دالة updateQuantity مفقودة</div>";
}

// فحص وجود دالة updateOrderDisplay
if (strpos($content, 'function updateOrderDisplay') !== false) {
    echo "<div style='color: green;'>✅ دالة updateOrderDisplay موجودة</div>";
} else {
    echo "<div style='color: red;'>❌ دالة updateOrderDisplay مفقودة</div>";
}

// فحص وجود متغير orderItems
if (strpos($content, 'let orderItems') !== false || strpos($content, 'var orderItems') !== false) {
    echo "<div style='color: green;'>✅ متغير orderItems موجود</div>";
} else {
    echo "<div style='color: red;'>❌ متغير orderItems مفقود</div>";
}

// فحص وجود أزرار + و -
if (strpos($content, 'onclick="updateQuantity(') !== false) {
    echo "<div style='color: green;'>✅ أزرار + و - موجودة</div>";
} else {
    echo "<div style='color: red;'>❌ أزرار + و - مفقودة</div>";
}

// 2. إنشاء نسخة محسنة من JavaScript
echo "<h2>2. إنشاء JavaScript محسن</h2>";

if (isset($_POST['fix_javascript'])) {
    try {
        // إنشاء نسخة احتياطية
        copy('add_order.php', 'add_order_backup_' . date('Y-m-d_H-i-s') . '.php');
        echo "<div style='color: blue;'>📄 تم إنشاء نسخة احتياطية</div>";
        
        // JavaScript محسن
        $enhanced_js = "
<script>
// متغيرات النظام
let orderItems = {};
let products = " . json_encode([]) . ";

// دالة تسجيل مفصلة
function debugLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`[${timestamp}] ${prefix} ${message}`);
}

// إضافة منتج للطلب
function addProductToOrder(productId) {
    debugLog(`إضافة منتج ${productId} للطلب`, 'info');
    
    const product = products.find(p => p.id == productId);
    if (!product) {
        debugLog(`منتج غير موجود: ${productId}`, 'error');
        return;
    }
    
    if (orderItems[productId]) {
        orderItems[productId].quantity++;
        debugLog(`زيادة كمية ${product.name} إلى ${orderItems[productId].quantity}`, 'success');
    } else {
        orderItems[productId] = {
            id: productId,
            name: product.name,
            price: product.price,
            quantity: 1
        };
        debugLog(`تم إضافة ${product.name} للسلة`, 'success');
    }
    
    debugLog(`حالة orderItems: ${JSON.stringify(orderItems)}`, 'info');
    updateOrderDisplay();
}

// تحديث الكمية - الدالة الرئيسية
function updateQuantity(productId, change) {
    debugLog(`🔄 updateQuantity(${productId}, ${change})`, 'info');
    
    // التحقق من وجود المنتج
    if (!orderItems[productId]) {
        debugLog(`المنتج غير موجود في السلة: ${productId}`, 'error');
        alert('خطأ: المنتج غير موجود في السلة');
        return false;
    }

    try {
        const oldQuantity = orderItems[productId].quantity;
        
        if (typeof change === 'string') {
            // من input field
            const newQuantity = parseInt(change);
            if (newQuantity > 0) {
                orderItems[productId].quantity = newQuantity;
                debugLog(`تحديث من input: ${oldQuantity} → ${newQuantity}`, 'success');
            } else {
                delete orderItems[productId];
                debugLog(`حذف المنتج (كمية 0 من input)`, 'warning');
            }
        } else {
            // من أزرار + أو -
            orderItems[productId].quantity += change;
            debugLog(`تحديث من زر: ${oldQuantity} → ${orderItems[productId].quantity}`, 'info');

            if (orderItems[productId].quantity <= 0) {
                const productName = orderItems[productId].name;
                delete orderItems[productId];
                debugLog(`حذف ${productName} (كمية <= 0)`, 'warning');
            }
        }

        debugLog(`حالة السلة بعد التحديث: ${JSON.stringify(orderItems)}`, 'info');
        updateOrderDisplay();
        return true;
        
    } catch (error) {
        debugLog(`خطأ في updateQuantity: ${error.message}`, 'error');
        alert('حدث خطأ في تحديث الكمية: ' + error.message);
        return false;
    }
}

// حذف عنصر من السلة
function removeItem(productId) {
    debugLog(`حذف منتج ${productId}`, 'warning');
    
    if (orderItems[productId]) {
        const productName = orderItems[productId].name;
        delete orderItems[productId];
        debugLog(`تم حذف ${productName}`, 'success');
        updateOrderDisplay();
    } else {
        debugLog(`المنتج غير موجود للحذف: ${productId}`, 'error');
    }
}

// تحديث عرض السلة
function updateOrderDisplay() {
    debugLog('تحديث عرض السلة...', 'info');

    const container = document.getElementById('order-items');
    const totalElement = document.getElementById('total-amount');
    const submitButton = document.getElementById('submit-order');

    // فحص العناصر المطلوبة
    if (!container) {
        debugLog('عنصر order-items غير موجود', 'error');
        alert('خطأ: عنصر السلة غير موجود');
        return false;
    }
    
    if (!totalElement) {
        debugLog('عنصر total-amount غير موجود', 'error');
        return false;
    }
    
    if (!submitButton) {
        debugLog('عنصر submit-order غير موجود', 'error');
        return false;
    }

    debugLog(`عناصر السلة: ${Object.keys(orderItems).length}`, 'info');
    
    // إذا كانت السلة فارغة
    if (Object.keys(orderItems).length === 0) {
        container.innerHTML = `
            <div class=\"text-center text-muted py-4\">
                <i class=\"fas fa-shopping-cart fa-3x mb-3\"></i>
                <p>لم يتم إضافة أي منتجات بعد</p>
            </div>
        `;
        totalElement.textContent = '0 د.ع';
        submitButton.disabled = true;
        debugLog('السلة فارغة', 'info');
        return true;
    }
    
    // بناء HTML للعناصر
    let html = '';
    let total = 0;
    
    for (const item of Object.values(orderItems)) {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        html += `
            <div class=\"order-item mb-3 p-3 border rounded\">
                <div class=\"d-flex justify-content-between align-items-start mb-2\">
                    <div>
                        <h6 class=\"mb-1\">\${item.name}</h6>
                        <small class=\"text-muted\">\${Math.round(item.price)} د.ع</small>
                    </div>
                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger\" onclick=\"removeItem(\${item.id})\">
                        <i class=\"fas fa-times\"></i>
                    </button>
                </div>
                <div class=\"d-flex justify-content-between align-items-center\">
                    <div class=\"quantity-controls d-flex align-items-center gap-2\">
                        <button type=\"button\" class=\"btn btn-sm btn-outline-secondary\" onclick=\"updateQuantity(\${item.id}, -1)\" title=\"تقليل الكمية\">
                            <i class=\"fas fa-minus\"></i>
                        </button>
                        <span class=\"quantity-display mx-2 px-3 py-1 bg-light border rounded\">\${item.quantity}</span>
                        <button type=\"button\" class=\"btn btn-sm btn-outline-secondary\" onclick=\"updateQuantity(\${item.id}, 1)\" title=\"زيادة الكمية\">
                            <i class=\"fas fa-plus\"></i>
                        </button>
                    </div>
                    <strong>\${Math.round(itemTotal)} د.ع</strong>
                </div>
            </div>
        `;
    }
    
    // تحديث العرض
    container.innerHTML = html;
    totalElement.textContent = Math.round(total) + ' د.ع';
    submitButton.disabled = false;

    debugLog(`تم تحديث العرض - الإجمالي: ${total} د.ع`, 'success');
    return true;
}

// تأكيد الطلب
function submitOrder() {
    debugLog('تأكيد الطلب...', 'info');
    
    if (Object.keys(orderItems).length === 0) {
        alert('يرجى إضافة منتجات للطلب');
        debugLog('محاولة تأكيد طلب فارغ', 'error');
        return;
    }

    const orderData = {
        table_id: " . (isset($_GET['table_id']) ? intval($_GET['table_id']) : 1) . ",
        items: Object.values(orderItems)
    };

    debugLog(`بيانات الطلب: ${JSON.stringify(orderData)}`, 'info');

    fetch('ajax/create_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => {
        debugLog(`استجابة HTTP: ${response.status}`, response.ok ? 'success' : 'error');
        return response.json();
    })
    .then(data => {
        debugLog(`بيانات الاستجابة: ${JSON.stringify(data)}`, 'info');
        
        if (data.success) {
            alert('تم إنشاء الطلب بنجاح');
            debugLog('تم إنشاء الطلب بنجاح', 'success');
            window.location.href = 'tables.php';
        } else {
            alert('حدث خطأ: ' + data.message);
            debugLog(`خطأ في إنشاء الطلب: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        debugLog(`خطأ في AJAX: ${error.message}`, 'error');
        alert('حدث خطأ في الاتصال: ' + error.message);
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    debugLog('تم تحميل الصفحة', 'success');
    
    // فحص العناصر المطلوبة
    const requiredElements = ['order-items', 'total-amount', 'submit-order'];
    const missingElements = requiredElements.filter(id => !document.getElementById(id));
    
    if (missingElements.length > 0) {
        debugLog(`عناصر مفقودة: ${missingElements.join(', ')}`, 'error');
    } else {
        debugLog('جميع العناصر المطلوبة موجودة', 'success');
    }
    
    // تحديث العرض الأولي
    updateOrderDisplay();
    
    debugLog('تم تهيئة النظام بنجاح', 'success');
});

// التقاط الأخطاء
window.onerror = function(msg, url, line, col, error) {
    debugLog(`خطأ JavaScript: ${msg} في السطر ${line}`, 'error');
    return false;
};

window.addEventListener('unhandledrejection', function(event) {
    debugLog(`خطأ Promise: ${event.reason}`, 'error');
});

// دوال اختبار إضافية
function testCartFunctions() {
    debugLog('🧪 اختبار دوال السلة...', 'info');
    
    console.log('orderItems:', orderItems);
    console.log('updateQuantity function:', typeof updateQuantity);
    console.log('updateOrderDisplay function:', typeof updateOrderDisplay);
    
    const elements = {
        'order-items': document.getElementById('order-items'),
        'total-amount': document.getElementById('total-amount'),
        'submit-order': document.getElementById('submit-order')
    };
    
    console.log('HTML elements:', elements);
    
    debugLog('انتهى اختبار الدوال', 'info');
}

// إضافة دالة للوصول العام
window.testCartFunctions = testCartFunctions;
window.debugLog = debugLog;
</script>";
        
        echo "<div style='color: green;'>✅ تم إنشاء JavaScript محسن</div>";
        echo "<div style='color: blue;'>📄 يمكنك نسخ الكود أعلاه واستبداله في add_order.php</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}

// 3. اختبار مباشر
echo "<h2>3. اختبار مباشر</h2>";
?>

<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
    <h4>اختبار أزرار السلة:</h4>
    
    <div id="test-cart-area">
        <div style="margin-bottom: 15px;">
            <button onclick="addTestProduct(1, 'شاي عراقي', 1000)" style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 5px; margin: 5px;">
                إضافة شاي عراقي
            </button>
            <button onclick="addTestProduct(12, 'كباب عراقي', 8000)" style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 5px; margin: 5px;">
                إضافة كباب عراقي
            </button>
        </div>
        
        <div id="test-order-items" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; background: white; min-height: 100px;">
            <div style="text-align: center; color: #666; padding: 20px;">
                لم يتم إضافة أي منتجات بعد
            </div>
        </div>
        
        <div style="margin-top: 15px; display: flex; justify-content: space-between; align-items: center;">
            <strong>الإجمالي: <span id="test-total">0 د.ع</span></strong>
            <button onclick="clearTestCart()" style="background: #6c757d; color: white; border: none; padding: 8px 15px; border-radius: 5px;">
                مسح السلة
            </button>
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h5>سجل الاختبار:</h5>
        <div id="test-log" style="background: #fff; border: 1px solid #ddd; padding: 10px; border-radius: 5px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
        <button onclick="clearTestLog()" style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-top: 5px;">مسح السجل</button>
    </div>
</div>

<script>
// نظام اختبار مستقل
let testOrderItems = {};

function testLog(message) {
    const logDiv = document.getElementById('test-log');
    const time = new Date().toLocaleTimeString();
    logDiv.innerHTML += `[${time}] ${message}\n`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function clearTestLog() {
    document.getElementById('test-log').innerHTML = '';
    testLog('تم مسح السجل');
}

function addTestProduct(id, name, price) {
    testLog(`➕ إضافة ${name} (ID: ${id})`);
    
    if (testOrderItems[id]) {
        testOrderItems[id].quantity++;
        testLog(`📈 زيادة كمية ${name} إلى ${testOrderItems[id].quantity}`);
    } else {
        testOrderItems[id] = { id, name, price, quantity: 1 };
        testLog(`✅ تم إضافة ${name} للسلة`);
    }
    
    updateTestDisplay();
}

function testUpdateQuantity(id, change) {
    testLog(`🔄 تحديث كمية المنتج ${id} بـ ${change}`);
    
    if (!testOrderItems[id]) {
        testLog(`❌ المنتج غير موجود: ${id}`);
        return;
    }
    
    testOrderItems[id].quantity += change;
    
    if (testOrderItems[id].quantity <= 0) {
        const name = testOrderItems[id].name;
        delete testOrderItems[id];
        testLog(`🗑️ تم حذف ${name}`);
    } else {
        testLog(`✅ الكمية الجديدة: ${testOrderItems[id].quantity}`);
    }
    
    updateTestDisplay();
}

function removeTestItem(id) {
    if (testOrderItems[id]) {
        const name = testOrderItems[id].name;
        delete testOrderItems[id];
        testLog(`🗑️ تم حذف ${name}`);
        updateTestDisplay();
    }
}

function updateTestDisplay() {
    const container = document.getElementById('test-order-items');
    const totalElement = document.getElementById('test-total');
    
    if (Object.keys(testOrderItems).length === 0) {
        container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لم يتم إضافة أي منتجات بعد</div>';
        totalElement.textContent = '0 د.ع';
        return;
    }
    
    let html = '';
    let total = 0;
    
    for (const item of Object.values(testOrderItems)) {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        html += `
            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px; background: #f9f9f9;">
                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                    <div>
                        <strong>${item.name}</strong><br>
                        <small style="color: #666;">${item.price} د.ع</small>
                    </div>
                    <button onclick="removeTestItem(${item.id})" style="background: #dc3545; color: white; border: none; padding: 5px 8px; border-radius: 3px;">
                        ✕
                    </button>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 5px;">
                        <button onclick="testUpdateQuantity(${item.id}, -1)" style="background: #6c757d; color: white; border: none; padding: 5px 8px; border-radius: 3px;">
                            -
                        </button>
                        <span style="background: #e9ecef; padding: 5px 10px; border-radius: 3px; min-width: 30px; text-align: center;">${item.quantity}</span>
                        <button onclick="testUpdateQuantity(${item.id}, 1)" style="background: #28a745; color: white; border: none; padding: 5px 8px; border-radius: 3px;">
                            +
                        </button>
                    </div>
                    <strong>${itemTotal} د.ع</strong>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
    totalElement.textContent = total + ' د.ع';
    
    testLog(`✅ تم تحديث العرض - العناصر: ${Object.keys(testOrderItems).length}, الإجمالي: ${total}`);
}

function clearTestCart() {
    testOrderItems = {};
    updateTestDisplay();
    testLog('🧹 تم مسح السلة');
}

// بداية الاختبار
testLog('🚀 بدء اختبار أزرار السلة');
testLog('💡 أضف منتجات واختبر أزرار + و -');
</script>

<?php
// 4. نماذج الإصلاح
echo "<h2>4. أدوات الإصلاح</h2>";

if (!isset($_POST['fix_javascript'])) {
    echo "<form method='POST' style='margin: 20px 0;'>";
    echo "<button type='submit' name='fix_javascript' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;'>إنشاء JavaScript محسن</button>";
    echo "</form>";
}

// 5. روابط الاختبار
echo "<h2>5. روابط الاختبار</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='debug_cart_buttons.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة تشخيص متقدمة</a>";
echo "<a href='add_order.php?table_id=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الحقيقية</a>";
echo "<a href='test_add_order.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة اختبار أخرى</a>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b8daff; margin: 20px 0;'>";
echo "<h4>💡 خطوات الإصلاح النهائي:</h4>";
echo "<ol>";
echo "<li><strong>اختبر الأزرار أعلاه</strong> - إذا عملت فالمشكلة في الصفحة الأصلية</li>";
echo "<li><strong>انسخ JavaScript المحسن</strong> واستبدله في add_order.php</li>";
echo "<li><strong>افتح الصفحة الحقيقية</strong> واختبر مرة أخرى</li>";
echo "<li><strong>افتح Console (F12)</strong> وراقب رسائل debugLog</li>";
echo "<li><strong>إذا لم تعمل</strong> استخدم صفحة التشخيص المتقدمة</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><strong>🔧 تم إنشاء الإصلاح النهائي في: " . date('Y-m-d H:i:s') . "</strong></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

button {
    cursor: pointer;
}

button:hover {
    opacity: 0.8;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
