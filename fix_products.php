<?php
// ملف إصلاح مشاكل المنتجات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح مشاكل المنتجات</h1>";
echo "<hr>";

if (!file_exists('config/database.php')) {
    echo "<div style='color: red;'>❌ ملف الإعداد غير موجود - <a href='simple_install.php'>إعادة التثبيت</a></div>";
    exit;
}

try {
    require_once 'config/database.php';
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

$fixes_applied = [];

// 1. إضافة عمود image_path إذا لم يكن موجود
try {
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'image_path'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE products ADD COLUMN image_path VARCHAR(500)");
        $fixes_applied[] = "تم إضافة عمود image_path لجدول المنتجات";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في إضافة عمود الصورة: " . $e->getMessage() . "</div>";
}

// 2. إنشاء مجلد الصور
$upload_dir = 'uploads/products/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        $fixes_applied[] = "تم إنشاء مجلد الصور: $upload_dir";
    }
}

// 3. إضافة فئات افتراضية إذا لم تكن موجودة
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $default_categories = [
            'المشروبات الساخنة',
            'المشروبات الباردة', 
            'الوجبات الرئيسية',
            'المقبلات',
            'الحلويات',
            'السلطات',
            'المشاوي',
            'الأطباق الشعبية'
        ];
        
        foreach ($default_categories as $cat_name) {
            $stmt = $pdo->prepare("INSERT INTO categories (name) VALUES (?)");
            $stmt->execute([$cat_name]);
        }
        
        $fixes_applied[] = "تم إضافة " . count($default_categories) . " فئة افتراضية";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في إضافة الفئات: " . $e->getMessage() . "</div>";
}

// 4. إضافة منتجات تجريبية
if (isset($_GET['add_sample_products'])) {
    try {
        // جلب الفئات
        $stmt = $pdo->query("SELECT * FROM categories LIMIT 5");
        $categories = $stmt->fetchAll();
        
        $sample_products = [
            ['name' => 'شاي عراقي', 'description' => 'شاي عراقي أصيل', 'price' => 1000, 'category' => 0],
            ['name' => 'قهوة عربية', 'description' => 'قهوة عربية مميزة', 'price' => 1500, 'category' => 0],
            ['name' => 'عصير برتقال', 'description' => 'عصير برتقال طازج', 'price' => 2000, 'category' => 1],
            ['name' => 'كباب عراقي', 'description' => 'كباب عراقي مشوي', 'price' => 8000, 'category' => 2],
            ['name' => 'دولمة', 'description' => 'دولمة عراقية تقليدية', 'price' => 6000, 'category' => 2],
            ['name' => 'حمص', 'description' => 'حمص بالطحينة', 'price' => 3000, 'category' => 3],
            ['name' => 'كنافة', 'description' => 'كنافة بالجبن', 'price' => 4000, 'category' => 4],
            ['name' => 'مهلبية', 'description' => 'مهلبية بالفستق', 'price' => 3500, 'category' => 4]
        ];
        
        foreach ($sample_products as $product) {
            if (isset($categories[$product['category']])) {
                $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id) VALUES (?, ?, ?, ?)");
                $stmt->execute([
                    $product['name'],
                    $product['description'],
                    $product['price'],
                    $categories[$product['category']]['id']
                ]);
            }
        }
        
        $fixes_applied[] = "تم إضافة " . count($sample_products) . " منتج تجريبي";
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في إضافة المنتجات التجريبية: " . $e->getMessage() . "</div>";
    }
}

// 5. تحديث دالة formatPrice في قاعدة البيانات
try {
    $config_file = 'config/database.php';
    $config_content = file_get_contents($config_file);
    
    if (strpos($config_content, 'ريال') !== false) {
        $config_content = str_replace(
            "return number_format(\$price, 2) . ' ريال';",
            "return number_format(\$price, 0) . ' د.ع';",
            $config_content
        );
        
        file_put_contents($config_file, $config_content);
        $fixes_applied[] = "تم تحديث العملة إلى الدينار العراقي";
    }
} catch (Exception $e) {
    echo "<div style='color: orange;'>⚠️ تحذير: لم يتم تحديث العملة تلقائياً</div>";
}

// عرض النتائج
echo "<h2>نتائج الإصلاح:</h2>";

if (empty($fixes_applied)) {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ النظام يعمل بشكل صحيح!</h3>";
    echo "<p>لا توجد مشاكل تحتاج إلى إصلاح.</p>";
    echo "</div>";
} else {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ تم تطبيق الإصلاحات التالية:</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// إحصائيات النظام
echo "<h2>إحصائيات النظام:</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $categories_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $products_count = $stmt->fetchColumn();
    
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; background: white;'>";
    echo "<tr><th>العنصر</th><th>العدد</th></tr>";
    echo "<tr><td>الفئات</td><td>$categories_count</td></tr>";
    echo "<tr><td>المنتجات</td><td>$products_count</td></tr>";
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في جلب الإحصائيات: " . $e->getMessage() . "</div>";
}

// أزرار الإجراءات
echo "<h2>إجراءات إضافية:</h2>";
echo "<div style='margin: 20px 0;'>";

if (!isset($_GET['add_sample_products'])) {
    echo "<a href='?add_sample_products=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة منتجات تجريبية</a>";
}

echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة المنتجات</a>";
echo "<a href='debug_products.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص المشاكل</a>";
echo "<a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
echo "</div>";

// اختبار إضافة منتج
echo "<h2>اختبار إضافة منتج:</h2>";
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_product'])) {
    try {
        $name = trim($_POST['name']);
        $price = floatval($_POST['price']);
        $category_id = intval($_POST['category_id']);
        
        if (empty($name) || $price <= 0 || $category_id <= 0) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id) VALUES (?, ?, ?, ?)");
        $stmt->execute([$name, 'منتج تجريبي', $price, $category_id]);
        
        echo "<div style='color: green; background: #d4edda; padding: 10px; border-radius: 5px;'>";
        echo "✅ تم إضافة المنتج '$name' بنجاح!";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red; background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo "❌ خطأ: " . $e->getMessage();
        echo "</div>";
    }
}

// نموذج اختبار
try {
    $stmt = $pdo->query("SELECT * FROM categories LIMIT 5");
    $categories = $stmt->fetchAll();
    
    if (!empty($categories)) {
        echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<h4>اختبار إضافة منتج:</h4>";
        echo "<div style='margin: 10px 0;'>";
        echo "<label>اسم المنتج:</label><br>";
        echo "<input type='text' name='name' value='منتج تجريبي " . date('H:i') . "' required style='width: 200px; padding: 5px;'>";
        echo "</div>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label>السعر:</label><br>";
        echo "<input type='number' name='price' value='5000' required style='width: 200px; padding: 5px;'>";
        echo "</div>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label>الفئة:</label><br>";
        echo "<select name='category_id' required style='width: 200px; padding: 5px;'>";
        foreach ($categories as $category) {
            echo "<option value='{$category['id']}'>{$category['name']}</option>";
        }
        echo "</select>";
        echo "</div>";
        
        echo "<button type='submit' name='test_product' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>إضافة منتج تجريبي</button>";
        echo "</form>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ لا يمكن عرض نموذج الاختبار: " . $e->getMessage() . "</div>";
}

echo "<hr>";
echo "<p><small>تم إنشاء التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

table {
    margin: 10px 0;
}

th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
