<?php
/**
 * نظام فحص التفعيل والترخيص
 * Activation and License Check System
 */

class ActivationManager {
    private $pdo;
    private $settings = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->loadSettings();
    }
    
    /**
     * تحميل إعدادات النظام
     */
    private function loadSettings() {
        try {
            $stmt = $this->pdo->prepare("SELECT setting_key, setting_value FROM system_settings");
            $stmt->execute();
            
            while ($row = $stmt->fetch()) {
                $this->settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (Exception $e) {
            // إذا لم يتم إعداد النظام بعد
            $this->settings = [];
        }
    }
    
    /**
     * فحص حالة التفعيل
     */
    public function checkActivationStatus() {
        if (empty($this->settings)) {
            return [
                'status' => 'not_setup',
                'message' => 'نظام التفعيل غير مُعد',
                'can_use' => false,
                'setup_url' => 'setup_activation_system.php'
            ];
        }
        
        $status = $this->settings['system_status'] ?? 'not_setup';
        $current_orders = intval($this->settings['current_orders_count'] ?? 0);
        $max_orders = intval($this->settings['trial_orders_limit'] ?? 10);
        
        switch ($status) {
            case 'trial':
                return $this->checkTrialStatus($current_orders, $max_orders);
                
            case 'active':
                return $this->checkFullLicense();
                
            case 'expired':
                return [
                    'status' => 'expired',
                    'message' => 'انتهت صلاحية الترخيص',
                    'can_use' => false,
                    'activation_url' => 'activation_manager.php'
                ];
                
            default:
                return [
                    'status' => 'unknown',
                    'message' => 'حالة غير معروفة',
                    'can_use' => false
                ];
        }
    }
    
    /**
     * فحص حالة النسخة التجريبية
     */
    private function checkTrialStatus($current_orders, $max_orders) {
        // فحص عدد الطلبات
        if ($current_orders >= $max_orders) {
            $this->updateSystemStatus('expired');
            return [
                'status' => 'trial_expired',
                'message' => "تم استنفاد الطلبات التجريبية ({$current_orders}/{$max_orders})",
                'can_use' => false,
                'remaining_orders' => 0,
                'activation_url' => 'activation_manager.php'
            ];
        }
        
        // فحص تاريخ انتهاء التجربة
        $license_key = $this->settings['license_key'] ?? null;
        if ($license_key) {
            $stmt = $this->pdo->prepare("SELECT expires_at FROM activation_licenses WHERE license_key = ?");
            $stmt->execute([$license_key]);
            $license = $stmt->fetch();
            
            if ($license && strtotime($license['expires_at']) < time()) {
                $this->updateSystemStatus('expired');
                return [
                    'status' => 'trial_expired',
                    'message' => 'انتهت مدة التجربة المجانية',
                    'can_use' => false,
                    'activation_url' => 'activation_manager.php'
                ];
            }
        }
        
        $remaining_orders = $max_orders - $current_orders;
        return [
            'status' => 'trial_active',
            'message' => "النسخة التجريبية نشطة ({$current_orders}/{$max_orders})",
            'can_use' => true,
            'remaining_orders' => $remaining_orders,
            'is_trial' => true
        ];
    }
    
    /**
     * فحص الترخيص الكامل
     */
    private function checkFullLicense() {
        $license_key = $this->settings['license_key'] ?? null;
        
        if (!$license_key) {
            return [
                'status' => 'no_license',
                'message' => 'لا يوجد ترخيص',
                'can_use' => false
            ];
        }
        
        $stmt = $this->pdo->prepare("
            SELECT * FROM activation_licenses 
            WHERE license_key = ? AND is_active = 1
        ");
        $stmt->execute([$license_key]);
        $license = $stmt->fetch();
        
        if (!$license) {
            return [
                'status' => 'invalid_license',
                'message' => 'ترخيص غير صحيح',
                'can_use' => false
            ];
        }
        
        // فحص انتهاء الصلاحية (إذا كان محدد)
        if ($license['expires_at'] && strtotime($license['expires_at']) < time()) {
            $this->updateSystemStatus('expired');
            return [
                'status' => 'license_expired',
                'message' => 'انتهت صلاحية الترخيص',
                'can_use' => false,
                'expired_at' => $license['expires_at']
            ];
        }
        
        return [
            'status' => 'active',
            'message' => 'الترخيص نشط',
            'can_use' => true,
            'license_type' => $license['license_type'],
            'expires_at' => $license['expires_at']
        ];
    }
    
    /**
     * تحديث حالة النظام
     */
    private function updateSystemStatus($status) {
        $stmt = $this->pdo->prepare("
            UPDATE system_settings 
            SET setting_value = ? 
            WHERE setting_key = 'system_status'
        ");
        $stmt->execute([$status]);
        $this->settings['system_status'] = $status;
    }
    
    /**
     * تسجيل استخدام طلب جديد
     */
    public function recordOrderUsage($user_id, $order_id) {
        try {
            // تسجيل في جدول الاستخدام
            $stmt = $this->pdo->prepare("
                INSERT INTO system_usage (usage_type, user_id, description) 
                VALUES ('order', ?, ?)
            ");
            $stmt->execute([$user_id, "إنشاء طلب #{$order_id}"]);
            
            // تحديث العداد
            $current_count = intval($this->settings['current_orders_count'] ?? 0);
            $new_count = $current_count + 1;
            
            $stmt = $this->pdo->prepare("
                UPDATE system_settings 
                SET setting_value = ? 
                WHERE setting_key = 'current_orders_count'
            ");
            $stmt->execute([$new_count]);
            
            $this->settings['current_orders_count'] = $new_count;
            
            return true;
        } catch (Exception $e) {
            error_log("Error recording order usage: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * فحص إمكانية إنشاء طلب جديد
     */
    public function canCreateOrder() {
        $status = $this->checkActivationStatus();
        
        if (!$status['can_use']) {
            return [
                'allowed' => false,
                'message' => $status['message'],
                'redirect_url' => $status['activation_url'] ?? null
            ];
        }
        
        // إذا كانت نسخة تجريبية، فحص العدد المتبقي
        if (isset($status['is_trial']) && $status['is_trial']) {
            if ($status['remaining_orders'] <= 0) {
                return [
                    'allowed' => false,
                    'message' => 'تم استنفاد عدد الطلبات التجريبية',
                    'redirect_url' => 'activation_manager.php'
                ];
            }
        }
        
        return [
            'allowed' => true,
            'remaining_orders' => $status['remaining_orders'] ?? null
        ];
    }
    
    /**
     * الحصول على معلومات الاستخدام
     */
    public function getUsageStats() {
        $stats = [];
        
        // إحصائيات الطلبات
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as total_orders 
            FROM system_usage 
            WHERE usage_type = 'order'
        ");
        $stmt->execute();
        $stats['total_orders'] = $stmt->fetchColumn();
        
        // إحصائيات اليوم
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as today_orders 
            FROM system_usage 
            WHERE usage_type = 'order' AND DATE(created_at) = CURDATE()
        ");
        $stmt->execute();
        $stats['today_orders'] = $stmt->fetchColumn();
        
        // إحصائيات المستخدمين
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as total_users FROM users");
        $stmt->execute();
        $stats['total_users'] = $stmt->fetchColumn();
        
        return $stats;
    }
    
    /**
     * إنشاء تحذير للمستخدم
     */
    public function getActivationWarning() {
        $status = $this->checkActivationStatus();
        
        if (!$status['can_use']) {
            return [
                'type' => 'danger',
                'title' => 'تحذير: النظام غير مفعل',
                'message' => $status['message'],
                'action_url' => $status['activation_url'] ?? $status['setup_url'] ?? null,
                'action_text' => 'تفعيل النظام'
            ];
        }
        
        if (isset($status['is_trial']) && $status['is_trial']) {
            $remaining = $status['remaining_orders'];
            
            if ($remaining <= 2) {
                return [
                    'type' => 'warning',
                    'title' => 'تحذير: قارب انتهاء النسخة التجريبية',
                    'message' => "متبقي {$remaining} طلبات فقط من النسخة التجريبية",
                    'action_url' => 'activation_manager.php',
                    'action_text' => 'تفعيل النسخة الكاملة'
                ];
            } elseif ($remaining <= 5) {
                return [
                    'type' => 'info',
                    'title' => 'تذكير: النسخة التجريبية',
                    'message' => "متبقي {$remaining} طلبات من النسخة التجريبية",
                    'action_url' => 'activation_manager.php',
                    'action_text' => 'ترقية النظام'
                ];
            }
        }
        
        return null;
    }
}

// إنشاء مثيل عام لمدير التفعيل
if (isset($pdo)) {
    $activationManager = new ActivationManager($pdo);
}

/**
 * دالة مساعدة للتحقق من التفعيل
 */
function checkSystemActivation() {
    global $activationManager;
    
    if (!isset($activationManager)) {
        return [
            'status' => 'not_setup',
            'can_use' => false,
            'message' => 'نظام التفعيل غير مُعد'
        ];
    }
    
    return $activationManager->checkActivationStatus();
}

/**
 * دالة للتحقق من إمكانية إنشاء طلب
 */
function canCreateNewOrder() {
    global $activationManager;
    
    if (!isset($activationManager)) {
        return ['allowed' => false, 'message' => 'نظام التفعيل غير مُعد'];
    }
    
    return $activationManager->canCreateOrder();
}

/**
 * دالة لتسجيل استخدام طلب
 */
function recordOrderUsage($user_id, $order_id) {
    global $activationManager;
    
    if (!isset($activationManager)) {
        return false;
    }
    
    return $activationManager->recordOrderUsage($user_id, $order_id);
}

/**
 * دالة للحصول على تحذير التفعيل
 */
function getActivationWarning() {
    global $activationManager;
    
    if (!isset($activationManager)) {
        return null;
    }
    
    return $activationManager->getActivationWarning();
}
?>
