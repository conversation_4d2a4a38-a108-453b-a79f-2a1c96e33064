    <?php if (isLoggedIn()): ?>
            </div> <!-- End main-content -->
        </div> <!-- End row -->
    </div> <!-- End container-fluid -->
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تحديث الوقت كل ثانية
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }
        
        // تحديث الوقت عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
        });
        
        // دالة لتأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }
        
        // دالة لطباعة الفاتورة
        function printInvoice(orderId) {
            window.open('print_invoice.php?order_id=' + orderId, '_blank', 'width=800,height=600');
        }
        
        // دالة لتحديث حالة الطلب
        function updateOrderStatus(orderId, status) {
            $.ajax({
                url: 'ajax/update_order_status.php',
                method: 'POST',
                data: {
                    order_id: orderId,
                    status: status
                },
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ في الاتصال');
                }
            });
        }
        
        // دالة لتحديث حالة عنصر الطلب
        function updateOrderItemStatus(itemId, status) {
            $.ajax({
                url: 'ajax/update_order_item_status.php',
                method: 'POST',
                data: {
                    item_id: itemId,
                    status: status
                },
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ في الاتصال');
                }
            });
        }
        
        // دالة لإضافة منتج إلى الطلب
        function addToOrder(tableId, productId, quantity = 1) {
            $.ajax({
                url: 'ajax/add_to_order.php',
                method: 'POST',
                data: {
                    table_id: tableId,
                    product_id: productId,
                    quantity: quantity
                },
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ في الاتصال');
                }
            });
        }
        
        // دالة لتحديث كمية المنتج
        function updateQuantity(itemId, quantity) {
            if (quantity < 1) {
                if (confirm('هل تريد حذف هذا العنصر من الطلب؟')) {
                    removeFromOrder(itemId);
                }
                return;
            }
            
            $.ajax({
                url: 'ajax/update_quantity.php',
                method: 'POST',
                data: {
                    item_id: itemId,
                    quantity: quantity
                },
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ في الاتصال');
                }
            });
        }
        
        // دالة لحذف عنصر من الطلب
        function removeFromOrder(itemId) {
            $.ajax({
                url: 'ajax/remove_from_order.php',
                method: 'POST',
                data: {
                    item_id: itemId
                },
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ في الاتصال');
                }
            });
        }
        
        // تحديث تلقائي للصفحات كل 30 ثانية
        if (window.location.pathname.includes('kitchen.php') || window.location.pathname.includes('dashboard.php')) {
            setInterval(function() {
                location.reload();
            }, 30000);
        }
    </script>
</body>
</html>
