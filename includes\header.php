<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'نظام إدارة المطعم'; ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 600;
        }
        
        .table-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        
        .table-card:hover {
            transform: translateY(-5px);
        }
        
        .table-available {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .table-occupied {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        }
        
        .sidebar .nav-link {
            color: white;
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        
        .order-item {
            border-right: 4px solid #007bff;
            margin-bottom: 10px;
        }
        
        .kitchen-order {
            border-right: 4px solid #28a745;
        }
        
        .kitchen-order.preparing {
            border-right-color: #ffc107;
        }
        
        .kitchen-order.ready {
            border-right-color: #28a745;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <?php if (isLoggedIn()): ?>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i>
                نظام إدارة المطعم
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user-cog me-2"></i>الملف الشخصي
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-3">
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    
                    <?php if (hasRole('admin') || hasRole('cashier')): ?>
                    <a class="nav-link" href="tables.php">
                        <i class="fas fa-table me-2"></i>
                        إدارة الطاولات
                    </a>
                    
                    <a class="nav-link" href="orders.php">
                        <i class="fas fa-shopping-cart me-2"></i>
                        الطلبات
                    </a>
                    
                    <a class="nav-link" href="cashier.php">
                        <i class="fas fa-cash-register me-2"></i>
                        الكاشير
                    </a>
                    <?php endif; ?>
                    
                    <?php if (hasRole('kitchen')): ?>
                    <a class="nav-link" href="kitchen.php">
                        <i class="fas fa-fire me-2"></i>
                        المطبخ
                    </a>
                    <?php endif; ?>
                    
                    <?php if (hasRole('admin')): ?>
                    <a class="nav-link" href="products.php">
                        <i class="fas fa-box me-2"></i>
                        المنتجات
                    </a>
                    
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>
                        المستخدمين
                    </a>
                    
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                    <?php endif; ?>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <?php displayMessage(); ?>
    <?php endif; ?>
