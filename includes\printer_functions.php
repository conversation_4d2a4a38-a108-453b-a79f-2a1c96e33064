<?php
/**
 * نظام الطباعة للمطبخ والكاشير - طابعات 80 ملم
 * دعم ESC/POS Commands للطابعات الحرارية
 */

class RestaurantPrinter {
    private $printer_ip;
    private $printer_port;
    private $printer_type;
    
    // أوامر ESC/POS للطابعات الحرارية
    const ESC = "\x1B";
    const GS = "\x1D";
    const LF = "\x0A";
    const CR = "\x0D";
    const FF = "\x0C";
    
    // أوامر التنسيق
    const INIT = "\x1B\x40";                    // تهيئة الطابعة
    const BOLD_ON = "\x1B\x45\x01";            // خط عريض
    const BOLD_OFF = "\x1B\x45\x00";           // إيقاف الخط العريض
    const UNDERLINE_ON = "\x1B\x2D\x01";       // خط تحت
    const UNDERLINE_OFF = "\x1B\x2D\x00";      // إيقاف الخط تحت
    const CENTER = "\x1B\x61\x01";             // توسيط
    const LEFT = "\x1B\x61\x00";               // محاذاة يسار
    const RIGHT = "\x1B\x61\x02";              // محاذاة يمين
    const CUT = "\x1D\x56\x00";                // قطع الورق
    const DRAWER = "\x1B\x70\x00\x19\xFA";     // فتح الدرج
    
    // أحجام الخط
    const FONT_SMALL = "\x1B\x21\x00";         // خط صغير
    const FONT_NORMAL = "\x1B\x21\x01";        // خط عادي
    const FONT_LARGE = "\x1B\x21\x10";         // خط كبير
    const FONT_XLARGE = "\x1B\x21\x30";        // خط كبير جداً
    
    public function __construct($printer_ip = '*************', $printer_port = 9100, $printer_type = 'kitchen') {
        $this->printer_ip = $printer_ip;
        $this->printer_port = $printer_port;
        $this->printer_type = $printer_type;
    }
    
    /**
     * إرسال البيانات للطابعة
     */
    private function sendToPrinter($data) {
        try {
            // محاولة الاتصال بالطابعة عبر الشبكة
            $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
            if (!$socket) {
                throw new Exception('فشل في إنشاء Socket');
            }
            
            $result = socket_connect($socket, $this->printer_ip, $this->printer_port);
            if (!$result) {
                throw new Exception('فشل في الاتصال بالطابعة');
            }
            
            socket_write($socket, $data, strlen($data));
            socket_close($socket);
            
            return true;
            
        } catch (Exception $e) {
            error_log('خطأ في الطباعة: ' . $e->getMessage());
            
            // محاولة الطباعة عبر USB أو Serial
            return $this->printViaUSB($data);
        }
    }
    
    /**
     * طباعة عبر USB (للطابعات المحلية)
     */
    private function printViaUSB($data) {
        try {
            // للويندوز
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                $printer_name = 'POS-80'; // اسم الطابعة في الويندوز
                $handle = fopen("//.//{$printer_name}", "w");
                if ($handle) {
                    fwrite($handle, $data);
                    fclose($handle);
                    return true;
                }
            }
            
            // للينكس
            $usb_device = '/dev/usb/lp0'; // مسار الطابعة USB
            if (file_exists($usb_device)) {
                file_put_contents($usb_device, $data);
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('خطأ في الطباعة عبر USB: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنسيق النص للطباعة (دعم العربية)
     */
    private function formatText($text, $width = 32) {
        // تحويل النص للترميز المناسب للطابعة
        $text = mb_convert_encoding($text, 'CP1256', 'UTF-8');
        
        // تقسيم النص حسب عرض الطابعة
        $lines = [];
        $words = explode(' ', $text);
        $current_line = '';
        
        foreach ($words as $word) {
            if (strlen($current_line . ' ' . $word) <= $width) {
                $current_line .= ($current_line ? ' ' : '') . $word;
            } else {
                if ($current_line) {
                    $lines[] = $current_line;
                }
                $current_line = $word;
            }
        }
        
        if ($current_line) {
            $lines[] = $current_line;
        }
        
        return implode(self::LF, $lines);
    }
    
    /**
     * إنشاء خط فاصل
     */
    private function createSeparator($char = '-', $length = 32) {
        return str_repeat($char, $length);
    }
    
    /**
     * تنسيق سطر بعمودين
     */
    private function formatTwoColumns($left, $right, $width = 32) {
        $left = mb_convert_encoding($left, 'CP1256', 'UTF-8');
        $right = mb_convert_encoding($right, 'CP1256', 'UTF-8');
        
        $left_len = strlen($left);
        $right_len = strlen($right);
        $spaces = $width - $left_len - $right_len;
        
        if ($spaces < 1) {
            $spaces = 1;
        }
        
        return $left . str_repeat(' ', $spaces) . $right;
    }
    
    /**
     * طباعة فاتورة المطبخ
     */
    public function printKitchenOrder($order_data) {
        $output = '';
        
        // تهيئة الطابعة
        $output .= self::INIT;
        $output .= self::CENTER;
        $output .= self::FONT_LARGE;
        $output .= self::BOLD_ON;
        
        // عنوان المطبخ
        $output .= $this->formatText('=== المطبخ ===') . self::LF;
        $output .= self::FONT_XLARGE;
        $output .= $this->formatText('طلب جديد') . self::LF;
        $output .= self::BOLD_OFF;
        $output .= self::FONT_NORMAL;
        
        // معلومات الطلب
        $output .= self::LEFT;
        $output .= $this->createSeparator('=') . self::LF;
        $output .= self::BOLD_ON;
        $output .= $this->formatTwoColumns('رقم الطلب:', '#' . $order_data['order_id']) . self::LF;
        $output .= $this->formatTwoColumns('الطاولة:', $order_data['table_number']) . self::LF;
        $output .= $this->formatTwoColumns('الوقت:', date('H:i:s')) . self::LF;
        $output .= $this->formatTwoColumns('التاريخ:', date('Y-m-d')) . self::LF;
        $output .= self::BOLD_OFF;
        $output .= $this->createSeparator('=') . self::LF;
        
        // عناصر الطلب
        $output .= self::BOLD_ON;
        $output .= self::CENTER;
        $output .= $this->formatText('عناصر الطلب') . self::LF;
        $output .= self::LEFT;
        $output .= self::BOLD_OFF;
        $output .= $this->createSeparator('-') . self::LF;
        
        foreach ($order_data['items'] as $item) {
            $output .= self::FONT_LARGE;
            $output .= self::BOLD_ON;
            $output .= $this->formatText($item['name']) . self::LF;
            $output .= self::BOLD_OFF;
            $output .= self::FONT_NORMAL;
            
            $output .= $this->formatTwoColumns('الكمية:', $item['quantity'] . ' قطعة') . self::LF;
            
            if (!empty($item['notes'])) {
                $output .= self::UNDERLINE_ON;
                $output .= $this->formatText('ملاحظات: ' . $item['notes']) . self::LF;
                $output .= self::UNDERLINE_OFF;
            }
            
            $output .= $this->createSeparator('-') . self::LF;
        }
        
        // معلومات إضافية
        $output .= self::BOLD_ON;
        $output .= $this->formatTwoColumns('إجمالي العناصر:', count($order_data['items'])) . self::LF;
        $output .= $this->formatTwoColumns('الموظف:', $order_data['user_name']) . self::LF;
        $output .= self::BOLD_OFF;
        
        // تعليمات المطبخ
        $output .= self::LF;
        $output .= self::CENTER;
        $output .= self::FONT_LARGE;
        $output .= self::BOLD_ON;
        $output .= $this->formatText('*** يرجى التحضير فوراً ***') . self::LF;
        $output .= self::BOLD_OFF;
        $output .= self::FONT_NORMAL;
        
        // قطع الورق
        $output .= self::LF . self::LF . self::LF;
        $output .= self::CUT;
        
        return $this->sendToPrinter($output);
    }
    
    /**
     * طباعة فاتورة الكاشير
     */
    public function printCashierReceipt($order_data) {
        $output = '';
        
        // تهيئة الطابعة
        $output .= self::INIT;
        $output .= self::CENTER;
        $output .= self::FONT_LARGE;
        $output .= self::BOLD_ON;
        
        // شعار المطعم
        $output .= $this->formatText('مطعم وسام') . self::LF;
        $output .= self::BOLD_OFF;
        $output .= self::FONT_SMALL;
        $output .= $this->formatText('نظام إدارة المطعم المتكامل') . self::LF;
        $output .= self::FONT_NORMAL;
        
        // معلومات الفاتورة
        $output .= self::LEFT;
        $output .= $this->createSeparator('=') . self::LF;
        $output .= $this->formatTwoColumns('رقم الفاتورة:', '#' . $order_data['order_id']) . self::LF;
        $output .= $this->formatTwoColumns('الطاولة:', $order_data['table_number']) . self::LF;
        $output .= $this->formatTwoColumns('التاريخ:', date('Y-m-d')) . self::LF;
        $output .= $this->formatTwoColumns('الوقت:', date('H:i:s')) . self::LF;
        $output .= $this->formatTwoColumns('الكاشير:', $order_data['user_name']) . self::LF;
        $output .= $this->createSeparator('=') . self::LF;
        
        // تفاصيل الطلب
        $output .= self::BOLD_ON;
        $output .= sprintf("%-16s %3s %8s", 'الصنف', 'كمية', 'المبلغ') . self::LF;
        $output .= self::BOLD_OFF;
        $output .= $this->createSeparator('-') . self::LF;
        
        $total = 0;
        foreach ($order_data['items'] as $item) {
            $item_total = $item['price'] * $item['quantity'];
            $total += $item_total;
            
            // اسم الصنف (مقطوع إذا كان طويل)
            $name = mb_substr($item['name'], 0, 16);
            $name = mb_convert_encoding($name, 'CP1256', 'UTF-8');
            
            $output .= sprintf("%-16s %3d %8s", 
                $name, 
                $item['quantity'], 
                number_format($item_total) . ' د.ع'
            ) . self::LF;
        }
        
        $output .= $this->createSeparator('-') . self::LF;
        
        // الإجمالي
        $output .= self::BOLD_ON;
        $output .= self::FONT_LARGE;
        $output .= $this->formatTwoColumns('الإجمالي:', number_format($total) . ' د.ع') . self::LF;
        $output .= self::BOLD_OFF;
        $output .= self::FONT_NORMAL;
        
        // معلومات الدفع
        $output .= $this->createSeparator('=') . self::LF;
        $output .= $this->formatTwoColumns('طريقة الدفع:', 'نقداً') . self::LF;
        $output .= $this->formatTwoColumns('المبلغ المدفوع:', number_format($total) . ' د.ع') . self::LF;
        $output .= $this->formatTwoColumns('الباقي:', '0 د.ع') . self::LF;
        
        // رسالة شكر
        $output .= self::LF;
        $output .= self::CENTER;
        $output .= self::BOLD_ON;
        $output .= $this->formatText('شكراً لزيارتكم') . self::LF;
        $output .= $this->formatText('نتطلع لخدمتكم مرة أخرى') . self::LF;
        $output .= self::BOLD_OFF;
        
        // معلومات التواصل
        $output .= self::LF;
        $output .= self::FONT_SMALL;
        $output .= $this->formatText('هاتف: 07XX XXX XXXX') . self::LF;
        $output .= $this->formatText('العنوان: بغداد - العراق') . self::LF;
        $output .= self::FONT_NORMAL;
        
        // فتح الدرج وقطع الورق
        $output .= self::LF . self::LF;
        $output .= self::DRAWER; // فتح درج النقود
        $output .= self::LF;
        $output .= self::CUT;
        
        return $this->sendToPrinter($output);
    }
    
    /**
     * طباعة تقرير يومي
     */
    public function printDailyReport($report_data) {
        $output = '';
        
        // تهيئة الطابعة
        $output .= self::INIT;
        $output .= self::CENTER;
        $output .= self::FONT_XLARGE;
        $output .= self::BOLD_ON;
        
        $output .= $this->formatText('تقرير يومي') . self::LF;
        $output .= self::BOLD_OFF;
        $output .= self::FONT_NORMAL;
        $output .= $this->formatText(date('Y-m-d')) . self::LF;
        
        $output .= self::LEFT;
        $output .= $this->createSeparator('=') . self::LF;
        
        // إحصائيات المبيعات
        $output .= self::BOLD_ON;
        $output .= $this->formatText('إحصائيات المبيعات') . self::LF;
        $output .= self::BOLD_OFF;
        $output .= $this->createSeparator('-') . self::LF;
        
        $output .= $this->formatTwoColumns('عدد الطلبات:', $report_data['total_orders']) . self::LF;
        $output .= $this->formatTwoColumns('إجمالي المبيعات:', number_format($report_data['total_sales']) . ' د.ع') . self::LF;
        $output .= $this->formatTwoColumns('متوسط الطلب:', number_format($report_data['avg_order']) . ' د.ع') . self::LF;
        
        // أكثر المنتجات مبيعاً
        if (!empty($report_data['top_products'])) {
            $output .= self::LF;
            $output .= self::BOLD_ON;
            $output .= $this->formatText('أكثر المنتجات مبيعاً') . self::LF;
            $output .= self::BOLD_OFF;
            $output .= $this->createSeparator('-') . self::LF;
            
            foreach ($report_data['top_products'] as $product) {
                $output .= $this->formatTwoColumns($product['name'], $product['quantity'] . ' قطعة') . self::LF;
            }
        }
        
        // قطع الورق
        $output .= self::LF . self::LF;
        $output .= self::CUT;
        
        return $this->sendToPrinter($output);
    }
    
    /**
     * اختبار الطابعة
     */
    public function testPrinter() {
        $output = '';
        
        $output .= self::INIT;
        $output .= self::CENTER;
        $output .= self::FONT_XLARGE;
        $output .= self::BOLD_ON;
        
        $output .= $this->formatText('اختبار الطابعة') . self::LF;
        $output .= self::BOLD_OFF;
        $output .= self::FONT_NORMAL;
        
        $output .= self::LEFT;
        $output .= $this->createSeparator('=') . self::LF;
        $output .= $this->formatTwoColumns('نوع الطابعة:', $this->printer_type) . self::LF;
        $output .= $this->formatTwoColumns('عنوان IP:', $this->printer_ip) . self::LF;
        $output .= $this->formatTwoColumns('المنفذ:', $this->printer_port) . self::LF;
        $output .= $this->formatTwoColumns('الوقت:', date('Y-m-d H:i:s')) . self::LF;
        $output .= $this->createSeparator('=') . self::LF;
        
        $output .= self::CENTER;
        $output .= self::BOLD_ON;
        $output .= $this->formatText('الطابعة تعمل بنجاح!') . self::LF;
        $output .= self::BOLD_OFF;
        
        $output .= self::LF . self::LF;
        $output .= self::CUT;
        
        return $this->sendToPrinter($output);
    }
}

/**
 * دوال مساعدة للطباعة
 */

// إنشاء كائن طابعة المطبخ
function getKitchenPrinter() {
    return new RestaurantPrinter('*************', 9100, 'kitchen');
}

// إنشاء كائن طابعة الكاشير
function getCashierPrinter() {
    return new RestaurantPrinter('*************', 9100, 'cashier');
}

// طباعة طلب للمطبخ
function printToKitchen($order_data) {
    $printer = getKitchenPrinter();
    return $printer->printKitchenOrder($order_data);
}

// طباعة فاتورة للكاشير
function printToCashier($order_data) {
    $printer = getCashierPrinter();
    return $printer->printCashierReceipt($order_data);
}

// طباعة للطابعتين معاً
function printToBoth($order_data) {
    $kitchen_result = printToKitchen($order_data);
    $cashier_result = printToCashier($order_data);
    
    return $kitchen_result && $cashier_result;
}
?>
