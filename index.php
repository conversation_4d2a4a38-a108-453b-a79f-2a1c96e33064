<?php
// صفحة الدخول الرئيسية للنظام

// التحقق من وجود ملف الإعداد
if (!file_exists('config/database.php')) {
    // إعادة التوجيه إلى صفحة التثبيت
    header('Location: simple_install.php');
    exit;
}

try {
    require_once 'config/database.php';

    // التحقق من وجود الدوال المطلوبة
    if (!function_exists('isLoggedIn')) {
        header('Location: debug_login.php');
        exit;
    }

    // إعادة التوجيه حسب حالة تسجيل الدخول
    if (isLoggedIn()) {
        if (function_exists('redirect')) {
            redirect('dashboard.php');
        } else {
            header('Location: dashboard.php');
            exit;
        }
    } else {
        header('Location: login.php');
        exit;
    }
} catch (Exception $e) {
    // في حالة وجود خطأ، إعادة التوجيه إلى صفحة التشخيص
    header('Location: debug_login.php');
    exit;
}
?>
