<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

// ملف التثبيت المبسط

// التحقق من وجود ملف الإعداد
if (file_exists('config/database.php')) {
    // محاولة الاتصال بقاعدة البيانات
    try {
        include_once 'config/database.php';
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px; border: 1px solid #c3e6cb;'>";
        echo "<h3>✅ النظام مثبت بالفعل!</h3>";
        echo "<p>يمكنك الآن <a href='login.php'>تسجيل الدخول</a> أو <a href='test_connection.php'>اختبار النظام</a></p>";
        echo "</div>";
        exit;
    } catch (Exception $e) {
        // إذا كان هناك خطأ في الاتصال، متابعة التثبيت
    }
}

$message = '';
$step = 1;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $host = $_POST['host'] ?? 'localhost';
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    $database = $_POST['database'] ?? 'restaurant_management_system';
    
    try {
        // الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$database`");
        
        // قراءة وتنفيذ ملف SQL
        $sql = file_get_contents('database.sql');
        
        // إزالة أوامر إنشاء قاعدة البيانات من الملف
        $sql = preg_replace('/CREATE DATABASE.*?;/i', '', $sql);
        $sql = preg_replace('/USE.*?;/i', '', $sql);
        
        // تقسيم الاستعلامات وتنفيذها
        $statements = explode(';', $sql);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // إنشاء ملف الإعداد
        $config_content = "<?php\n";
        $config_content .= "// إعدادات قاعدة البيانات\n";
        $config_content .= "define('DB_HOST', '" . str_replace("'", "\\'", $host) . "');\n";
        $config_content .= "define('DB_NAME', '" . str_replace("'", "\\'", $database) . "');\n";
        $config_content .= "define('DB_USER', '" . str_replace("'", "\\'", $username) . "');\n";
        $config_content .= "define('DB_PASS', '" . str_replace("'", "\\'", $password) . "');\n\n";
        
        $config_content .= "// إنشاء اتصال بقاعدة البيانات\n";
        $config_content .= "try {\n";
        $config_content .= "    \$pdo = new PDO(\"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\", DB_USER, DB_PASS);\n";
        $config_content .= "    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n";
        $config_content .= "    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);\n";
        $config_content .= "} catch(PDOException \$e) {\n";
        $config_content .= "    die(\"خطأ في الاتصال بقاعدة البيانات: \" . \$e->getMessage());\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// بدء الجلسة\n";
        $config_content .= "if (session_status() == PHP_SESSION_NONE) {\n";
        $config_content .= "    session_start();\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// دالة للتحقق من تسجيل الدخول\n";
        $config_content .= "function isLoggedIn() {\n";
        $config_content .= "    return isset(\$_SESSION['user_id']);\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// دالة للتحقق من صلاحيات المستخدم\n";
        $config_content .= "function hasRole(\$role) {\n";
        $config_content .= "    return isset(\$_SESSION['user_role']) && \$_SESSION['user_role'] === \$role;\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// دالة لإعادة التوجيه\n";
        $config_content .= "function redirect(\$url) {\n";
        $config_content .= "    header(\"Location: \$url\");\n";
        $config_content .= "    exit();\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// دالة لعرض الرسائل\n";
        $config_content .= "function showMessage(\$message, \$type = 'info') {\n";
        $config_content .= "    \$_SESSION['message'] = \$message;\n";
        $config_content .= "    \$_SESSION['message_type'] = \$type;\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// دالة لعرض الرسائل المحفوظة\n";
        $config_content .= "function displayMessage() {\n";
        $config_content .= "    if (isset(\$_SESSION['message'])) {\n";
        $config_content .= "        \$message = \$_SESSION['message'];\n";
        $config_content .= "        \$type = \$_SESSION['message_type'] ?? 'info';\n";
        $config_content .= "        unset(\$_SESSION['message']);\n";
        $config_content .= "        unset(\$_SESSION['message_type']);\n";
        $config_content .= "        \n";
        $config_content .= "        \$alertClass = '';\n";
        $config_content .= "        switch(\$type) {\n";
        $config_content .= "            case 'success': \$alertClass = 'alert-success'; break;\n";
        $config_content .= "            case 'error': \$alertClass = 'alert-danger'; break;\n";
        $config_content .= "            case 'warning': \$alertClass = 'alert-warning'; break;\n";
        $config_content .= "            default: \$alertClass = 'alert-info';\n";
        $config_content .= "        }\n";
        $config_content .= "        \n";
        $config_content .= "        echo \"<div class='alert \$alertClass alert-dismissible fade show' role='alert'>\";\n";
        $config_content .= "        echo \$message;\n";
        $config_content .= "        echo \"<button type='button' class='btn-close' data-bs-dismiss='alert'></button>\";\n";
        $config_content .= "        echo \"</div>\";\n";
        $config_content .= "    }\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// دالة لتنسيق التاريخ والوقت\n";
        $config_content .= "function formatDateTime(\$datetime) {\n";
        $config_content .= "    return date('Y-m-d H:i:s', strtotime(\$datetime));\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// دالة لتنسيق السعر\n";
        $config_content .= "function formatPrice(\$price) {\n";
        $config_content .= "    return number_format(\$price, 2) . ' ريال';\n";
        $config_content .= "}\n";
        $config_content .= "?>";
        
        // إنشاء مجلد config إذا لم يكن موجوداً
        if (!is_dir('config')) {
            mkdir('config', 0755, true);
        }
        
        file_put_contents('config/database.php', $config_content);
        
        $message = "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
        $message .= "<h3>🎉 تم التثبيت بنجاح!</h3>";
        $message .= "<p><strong>بيانات تسجيل الدخول:</strong></p>";
        $message .= "<ul>";
        $message .= "<li><strong>مدير:</strong> admin / password123</li>";
        $message .= "<li><strong>كاشير:</strong> cashier / password123</li>";
        $message .= "<li><strong>مطبخ:</strong> kitchen / password123</li>";
        $message .= "</ul>";
        $message .= "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>بدء الاستخدام</a> ";
        $message .= "<a href='test_connection.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>اختبار النظام</a></p>";
        $message .= "</div>";
        $step = 2;
        
    } catch (Exception $e) {
        $message = "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
        $message .= "<h3>❌ خطأ في التثبيت</h3>";
        $message .= "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        $message .= "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة المطاعم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            border-color: #6c5ce7;
            outline: none;
        }
        .btn {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            background: linear-gradient(135deg, #5a4fcf, #8b7eff);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍽️ نظام إدارة المطاعم</h1>
            <p>تثبيت سريع وسهل</p>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <?php echo $message; ?>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <h3>إعداد قاعدة البيانات</h3>
                <p>أدخل بيانات الاتصال بقاعدة البيانات MySQL:</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="host">عنوان الخادم:</label>
                        <input type="text" id="host" name="host" value="localhost" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="username">اسم المستخدم:</label>
                        <input type="text" id="username" name="username" value="root" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">كلمة المرور:</label>
                        <input type="password" id="password" name="password" value="">
                    </div>
                    
                    <div class="form-group">
                        <label for="database">اسم قاعدة البيانات:</label>
                        <input type="text" id="database" name="database" value="restaurant_management_system" required>
                    </div>
                    
                    <button type="submit" class="btn">تثبيت النظام</button>
                </form>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
