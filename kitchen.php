<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('kitchen')) {
    redirect('login.php');
}

$page_title = 'المطبخ - KOT';

try {
    // جلب عناصر الطلبات التي تحتاج تحضير
    $stmt = $pdo->query("
        SELECT oi.*, p.name as product_name, p.description,
               o.id as order_id, t.table_number, o.created_at as order_time,
               u.full_name as user_name
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        JOIN orders o ON oi.order_id = o.id
        JOIN tables t ON o.table_id = t.id
        JOIN users u ON o.user_id = u.id
        WHERE o.status IN ('pending', 'processing') 
        AND oi.status IN ('pending', 'preparing')
        ORDER BY o.created_at ASC, oi.id ASC
    ");
    $kitchen_items = $stmt->fetchAll();
    
    // تجميع العناصر حسب الطلب
    $orders_items = [];
    foreach ($kitchen_items as $item) {
        $orders_items[$item['order_id']][] = $item;
    }
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب البيانات';
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-fire me-2"></i>المطبخ - KOT</h2>
    <div class="text-muted">
        <i class="fas fa-clock me-1"></i>
        <span id="current-time"></span>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
    </div>
<?php endif; ?>

<?php if (empty($orders_items)): ?>
    <div class="text-center py-5">
        <i class="fas fa-fire fa-5x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد طلبات في المطبخ</h4>
        <p class="text-muted">جميع الطلبات جاهزة أو لا توجد طلبات جديدة</p>
    </div>
<?php else: ?>
    <div class="row">
        <?php foreach ($orders_items as $order_id => $items): ?>
            <?php $first_item = $items[0]; ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card kitchen-order h-100">
                    <div class="card-header bg-danger text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">طلب #<?php echo $order_id; ?></h6>
                            <span class="badge bg-light text-dark">
                                طاولة <?php echo $first_item['table_number']; ?>
                            </span>
                        </div>
                        <small>
                            <?php echo $first_item['user_name']; ?> - 
                            <?php echo date('H:i', strtotime($first_item['order_time'])); ?>
                        </small>
                    </div>
                    
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <?php foreach ($items as $item): ?>
                                <div class="list-group-item px-0 kitchen-item" data-item-id="<?php echo $item['id']; ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo $item['product_name']; ?></h6>
                                            <?php if ($item['description']): ?>
                                                <small class="text-muted d-block"><?php echo $item['description']; ?></small>
                                            <?php endif; ?>
                                            <?php if ($item['notes']): ?>
                                                <small class="text-info d-block">
                                                    <i class="fas fa-sticky-note me-1"></i>
                                                    <?php echo $item['notes']; ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="text-end">
                                            <span class="badge bg-primary fs-6">
                                                <?php echo $item['quantity']; ?>x
                                            </span>
                                            <br>
                                            <span class="badge bg-<?php 
                                                echo $item['status'] == 'pending' ? 'warning' : 
                                                    ($item['status'] == 'preparing' ? 'info' : 'success'); 
                                            ?> mt-1">
                                                <?php 
                                                $status_text = [
                                                    'pending' => 'معلق',
                                                    'preparing' => 'قيد التحضير',
                                                    'ready' => 'جاهز',
                                                    'served' => 'تم التقديم'
                                                ];
                                                echo $status_text[$item['status']];
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-2">
                                        <div class="btn-group btn-group-sm w-100" role="group">
                                            <?php if ($item['status'] == 'pending'): ?>
                                                <button class="btn btn-info" onclick="updateItemStatus(<?php echo $item['id']; ?>, 'preparing')">
                                                    <i class="fas fa-play me-1"></i>
                                                    بدء التحضير
                                                </button>
                                            <?php elseif ($item['status'] == 'preparing'): ?>
                                                <button class="btn btn-success" onclick="updateItemStatus(<?php echo $item['id']; ?>, 'ready')">
                                                    <i class="fas fa-check me-1"></i>
                                                    جاهز
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <?php 
                                $pending_count = count(array_filter($items, function($item) { 
                                    return in_array($item['status'], ['pending', 'preparing']); 
                                }));
                                echo $pending_count . ' عنصر متبقي';
                                ?>
                            </small>
                            
                            <?php if ($pending_count == 0): ?>
                                <button class="btn btn-success btn-sm" onclick="markOrderReady(<?php echo $order_id; ?>)">
                                    <i class="fas fa-bell me-1"></i>
                                    إشعار الكاشير
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- نافذة إضافة ملاحظة -->
<div class="modal fade" id="addNoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة ملاحظة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="item_note" class="form-label">الملاحظة</label>
                    <textarea class="form-control" id="item_note" rows="3" placeholder="أدخل ملاحظة للعنصر..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveItemNote()">
                    <i class="fas fa-save me-2"></i>
                    حفظ الملاحظة
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.kitchen-order {
    border-right: 4px solid #dc3545;
}

.kitchen-order.preparing {
    border-right-color: #ffc107;
}

.kitchen-order.ready {
    border-right-color: #28a745;
}

.kitchen-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    background: #f8f9fa;
    transition: all 0.3s;
}

.kitchen-item:hover {
    background: #e9ecef;
    transform: translateX(-5px);
}

.kitchen-item[data-status="preparing"] {
    border-right: 4px solid #ffc107;
}

.kitchen-item[data-status="ready"] {
    border-right: 4px solid #28a745;
}

@media (max-width: 768px) {
    .btn-group-sm .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>

<script>
let currentItemId = null;

function updateItemStatus(itemId, status) {
    fetch('ajax/update_order_item_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            item_id: itemId,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث العنصر في الواجهة
            const itemElement = document.querySelector(`[data-item-id="${itemId}"]`);
            if (itemElement) {
                itemElement.setAttribute('data-status', status);
                
                // إعادة تحميل الصفحة لتحديث العرض
                setTimeout(() => {
                    location.reload();
                }, 500);
            }
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في تحديث حالة العنصر');
    });
}

function markOrderReady(orderId) {
    if (confirm('هل جميع عناصر الطلب جاهزة؟')) {
        fetch('ajax/mark_order_ready.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order_id: orderId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إشعار الكاشير بأن الطلب جاهز');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في إرسال الإشعار');
        });
    }
}

function addItemNote(itemId) {
    currentItemId = itemId;
    document.getElementById('item_note').value = '';
    new bootstrap.Modal(document.getElementById('addNoteModal')).show();
}

function saveItemNote() {
    const note = document.getElementById('item_note').value.trim();
    
    if (!note) {
        alert('يرجى إدخال ملاحظة');
        return;
    }
    
    fetch('ajax/add_item_note.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            item_id: currentItemId,
            note: note
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addNoteModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في حفظ الملاحظة');
    });
}

// تحديث تلقائي كل 30 ثانية
setInterval(() => {
    location.reload();
}, 30000);
</script>

<?php include 'includes/footer.php'; ?>
