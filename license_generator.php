<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('login.php');
}

$page_title = 'مولد التراخيص';
$message = '';
$message_type = '';
$generated_license = null;

// معالجة إنشاء ترخيص جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['generate_license'])) {
    $license_type = $_POST['license_type'];
    $max_orders = intval($_POST['max_orders']);
    $duration_days = intval($_POST['duration_days']);
    $custom_prefix = trim($_POST['custom_prefix']);
    
    try {
        // إنشاء مفتاح ترخيص فريد
        $prefix = !empty($custom_prefix) ? strtoupper($custom_prefix) : strtoupper($license_type);
        $random_part = strtoupper(bin2hex(random_bytes(8)));
        $license_key = $prefix . '-' . $random_part;
        
        // حساب تاريخ انتهاء الصلاحية
        $expires_at = null;
        if ($duration_days > 0) {
            $expires_at = date('Y-m-d H:i:s', strtotime("+{$duration_days} days"));
        }
        
        // إدراج الترخيص في قاعدة البيانات
        $stmt = $pdo->prepare("
            INSERT INTO activation_licenses 
            (license_key, license_type, max_orders, expires_at, activation_data) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $activation_data = json_encode([
            'generated_by' => $_SESSION['user_id'],
            'generated_at' => date('Y-m-d H:i:s'),
            'generator_ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'custom_settings' => [
                'max_orders' => $max_orders,
                'duration_days' => $duration_days
            ]
        ]);
        
        $stmt->execute([$license_key, $license_type, $max_orders, $expires_at, $activation_data]);
        
        $generated_license = [
            'key' => $license_key,
            'type' => $license_type,
            'max_orders' => $max_orders,
            'expires_at' => $expires_at,
            'duration_days' => $duration_days
        ];
        
        $message = 'تم إنشاء الترخيص بنجاح!';
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = 'خطأ في إنشاء الترخيص: ' . $e->getMessage();
        $message_type = 'danger';
    }
}

// جلب التراخيص الموجودة
$stmt = $pdo->prepare("
    SELECT l.*, u.username as activated_by_name 
    FROM activation_licenses l 
    LEFT JOIN users u ON l.activated_by = u.id 
    ORDER BY l.created_at DESC 
    LIMIT 20
");
$stmt->execute();
$existing_licenses = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- عرض الترخيص المُنشأ -->
            <?php if ($generated_license): ?>
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-certificate me-2"></i>
                        تم إنشاء الترخيص بنجاح
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label"><strong>مفتاح الترخيص:</strong></label>
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           value="<?php echo htmlspecialchars($generated_license['key']); ?>" 
                                           id="generated-key" 
                                           readonly>
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            onclick="copyToClipboard('generated-key')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>نوع الترخيص:</strong> 
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($generated_license['type']); ?></span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>حد الطلبات:</strong> 
                                        <span class="badge bg-info"><?php echo $generated_license['max_orders']; ?></span>
                                    </p>
                                </div>
                            </div>
                            
                            <?php if ($generated_license['expires_at']): ?>
                            <p><strong>ينتهي في:</strong> 
                                <span class="text-warning"><?php echo date('Y-m-d H:i', strtotime($generated_license['expires_at'])); ?></span>
                                <small class="text-muted">(<?php echo $generated_license['duration_days']; ?> يوم)</small>
                            </p>
                            <?php else: ?>
                            <p><strong>الصلاحية:</strong> <span class="text-success">غير محدودة</span></p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-4 text-center">
                            <div class="p-3 bg-light rounded">
                                <i class="fas fa-qrcode fa-4x text-muted mb-2"></i>
                                <p class="small text-muted">يمكن إضافة QR Code هنا</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- مولد الترخيص -->
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-magic me-2"></i>
                        مولد التراخيص الجديدة
                    </h3>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="license_type" class="form-label">نوع الترخيص:</label>
                                    <select class="form-select" id="license_type" name="license_type" required>
                                        <option value="trial">تجريبي (Trial)</option>
                                        <option value="full">كامل (Full)</option>
                                        <option value="premium">متقدم (Premium)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_orders" class="form-label">حد الطلبات:</label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="max_orders" 
                                           name="max_orders" 
                                           value="10" 
                                           min="1" 
                                           max="999999">
                                    <div class="form-text">0 = غير محدود</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="duration_days" class="form-label">مدة الصلاحية (بالأيام):</label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="duration_days" 
                                           name="duration_days" 
                                           value="30" 
                                           min="0" 
                                           max="3650">
                                    <div class="form-text">0 = بدون انتهاء صلاحية</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="custom_prefix" class="form-label">بادئة مخصصة (اختياري):</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="custom_prefix" 
                                           name="custom_prefix" 
                                           placeholder="مثال: RESTAURANT"
                                           maxlength="10">
                                    <div class="form-text">سيتم استخدام نوع الترخيص إذا تُرك فارغاً</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" name="generate_license" class="btn btn-success btn-lg">
                                <i class="fas fa-certificate me-2"></i>
                                إنشاء ترخيص جديد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- التراخيص الموجودة -->
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        التراخيص الموجودة (آخر 20)
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (empty($existing_licenses)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تراخيص</h5>
                            <p class="text-muted">ابدأ بإنشاء ترخيص جديد</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>مفتاح الترخيص</th>
                                        <th>النوع</th>
                                        <th>حد الطلبات</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>مُفعل بواسطة</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($existing_licenses as $license): ?>
                                        <tr>
                                            <td>
                                                <code class="small"><?php echo htmlspecialchars($license['license_key']); ?></code>
                                            </td>
                                            <td>
                                                <?php
                                                $type_badges = [
                                                    'trial' => 'warning',
                                                    'full' => 'success',
                                                    'premium' => 'primary'
                                                ];
                                                $badge_class = $type_badges[$license['license_type']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?php echo $badge_class; ?>">
                                                    <?php echo htmlspecialchars($license['license_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $license['max_orders'] == 0 ? '∞' : $license['max_orders']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $is_expired = $license['expires_at'] && strtotime($license['expires_at']) < time();
                                                $is_activated = !empty($license['activated_at']);
                                                
                                                if ($is_expired) {
                                                    echo '<span class="badge bg-danger">منتهي الصلاحية</span>';
                                                } elseif ($is_activated) {
                                                    echo '<span class="badge bg-success">مُفعل</span>';
                                                } else {
                                                    echo '<span class="badge bg-warning">غير مُفعل</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <small><?php echo date('Y-m-d H:i', strtotime($license['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <?php if ($license['activated_by_name']): ?>
                                                    <small><?php echo htmlspecialchars($license['activated_by_name']); ?></small>
                                                <?php else: ?>
                                                    <small class="text-muted">غير مُفعل</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-secondary" 
                                                        onclick="copyToClipboard('key-<?php echo $license['id']; ?>')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <input type="hidden" 
                                                       id="key-<?php echo $license['id']; ?>" 
                                                       value="<?php echo htmlspecialchars($license['license_key']); ?>">
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="activation_manager.php" class="btn btn-secondary me-2">
                    <i class="fas fa-key me-1"></i>إدارة التفعيل
                </a>
                <a href="system_status.php" class="btn btn-info me-2">
                    <i class="fas fa-chart-line me-1"></i>حالة النظام
                </a>
                <a href="dashboard.php" class="btn btn-outline-dark">
                    <i class="fas fa-home me-1"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999); // للهواتف المحمولة
    
    try {
        document.execCommand('copy');
        
        // إظهار رسالة نجاح
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-success"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
        
    } catch (err) {
        alert('فشل في النسخ. يرجى النسخ يدوياً.');
    }
}

// تحديث حد الطلبات حسب نوع الترخيص
document.getElementById('license_type').addEventListener('change', function() {
    const maxOrdersInput = document.getElementById('max_orders');
    const durationInput = document.getElementById('duration_days');
    
    switch (this.value) {
        case 'trial':
            maxOrdersInput.value = 10;
            durationInput.value = 30;
            break;
        case 'full':
            maxOrdersInput.value = 0; // غير محدود
            durationInput.value = 0; // بدون انتهاء
            break;
        case 'premium':
            maxOrdersInput.value = 0; // غير محدود
            durationInput.value = 0; // بدون انتهاء
            break;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
