<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من وجود ملف الإعداد
if (!file_exists('config/database.php')) {
    header('Location: simple_install.php');
    exit;
}

try {
    require_once 'config/database.php';
} catch (Exception $e) {
    die('خطأ في تحميل الإعدادات: ' . $e->getMessage() . '<br><a href="simple_install.php">إعادة التثبيت</a>');
}

// إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه إلى لوحة التحكم
if (function_exists('isLoggedIn') && isLoggedIn()) {
    if (function_exists('redirect')) {
        redirect('dashboard.php');
    } else {
        header('Location: dashboard.php');
        exit;
    }
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT id, username, password, full_name, role FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // بدء الجلسة إذا لم تكن بدأت
                if (session_status() == PHP_SESSION_NONE) {
                    session_start();
                }

                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_name'] = $user['full_name'];
                $_SESSION['user_role'] = $user['role'];

                if (function_exists('redirect')) {
                    redirect('dashboard.php');
                } else {
                    header('Location: dashboard.php');
                    exit;
                }
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في النظام: ' . $e->getMessage();
        }
    }
}

$page_title = 'تسجيل الدخول';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .login-body {
            padding: 30px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
        }
        
        .form-control:focus {
            border-color: #6c5ce7;
            box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            font-size: 16px;
            width: 100%;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #5a4fcf, #8b7eff);
        }
        
        .alert {
            border-radius: 10px;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
        }
        
        .form-control {
            border-right: none;
        }
        
        .demo-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <i class="fas fa-utensils fa-3x mb-3"></i>
            <h3>نظام إدارة المطعم</h3>
            <p class="mb-0">تسجيل الدخول</p>
        </div>
        
        <div class="login-body">
            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="demo-info">
                <h6><i class="fas fa-info-circle me-2"></i>بيانات تجريبية:</h6>
                <div class="row">
                    <div class="col-12 mb-2">
                        <strong>مدير:</strong> admin / password123
                    </div>
                    <div class="col-12 mb-2">
                        <strong>كاشير:</strong> cashier / password123
                    </div>
                    <div class="col-12 mb-3">
                        <strong>مطبخ:</strong> kitchen / password123
                    </div>
                </div>

                <!-- أزرار تسجيل دخول سريع -->
                <div class="quick-login">
                    <h6><i class="fas fa-bolt me-2"></i>تسجيل دخول سريع:</h6>
                    <button type="button" class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="quickLogin('admin', 'password123')">
                        <i class="fas fa-user-shield me-1"></i> دخول كمدير
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm w-100 mb-2" onclick="quickLogin('cashier', 'password123')">
                        <i class="fas fa-cash-register me-1"></i> دخول ككاشير
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="quickLogin('kitchen', 'password123')">
                        <i class="fas fa-fire me-1"></i> دخول كمطبخ
                    </button>
                </div>
            </div>

            <div class="text-center mt-3">
                <small class="text-muted">
                    <a href="simple_install.php">إعادة التثبيت</a> |
                    <a href="check_system.php">فحص النظام</a>
                </small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.querySelector('form').submit();
        }

        // تركيز على حقل اسم المستخدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
