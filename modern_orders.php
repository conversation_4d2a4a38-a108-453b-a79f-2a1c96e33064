<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$page_title = 'إدارة الطلبات - النظام العصري';

// جلب الطلبات مع تفاصيل كاملة
$stmt = $pdo->prepare("
    SELECT 
        o.id,
        o.table_id,
        o.total_amount,
        o.status,
        o.created_at,
        o.updated_at,
        t.table_number,
        u.username as user_name,
        COUNT(oi.id) as items_count,
        GROUP_CONCAT(CONCAT(p.name, ' (', oi.quantity, ')') SEPARATOR ', ') as items_summary
    FROM orders o
    LEFT JOIN tables t ON o.table_id = t.id
    LEFT JOIN users u ON o.user_id = u.id
    LEFT JOIN order_items oi ON o.id = oi.order_id
    LEFT JOIN products p ON oi.product_id = p.id
    WHERE DATE(o.created_at) = CURDATE()
    GROUP BY o.id
    ORDER BY 
        CASE o.status 
            WHEN 'pending' THEN 1 
            WHEN 'processing' THEN 2 
            WHEN 'completed' THEN 3 
            WHEN 'cancelled' THEN 4 
        END,
        o.created_at DESC
");
$stmt->execute();
$orders = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-clipboard-list me-2 text-primary"></i>إدارة الطلبات العصرية</h2>
            <p class="text-muted mb-0">نظام متطور لإدارة طلبات المطعم بتقنيات حديثة</p>
        </div>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary" onclick="refreshOrders()">
                <i class="fas fa-sync-alt me-2"></i>تحديث
            </button>
            <a href="add_order.php?table_id=1" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>طلب جديد
            </a>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-home me-2"></i>الرئيسية
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="text-warning" id="pending-count">0</h4>
                    <small class="text-muted">طلبات معلقة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-cog fa-2x text-info mb-2"></i>
                    <h4 class="text-info" id="processing-count">0</h4>
                    <small class="text-muted">قيد التحضير</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="text-success" id="completed-count">0</h4>
                    <small class="text-muted">مكتملة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                    <h4 class="text-danger" id="cancelled-count">0</h4>
                    <small class="text-muted">ملغية</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Grid -->
    <div class="row" id="orders-container">
        <!-- سيتم ملؤها بـ JavaScript -->
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="printOrderBtn">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmationTitle">تأكيد العملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="confirmationMessage">
                هل أنت متأكد من هذا الإجراء؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmActionBtn">تأكيد</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notifications -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert">
        <div class="toast-header bg-success text-white">
            <i class="fas fa-check-circle me-2"></i>
            <strong class="me-auto">نجح</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="successMessage"></div>
    </div>
    
    <div id="errorToast" class="toast" role="alert">
        <div class="toast-header bg-danger text-white">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong class="me-auto">خطأ</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="errorMessage"></div>
    </div>
</div>

<style>
.order-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.order-status-pending {
    border-left: 5px solid #ffc107;
}

.order-status-processing {
    border-left: 5px solid #17a2b8;
}

.order-status-completed {
    border-left: 5px solid #28a745;
}

.order-status-cancelled {
    border-left: 5px solid #dc3545;
}

.action-btn {
    border-radius: 50px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: scale(1.05);
}

.order-time {
    font-size: 11px;
    opacity: 0.7;
}

.order-items {
    font-size: 13px;
    color: #6c757d;
    max-height: 40px;
    overflow: hidden;
}

.loading-spinner {
    display: none;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>

<script>
// Modern Orders Management System
class ModernOrdersManager {
    constructor() {
        this.orders = <?php echo json_encode($orders); ?>;
        this.currentOrderId = null;
        this.confirmCallback = null;
        
        this.init();
    }
    
    init() {
        console.log('🚀 تهيئة نظام إدارة الطلبات العصري');
        
        this.renderOrders();
        this.updateStatistics();
        this.setupEventListeners();
        this.startAutoRefresh();
        
        console.log('✅ تم تهيئة النظام بنجاح');
    }
    
    setupEventListeners() {
        // Event delegation للأزرار الديناميكية
        document.getElementById('orders-container').addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (!target) return;
            
            e.preventDefault();
            const action = target.dataset.action;
            const orderId = parseInt(target.dataset.orderId);
            
            this.handleAction(action, orderId);
        });
        
        // زر التأكيد في Modal
        document.getElementById('confirmActionBtn').addEventListener('click', () => {
            if (this.confirmCallback) {
                this.confirmCallback();
                this.hideConfirmation();
            }
        });
        
        console.log('🔗 تم ربط Event Listeners');
    }
    
    handleAction(action, orderId) {
        console.log(`🎯 تنفيذ إجراء: ${action} للطلب: ${orderId}`);
        
        switch (action) {
            case 'start':
                this.showConfirmation(
                    'بدء التحضير',
                    'هل تريد بدء تحضير هذا الطلب؟',
                    () => this.updateOrderStatus(orderId, 'processing')
                );
                break;
                
            case 'complete':
                this.showConfirmation(
                    'إكمال الطلب',
                    'هل تم الانتهاء من تحضير هذا الطلب؟',
                    () => this.updateOrderStatus(orderId, 'completed')
                );
                break;
                
            case 'cancel':
                this.showConfirmation(
                    'إلغاء الطلب',
                    'هل أنت متأكد من إلغاء هذا الطلب؟',
                    () => this.updateOrderStatus(orderId, 'cancelled')
                );
                break;
                
            case 'view':
                this.viewOrderDetails(orderId);
                break;
                
            case 'edit':
                window.location.href = `edit_order.php?id=${orderId}`;
                break;
                
            case 'print':
                this.printOrder(orderId);
                break;
        }
    }
    
    async updateOrderStatus(orderId, newStatus) {
        console.log(`🔄 تحديث حالة الطلب ${orderId} إلى ${newStatus}`);
        
        this.showLoading(orderId);
        
        try {
            const response = await fetch('ajax/modern_update_order.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    order_id: orderId,
                    status: newStatus
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(data.message);
                await this.refreshOrderData();
                this.renderOrders();
                this.updateStatistics();
                
                console.log('✅ تم تحديث الطلب بنجاح');
            } else {
                throw new Error(data.message);
            }
            
        } catch (error) {
            console.error('❌ خطأ في تحديث الطلب:', error);
            this.showError('حدث خطأ في تحديث الطلب: ' + error.message);
        } finally {
            this.hideLoading(orderId);
        }
    }
    
    async refreshOrderData() {
        try {
            const response = await fetch('ajax/get_orders.php');
            const data = await response.json();
            
            if (data.success) {
                this.orders = data.orders;
                console.log('🔄 تم تحديث بيانات الطلبات');
            }
        } catch (error) {
            console.error('❌ خطأ في تحديث البيانات:', error);
        }
    }
    
    renderOrders() {
        const container = document.getElementById('orders-container');
        
        if (this.orders.length === 0) {
            container.innerHTML = `
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد طلبات اليوم</h4>
                        <p class="text-muted">ابدأ بإضافة طلب جديد</p>
                        <a href="add_order.php?table_id=1" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة طلب
                        </a>
                    </div>
                </div>
            `;
            return;
        }
        
        const html = this.orders.map(order => this.createOrderCard(order)).join('');
        container.innerHTML = html;
        
        // إضافة animation
        container.querySelectorAll('.order-card').forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('fade-in');
            }, index * 100);
        });
        
        console.log(`📋 تم عرض ${this.orders.length} طلب`);
    }
    
    createOrderCard(order) {
        const statusConfig = {
            pending: { 
                class: 'warning', 
                text: 'معلق', 
                icon: 'clock',
                actions: ['start', 'edit', 'cancel', 'view', 'print']
            },
            processing: { 
                class: 'info', 
                text: 'قيد التحضير', 
                icon: 'cog fa-spin',
                actions: ['complete', 'cancel', 'view', 'print']
            },
            completed: { 
                class: 'success', 
                text: 'مكتمل', 
                icon: 'check-circle',
                actions: ['view', 'print']
            },
            cancelled: { 
                class: 'danger', 
                text: 'ملغي', 
                icon: 'times-circle',
                actions: ['view']
            }
        };
        
        const config = statusConfig[order.status] || statusConfig.pending;
        const timeAgo = this.getTimeAgo(order.created_at);
        
        return `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card order-card order-status-${order.status}" data-order-id="${order.id}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">
                                <i class="fas fa-hashtag me-1"></i>
                                طلب ${order.id}
                            </h6>
                            <small class="text-muted">
                                <i class="fas fa-table me-1"></i>
                                ${order.table_number || 'طاولة ' + order.table_id}
                            </small>
                        </div>
                        <span class="badge bg-${config.class}">
                            <i class="fas fa-${config.icon} me-1"></i>
                            ${config.text}
                        </span>
                    </div>
                    
                    <div class="card-body">
                        <div class="order-items mb-3">
                            <i class="fas fa-utensils me-1 text-muted"></i>
                            ${order.items_summary || 'لا توجد عناصر'}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong class="text-success">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    ${parseInt(order.total_amount).toLocaleString()} د.ع
                                </strong>
                            </div>
                            <small class="order-time text-muted">
                                <i class="fas fa-clock me-1"></i>
                                ${timeAgo}
                            </small>
                        </div>
                        
                        <div class="d-flex flex-wrap gap-1">
                            ${this.createActionButtons(order.id, config.actions)}
                        </div>
                        
                        <div class="loading-spinner text-center mt-2" id="loading-${order.id}">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">جاري التحديث...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    createActionButtons(orderId, actions) {
        const buttons = {
            start: `<button class="btn btn-success action-btn" data-action="start" data-order-id="${orderId}" title="بدء التحضير">
                        <i class="fas fa-play"></i>
                    </button>`,
            complete: `<button class="btn btn-info action-btn" data-action="complete" data-order-id="${orderId}" title="إكمال الطلب">
                          <i class="fas fa-check"></i>
                       </button>`,
            edit: `<button class="btn btn-warning action-btn" data-action="edit" data-order-id="${orderId}" title="تعديل">
                      <i class="fas fa-edit"></i>
                   </button>`,
            cancel: `<button class="btn btn-danger action-btn" data-action="cancel" data-order-id="${orderId}" title="إلغاء">
                        <i class="fas fa-times"></i>
                     </button>`,
            view: `<button class="btn btn-primary action-btn" data-action="view" data-order-id="${orderId}" title="عرض التفاصيل">
                      <i class="fas fa-eye"></i>
                   </button>`,
            print: `<button class="btn btn-secondary action-btn" data-action="print" data-order-id="${orderId}" title="طباعة">
                       <i class="fas fa-print"></i>
                    </button>`
        };
        
        return actions.map(action => buttons[action] || '').join('');
    }
    
    updateStatistics() {
        const stats = {
            pending: 0,
            processing: 0,
            completed: 0,
            cancelled: 0
        };
        
        this.orders.forEach(order => {
            stats[order.status] = (stats[order.status] || 0) + 1;
        });
        
        Object.keys(stats).forEach(status => {
            const element = document.getElementById(`${status}-count`);
            if (element) {
                element.textContent = stats[status];
                if (stats[status] > 0) {
                    element.parentElement.classList.add('pulse');
                }
            }
        });
        
        console.log('📊 تم تحديث الإحصائيات:', stats);
    }
    
    showConfirmation(title, message, callback) {
        document.getElementById('confirmationTitle').textContent = title;
        document.getElementById('confirmationMessage').textContent = message;
        this.confirmCallback = callback;
        
        new bootstrap.Modal(document.getElementById('confirmationModal')).show();
    }
    
    hideConfirmation() {
        bootstrap.Modal.getInstance(document.getElementById('confirmationModal')).hide();
        this.confirmCallback = null;
    }
    
    showLoading(orderId) {
        const spinner = document.getElementById(`loading-${orderId}`);
        if (spinner) {
            spinner.style.display = 'block';
        }
    }
    
    hideLoading(orderId) {
        const spinner = document.getElementById(`loading-${orderId}`);
        if (spinner) {
            spinner.style.display = 'none';
        }
    }
    
    showSuccess(message) {
        document.getElementById('successMessage').textContent = message;
        new bootstrap.Toast(document.getElementById('successToast')).show();
    }
    
    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        new bootstrap.Toast(document.getElementById('errorToast')).show();
    }
    
    getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        
        if (diffMins < 1) return 'الآن';
        if (diffMins < 60) return `${diffMins} دقيقة`;
        
        const diffHours = Math.floor(diffMins / 60);
        if (diffHours < 24) return `${diffHours} ساعة`;
        
        return date.toLocaleDateString('ar');
    }
    
    async viewOrderDetails(orderId) {
        try {
            const response = await fetch(`ajax/get_order_details.php?order_id=${orderId}`);
            const html = await response.text();
            
            document.getElementById('orderDetailsContent').innerHTML = html;
            this.currentOrderId = orderId;
            
            new bootstrap.Modal(document.getElementById('orderDetailsModal')).show();
            
        } catch (error) {
            this.showError('حدث خطأ في جلب تفاصيل الطلب');
        }
    }
    
    printOrder(orderId) {
        window.open(`print_invoice.php?order_id=${orderId}`, '_blank', 'width=800,height=600');
    }
    
    startAutoRefresh() {
        setInterval(async () => {
            await this.refreshOrderData();
            this.renderOrders();
            this.updateStatistics();
        }, 30000); // كل 30 ثانية
        
        console.log('🔄 تم تفعيل التحديث التلقائي');
    }
}

// Global functions
function refreshOrders() {
    location.reload();
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.ordersManager = new ModernOrdersManager();
});
</script>

<?php include 'includes/footer.php'; ?>
