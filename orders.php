<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$page_title = 'إدارة الطلبات';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $order_id = intval($_POST['order_id']);
        
        try {
            if ($action == 'cancel') {
                $stmt = $pdo->prepare("UPDATE orders SET status = 'cancelled' WHERE id = ?");
                $stmt->execute([$order_id]);
                
                // تحرير الطاولة
                $stmt = $pdo->prepare("UPDATE tables SET status = 'available' WHERE id = (SELECT table_id FROM orders WHERE id = ?)");
                $stmt->execute([$order_id]);
                
                showMessage('تم إلغاء الطلب بنجاح', 'success');
            }
        } catch (PDOException $e) {
            showMessage('حدث خطأ في العملية', 'error');
        }
    }
}

// جلب الطلبات
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';

try {
    $where_conditions = [];
    $params = [];
    
    if ($filter != 'all') {
        $where_conditions[] = "o.status = ?";
        $params[] = $filter;
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(t.table_number LIKE ? OR u.full_name LIKE ? OR o.id LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $pdo->prepare("
        SELECT o.*, t.table_number, u.full_name as user_name,
               COUNT(oi.id) as items_count
        FROM orders o
        JOIN tables t ON o.table_id = t.id
        JOIN users u ON o.user_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        $where_clause
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ");
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب البيانات';
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-shopping-cart me-2"></i>إدارة الطلبات</h2>
    <a href="add_order.php" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>طلب جديد
    </a>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="filter" class="form-label">حالة الطلب</label>
                <select class="form-select" id="filter" name="filter">
                    <option value="all" <?php echo $filter == 'all' ? 'selected' : ''; ?>>جميع الطلبات</option>
                    <option value="pending" <?php echo $filter == 'pending' ? 'selected' : ''; ?>>معلق</option>
                    <option value="processing" <?php echo $filter == 'processing' ? 'selected' : ''; ?>>قيد التحضير</option>
                    <option value="completed" <?php echo $filter == 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                    <option value="cancelled" <?php echo $filter == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="رقم الطلب، الطاولة، أو اسم الموظف">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
    </div>
<?php endif; ?>

<!-- جدول الطلبات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة الطلبات (<?php echo count($orders); ?>)</h5>
    </div>
    <div class="card-body">
        <?php if (empty($orders)): ?>
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد طلبات</h4>
                <p class="text-muted">لم يتم العثور على طلبات تطابق معايير البحث</p>
                <a href="tables.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة طلب جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>الطاولة</th>
                            <th>الموظف</th>
                            <th>عدد العناصر</th>
                            <th>الإجمالي</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td>
                                    <strong>#<?php echo $order['id']; ?></strong>
                                </td>
                                <td>
                                    <i class="fas fa-table me-1"></i>
                                    <?php echo $order['table_number']; ?>
                                </td>
                                <td><?php echo $order['user_name']; ?></td>
                                <td>
                                    <span class="badge bg-info"><?php echo $order['items_count']; ?></span>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo formatPrice($order['total_amount']); ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'completed' => 'success',
                                        'cancelled' => 'danger'
                                    ];
                                    $status_text = [
                                        'pending' => 'معلق',
                                        'processing' => 'قيد التحضير',
                                        'completed' => 'مكتمل',
                                        'cancelled' => 'ملغي'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $status_colors[$order['status']]; ?>">
                                        <?php echo $status_text[$order['status']]; ?>
                                    </span>
                                </td>
                                <td>
                                    <small>
                                        <?php echo date('Y-m-d', strtotime($order['created_at'])); ?><br>
                                        <?php echo date('H:i', strtotime($order['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" onclick="viewOrder(<?php echo $order['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <?php if ($order['status'] != 'cancelled' && $order['status'] != 'completed'): ?>
                                            <button class="btn btn-outline-warning" onclick="editOrder(<?php echo $order['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            
                                            <button class="btn btn-outline-danger" onclick="cancelOrder(<?php echo $order['id']; ?>)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <button class="btn btn-outline-secondary" onclick="printInvoice(<?php echo $order['id']; ?>)">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- نافذة عرض تفاصيل الطلب -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderModalContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printInvoice(currentOrderId)">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إلغاء الطلب -->
<form id="cancelForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="cancel">
    <input type="hidden" name="order_id" id="cancelOrderId">
</form>

<script>
let currentOrderId = null;

function viewOrder(orderId) {
    currentOrderId = orderId;
    
    fetch(`ajax/get_order_details.php?order_id=${orderId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('orderModalContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('orderModal')).show();
        })
        .catch(error => {
            alert('حدث خطأ في جلب تفاصيل الطلب');
        });
}

function editOrder(orderId) {
    window.location.href = `edit_order.php?id=${orderId}`;
}

function cancelOrder(orderId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
        document.getElementById('cancelOrderId').value = orderId;
        document.getElementById('cancelForm').submit();
    }
}

function printInvoice(orderId) {
    window.open(`print_invoice.php?order_id=${orderId}`, '_blank', 'width=800,height=600');
}
</script>

<?php include 'includes/footer.php'; ?>
