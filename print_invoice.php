<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$order_id = intval($_GET['order_id'] ?? 0);

if ($order_id < 1) {
    die('معرف الطلب غير صحيح');
}

try {
    // جلب بيانات الطلب
    $stmt = $pdo->prepare("
        SELECT o.*, t.table_number, u.full_name as user_name
        FROM orders o
        JOIN tables t ON o.table_id = t.id
        JOIN users u ON o.user_id = u.id
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        die('الطلب غير موجود');
    }
    
    // جلب عناصر الطلب
    $stmt = $pdo->prepare("
        SELECT oi.*, p.name as product_name
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?
        ORDER BY oi.id
    ");
    $stmt->execute([$order_id]);
    $items = $stmt->fetchAll();
    
} catch (PDOException $e) {
    die('حدث خطأ في جلب البيانات');
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #<?php echo $order['id']; ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .invoice-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .invoice-header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .invoice-header h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .invoice-header p {
            margin: 2px 0;
            font-size: 12px;
        }
        
        .invoice-info {
            margin-bottom: 20px;
        }
        
        .invoice-info table {
            width: 100%;
            font-size: 12px;
        }
        
        .invoice-info td {
            padding: 2px 0;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .items-table th,
        .items-table td {
            padding: 5px;
            text-align: right;
            border-bottom: 1px solid #ddd;
            font-size: 12px;
        }
        
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .total-section {
            border-top: 2px solid #000;
            padding-top: 10px;
            margin-top: 15px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .total-row.final {
            font-size: 16px;
            font-weight: bold;
            border-top: 1px solid #000;
            padding-top: 5px;
        }
        
        .invoice-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #000;
            font-size: 11px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                max-width: none;
                margin: 0;
                padding: 10px;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        @page {
            size: 80mm auto;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <h1>مطعم الذواقة</h1>
            <p>العنوان: شارع الرشيد، بغداد</p>
            <p>الهاتف: 07901234567</p>
            <p>الرقم الضريبي: 123456789</p>
        </div>
        
        <!-- معلومات الفاتورة -->
        <div class="invoice-info">
            <table>
                <tr>
                    <td><strong>رقم الفاتورة:</strong></td>
                    <td>#<?php echo $order['id']; ?></td>
                </tr>
                <tr>
                    <td><strong>التاريخ:</strong></td>
                    <td><?php echo date('Y-m-d', strtotime($order['created_at'])); ?></td>
                </tr>
                <tr>
                    <td><strong>الوقت:</strong></td>
                    <td><?php echo date('H:i:s', strtotime($order['created_at'])); ?></td>
                </tr>
                <tr>
                    <td><strong>الطاولة:</strong></td>
                    <td><?php echo $order['table_number']; ?></td>
                </tr>
                <tr>
                    <td><strong>الكاشير:</strong></td>
                    <td><?php echo $order['user_name']; ?></td>
                </tr>
            </table>
        </div>
        
        <!-- عناصر الطلب -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($items as $item): ?>
                    <tr>
                        <td><?php echo $item['product_name']; ?></td>
                        <td><?php echo $item['quantity']; ?></td>
                        <td><?php echo number_format($item['price'], 0); ?> د.ع</td>
                        <td><?php echo number_format($item['price'] * $item['quantity'], 0); ?> د.ع</td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- إجمالي الفاتورة -->
        <div class="total-section">
            <div class="total-row final">
                <span>الإجمالي النهائي:</span>
                <span><?php echo number_format($order['total_amount'], 0); ?> د.ع</span>
            </div>
        </div>
        
        <!-- تذييل الفاتورة -->
        <div class="invoice-footer">
            <p><strong>شكراً لزيارتكم</strong></p>
            <p>نتطلع لخدمتكم مرة أخرى</p>
            <p>تم الطباعة في: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
        
        <!-- أزرار التحكم -->
        <div class="text-center mt-4 no-print">
            <button class="btn btn-primary" onclick="window.print()">
                طباعة الفاتورة
            </button>
            <button class="btn btn-secondary" onclick="window.close()">
                إغلاق
            </button>
        </div>
    </div>
    
    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            // تأخير قصير للسماح بتحميل الخطوط والأنماط
            setTimeout(function() {
                window.print();
            }, 500);
        };
        
        // إغلاق النافذة بعد الطباعة
        window.onafterprint = function() {
            window.close();
        };
    </script>
</body>
</html>
