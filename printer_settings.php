<?php
require_once 'config/database.php';
require_once 'includes/printer_functions.php';

// إنشاء مجلد config إذا لم يكن موجود
if (!file_exists('config')) {
    mkdir('config', 0755, true);
}

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('login.php');
}

$page_title = 'إعدادات الطابعة';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['save_settings'])) {
        try {
            // حفظ إعدادات طابعة المطبخ
            $kitchen_ip = $_POST['kitchen_ip'] ?? '*************';
            $kitchen_port = intval($_POST['kitchen_port'] ?? 9100);
            $kitchen_enabled = isset($_POST['kitchen_enabled']) ? 1 : 0;
            
            // حفظ إعدادات طابعة الكاشير
            $cashier_ip = $_POST['cashier_ip'] ?? '*************';
            $cashier_port = intval($_POST['cashier_port'] ?? 9100);
            $cashier_enabled = isset($_POST['cashier_enabled']) ? 1 : 0;
            
            // حفظ في قاعدة البيانات أو ملف إعدادات
            $settings = [
                'kitchen_printer' => [
                    'ip' => $kitchen_ip,
                    'port' => $kitchen_port,
                    'enabled' => $kitchen_enabled
                ],
                'cashier_printer' => [
                    'ip' => $cashier_ip,
                    'port' => $cashier_port,
                    'enabled' => $cashier_enabled
                ]
            ];
            
            file_put_contents('config/printer_settings.json', json_encode($settings, JSON_PRETTY_PRINT));
            
            showMessage('تم حفظ إعدادات الطابعة بنجاح', 'success');
            
        } catch (Exception $e) {
            showMessage('حدث خطأ في حفظ الإعدادات: ' . $e->getMessage(), 'error');
        }
    }
    
    // اختبار طابعة المطبخ
    if (isset($_POST['test_kitchen'])) {
        $printer = new RestaurantPrinter($_POST['kitchen_ip'], intval($_POST['kitchen_port']), 'kitchen');
        if ($printer->testPrinter()) {
            showMessage('طابعة المطبخ تعمل بنجاح!', 'success');
        } else {
            showMessage('فشل في الاتصال بطابعة المطبخ', 'error');
        }
    }
    
    // اختبار طابعة الكاشير
    if (isset($_POST['test_cashier'])) {
        $printer = new RestaurantPrinter($_POST['cashier_ip'], intval($_POST['cashier_port']), 'cashier');
        if ($printer->testPrinter()) {
            showMessage('طابعة الكاشير تعمل بنجاح!', 'success');
        } else {
            showMessage('فشل في الاتصال بطابعة الكاشير', 'error');
        }
    }
}

// قراءة الإعدادات الحالية
$settings_file = 'config/printer_settings.json';
$default_settings = [
    'kitchen_printer' => [
        'ip' => '*************',
        'port' => 9100,
        'enabled' => 1
    ],
    'cashier_printer' => [
        'ip' => '*************',
        'port' => 9100,
        'enabled' => 1
    ]
];

if (file_exists($settings_file)) {
    $settings = json_decode(file_get_contents($settings_file), true);
} else {
    $settings = $default_settings;
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-print me-2"></i>إعدادات الطابعة</h2>
    <a href="dashboard.php" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
    </a>
</div>

<div class="row">
    <!-- إعدادات الطابعة -->
    <div class="col-md-8">
        <form method="POST">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات الطابعات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- طابعة المطبخ -->
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-utensils me-2"></i>
                                        طابعة المطبخ
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="kitchen_enabled" name="kitchen_enabled" 
                                               <?php echo $settings['kitchen_printer']['enabled'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="kitchen_enabled">
                                            تفعيل طابعة المطبخ
                                        </label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="kitchen_ip" class="form-label">عنوان IP</label>
                                        <input type="text" class="form-control" id="kitchen_ip" name="kitchen_ip" 
                                               value="<?php echo $settings['kitchen_printer']['ip']; ?>" 
                                               placeholder="*************">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="kitchen_port" class="form-label">المنفذ (Port)</label>
                                        <input type="number" class="form-control" id="kitchen_port" name="kitchen_port" 
                                               value="<?php echo $settings['kitchen_printer']['port']; ?>" 
                                               placeholder="9100">
                                    </div>
                                    
                                    <button type="submit" name="test_kitchen" class="btn btn-outline-success w-100">
                                        <i class="fas fa-vial me-2"></i>اختبار الطابعة
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- طابعة الكاشير -->
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-cash-register me-2"></i>
                                        طابعة الكاشير
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="cashier_enabled" name="cashier_enabled" 
                                               <?php echo $settings['cashier_printer']['enabled'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="cashier_enabled">
                                            تفعيل طابعة الكاشير
                                        </label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="cashier_ip" class="form-label">عنوان IP</label>
                                        <input type="text" class="form-control" id="cashier_ip" name="cashier_ip" 
                                               value="<?php echo $settings['cashier_printer']['ip']; ?>" 
                                               placeholder="*************">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="cashier_port" class="form-label">المنفذ (Port)</label>
                                        <input type="number" class="form-control" id="cashier_port" name="cashier_port" 
                                               value="<?php echo $settings['cashier_printer']['port']; ?>" 
                                               placeholder="9100">
                                    </div>
                                    
                                    <button type="submit" name="test_cashier" class="btn btn-outline-info w-100">
                                        <i class="fas fa-vial me-2"></i>اختبار الطابعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="submit" name="save_settings" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <!-- معلومات ونصائح -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح الإعداد:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من اتصال الطابعات بالشبكة</li>
                        <li>استخدم طابعات حرارية 80 ملم</li>
                        <li>تأكد من دعم ESC/POS</li>
                        <li>اختبر الطابعات قبل الحفظ</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>متطلبات الشبكة:</h6>
                    <ul class="mb-0 small">
                        <li>عنوان IP ثابت للطابعات</li>
                        <li>المنفذ الافتراضي: 9100</li>
                        <li>تأكد من عدم حجب Firewall</li>
                        <li>اختبر الاتصال بـ ping</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>الطابعات المدعومة:</h6>
                    <ul class="mb-0 small">
                        <li>Epson TM-T82</li>
                        <li>Star TSP143</li>
                        <li>Citizen CT-S310</li>
                        <li>أي طابعة تدعم ESC/POS</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- اختبار سريع -->
        <div class="card mt-3">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    اختبار سريع
                </h6>
            </div>
            <div class="card-body">
                <p class="small">اختبر الطابعات بطلب تجريبي:</p>
                
                <div class="d-grid gap-2">
                    <a href="test_print.php?type=kitchen" class="btn btn-success btn-sm">
                        <i class="fas fa-utensils me-2"></i>اختبار طلب مطبخ
                    </a>
                    
                    <a href="test_print.php?type=cashier" class="btn btn-info btn-sm">
                        <i class="fas fa-receipt me-2"></i>اختبار فاتورة كاشير
                    </a>
                    
                    <a href="test_print.php?type=both" class="btn btn-primary btn-sm">
                        <i class="fas fa-print me-2"></i>اختبار الطابعتين
                    </a>
                </div>
            </div>
        </div>
        
        <!-- حالة الطابعات -->
        <div class="card mt-3">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0">
                    <i class="fas fa-heartbeat me-2"></i>
                    حالة الطابعات
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>طابعة المطبخ:</span>
                    <span class="badge bg-<?php echo $settings['kitchen_printer']['enabled'] ? 'success' : 'secondary'; ?>">
                        <?php echo $settings['kitchen_printer']['enabled'] ? 'مفعلة' : 'معطلة'; ?>
                    </span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>طابعة الكاشير:</span>
                    <span class="badge bg-<?php echo $settings['cashier_printer']['enabled'] ? 'success' : 'secondary'; ?>">
                        <?php echo $settings['cashier_printer']['enabled'] ? 'مفعلة' : 'معطلة'; ?>
                    </span>
                </div>
                
                <hr>
                
                <div class="small text-muted">
                    <div>آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات تقنية -->
<div class="card mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-code me-2"></i>
            معلومات تقنية للطابعات
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <h6>مواصفات الطابعة:</h6>
                <ul class="small">
                    <li>عرض الورق: 80 ملم</li>
                    <li>نوع الطباعة: حرارية</li>
                    <li>البروتوكول: ESC/POS</li>
                    <li>الاتصال: شبكة/USB</li>
                </ul>
            </div>
            
            <div class="col-md-3">
                <h6>أوامر ESC/POS:</h6>
                <ul class="small">
                    <li>تهيئة: ESC @</li>
                    <li>خط عريض: ESC E</li>
                    <li>توسيط: ESC a</li>
                    <li>قطع الورق: GS V</li>
                </ul>
            </div>
            
            <div class="col-md-3">
                <h6>إعدادات الشبكة:</h6>
                <ul class="small">
                    <li>بروتوكول: TCP/IP</li>
                    <li>منفذ افتراضي: 9100</li>
                    <li>مهلة الاتصال: 5 ثواني</li>
                    <li>إعادة المحاولة: 3 مرات</li>
                </ul>
            </div>
            
            <div class="col-md-3">
                <h6>استكشاف الأخطاء:</h6>
                <ul class="small">
                    <li>فحص اتصال الشبكة</li>
                    <li>تأكيد عنوان IP</li>
                    <li>فحص حالة الطابعة</li>
                    <li>إعادة تشغيل الطابعة</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// فحص حالة الطابعات كل 30 ثانية
setInterval(function() {
    // يمكن إضافة AJAX لفحص حالة الطابعات
    console.log('فحص حالة الطابعات...');
}, 30000);

// تحديث الصفحة عند تغيير الإعدادات
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input');
    
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // إضافة مؤشر للتغييرات غير المحفوظة
            document.title = '* ' + document.title.replace('* ', '');
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
