<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('login.php');
}

$page_title = 'إدارة المنتجات';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        try {
            if ($action == 'add') {
                $name = trim($_POST['name']);
                $description = trim($_POST['description']);
                $price = floatval($_POST['price']);
                $category_id = intval($_POST['category_id']);

                // التحقق من صحة البيانات
                if (empty($name)) {
                    throw new Exception('اسم المنتج مطلوب');
                }
                if ($price <= 0) {
                    throw new Exception('السعر يجب أن يكون أكبر من صفر');
                }
                if ($category_id <= 0) {
                    throw new Exception('يجب اختيار فئة صحيحة');
                }

                // التحقق من وجود الفئة
                $stmt = $pdo->prepare("SELECT id FROM categories WHERE id = ?");
                $stmt->execute([$category_id]);
                if (!$stmt->fetch()) {
                    throw new Exception('الفئة المختارة غير موجودة');
                }

                // معالجة رفع الصورة
                $image_path = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                    $upload_dir = 'uploads/products/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    // التحقق من حجم الملف (5MB كحد أقصى)
                    if ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                        throw new Exception('حجم الصورة كبير جداً (الحد الأقصى 5MB)');
                    }

                    $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

                    if (!in_array($file_extension, $allowed_extensions)) {
                        throw new Exception('صيغة الصورة غير مدعومة (JPG, PNG, GIF فقط)');
                    }

                    $new_filename = uniqid() . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_filename;

                    if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                        throw new Exception('فشل في رفع الصورة');
                    }

                    $image_path = $upload_path;
                }

                $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id, image_path) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$name, $description, $price, $category_id, $image_path]);

                showMessage('تم إضافة المنتج بنجاح', 'success');
                
            } elseif ($action == 'edit') {
                $id = intval($_POST['id']);
                $name = trim($_POST['name']);
                $description = trim($_POST['description']);
                $price = floatval($_POST['price']);
                $category_id = intval($_POST['category_id']);

                // التحقق من صحة البيانات
                if (empty($name)) {
                    throw new Exception('اسم المنتج مطلوب');
                }
                if ($price <= 0) {
                    throw new Exception('السعر يجب أن يكون أكبر من صفر');
                }
                if ($category_id <= 0) {
                    throw new Exception('يجب اختيار فئة صحيحة');
                }

                // التحقق من وجود المنتج
                $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ?");
                $stmt->execute([$id]);
                if (!$stmt->fetch()) {
                    throw new Exception('المنتج غير موجود');
                }

                // معالجة رفع الصورة الجديدة
                $image_update = '';
                $image_params = [];

                if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                    $upload_dir = 'uploads/products/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    // التحقق من حجم الملف
                    if ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                        throw new Exception('حجم الصورة كبير جداً (الحد الأقصى 5MB)');
                    }

                    $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

                    if (!in_array($file_extension, $allowed_extensions)) {
                        throw new Exception('صيغة الصورة غير مدعومة (JPG, PNG, GIF فقط)');
                    }

                    $new_filename = uniqid() . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_filename;

                    if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                        throw new Exception('فشل في رفع الصورة');
                    }

                    // حذف الصورة القديمة
                    $stmt = $pdo->prepare("SELECT image_path FROM products WHERE id = ?");
                    $stmt->execute([$id]);
                    $old_image = $stmt->fetchColumn();
                    if ($old_image && file_exists($old_image)) {
                        unlink($old_image);
                    }

                    $image_update = ', image_path = ?';
                    $image_params[] = $upload_path;
                }

                $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, price = ?, category_id = ? $image_update WHERE id = ?");
                $params = array_merge([$name, $description, $price, $category_id], $image_params, [$id]);
                $stmt->execute($params);

                showMessage('تم تحديث المنتج بنجاح', 'success');
                
            } elseif ($action == 'delete') {
                $id = intval($_POST['id']);
                
                // حذف الصورة
                $stmt = $pdo->prepare("SELECT image_path FROM products WHERE id = ?");
                $stmt->execute([$id]);
                $image_path = $stmt->fetchColumn();
                if ($image_path && file_exists($image_path)) {
                    unlink($image_path);
                }
                
                $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
                $stmt->execute([$id]);
                
                showMessage('تم حذف المنتج بنجاح', 'success');
            }
        } catch (Exception $e) {
            showMessage($e->getMessage(), 'error');
        } catch (PDOException $e) {
            showMessage('حدث خطأ في قاعدة البيانات: ' . $e->getMessage(), 'error');
        }
    }
}

// جلب الفئات
try {
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

// جلب المنتجات
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';

try {
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    if (!empty($category_filter)) {
        $where_conditions[] = "p.category_id = ?";
        $params[] = $category_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        $where_clause
        ORDER BY c.name, p.name
    ");
    $stmt->execute($params);
    $products = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب البيانات';
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-box me-2"></i>إدارة المنتجات</h2>
    <div>
        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#categoryModal">
            <i class="fas fa-plus me-2"></i>فئة جديدة
        </button>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal">
            <i class="fas fa-plus me-2"></i>منتج جديد
        </button>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="category" class="form-label">الفئة</label>
                <select class="form-select" id="category" name="category">
                    <option value="">جميع الفئات</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" 
                                <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo $category['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="اسم المنتج أو الوصف">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
    </div>
<?php endif; ?>

<!-- عرض المنتجات -->
<div class="row">
    <?php if (empty($products)): ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-box fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد منتجات</h4>
                <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal">
                    <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                </button>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($products as $product): ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <?php if ($product['image_path'] && file_exists($product['image_path'])): ?>
                        <img src="<?php echo $product['image_path']; ?>" class="card-img-top" 
                             style="height: 200px; object-fit: cover;" alt="<?php echo $product['name']; ?>">
                    <?php else: ?>
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <h5 class="card-title"><?php echo $product['name']; ?></h5>
                        <p class="card-text"><?php echo $product['description']; ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-secondary"><?php echo $product['category_name']; ?></span>
                            <strong class="text-success"><?php echo formatPrice($product['price']); ?></strong>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-primary btn-sm" 
                                    onclick="editProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo $product['name']; ?>')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- نافذة إضافة/تعديل منتج -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalTitle">إضافة منتج جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="productForm" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" id="productAction" value="add">
                <input type="hidden" name="id" id="productId">
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="productName" class="form-label">اسم المنتج</label>
                        <input type="text" class="form-control" id="productName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="productDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="productDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="productPrice" class="form-label">السعر</label>
                        <input type="number" class="form-control" id="productPrice" name="price" 
                               step="0.01" min="0" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="productCategory" class="form-label">الفئة</label>
                        <select class="form-select" id="productCategory" name="category_id" required>
                            <option value="">اختر الفئة</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo $category['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="productImage" class="form-label">صورة المنتج</label>
                        <input type="file" class="form-control" id="productImage" name="image" 
                               accept="image/*">
                        <div class="form-text">الصيغ المدعومة: JPG, PNG, GIF (حد أقصى 5MB)</div>
                        <div id="currentImage" style="display: none;">
                            <img id="currentImagePreview" src="" style="max-width: 100px; margin-top: 10px;">
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة إضافة فئة -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة فئة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="ajax/add_category.php" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">اسم الفئة</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج حذف المنتج -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="id" id="deleteProductId">
</form>

<script>
function editProduct(product) {
    document.getElementById('productModalTitle').textContent = 'تعديل المنتج';
    document.getElementById('productAction').value = 'edit';
    document.getElementById('productId').value = product.id;
    document.getElementById('productName').value = product.name;
    document.getElementById('productDescription').value = product.description;
    document.getElementById('productPrice').value = product.price;
    document.getElementById('productCategory').value = product.category_id;
    
    // عرض الصورة الحالية
    if (product.image_path) {
        document.getElementById('currentImage').style.display = 'block';
        document.getElementById('currentImagePreview').src = product.image_path;
    } else {
        document.getElementById('currentImage').style.display = 'none';
    }
    
    new bootstrap.Modal(document.getElementById('productModal')).show();
}

function deleteProduct(id, name) {
    if (confirm(`هل أنت متأكد من حذف المنتج "${name}"؟`)) {
        document.getElementById('deleteProductId').value = id;
        document.getElementById('deleteForm').submit();
    }
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.getElementById('productModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('productModalTitle').textContent = 'إضافة منتج جديد';
    document.getElementById('productAction').value = 'add';
    document.getElementById('productForm').reset();
    document.getElementById('currentImage').style.display = 'none';
});
</script>

<?php include 'includes/footer.php'; ?>
