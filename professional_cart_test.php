<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام السلة الاحترافي الجديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="alert alert-success">
            <h4><i class="fas fa-rocket me-2"></i>نظام السلة الاحترافي الجديد!</h4>
            <p><strong>تم إعادة كتابة النظام بالكامل باستخدام:</strong></p>
            <ul class="mb-0">
                <li><strong>Event Listeners</strong> بدلاً من onclick</li>
                <li><strong>دوال معالجة احترافية</strong> (handleQuantityChange, handleRemoveItem)</li>
                <li><strong>نظام رسائل محسن</strong> مع تشخيص مفصل</li>
                <li><strong>إزالة التعقيدات</strong> والاعتماد على طرق حديثة</li>
                <li><strong>معالجة أخطاء شاملة</strong> مع رسائل واضحة</li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            الفرق بين النظام القديم والجديد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ النظام القديم (onclick):</h6>
                                <pre class="bg-light p-2 rounded small"><code>&lt;button onclick="updateQuantity(12, 1)"&gt;
    &lt;i class="fas fa-plus"&gt;&lt;/i&gt;
&lt;/button&gt;</code></pre>
                                <div class="text-danger small">
                                    <i class="fas fa-times me-1"></i>معقد ومتداخل<br>
                                    <i class="fas fa-times me-1"></i>صعب التشخيص<br>
                                    <i class="fas fa-times me-1"></i>مشاكل في التحميل<br>
                                    <i class="fas fa-times me-1"></i>دوال مكررة
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-success">✅ النظام الجديد (Event Listeners):</h6>
                                <pre class="bg-light p-2 rounded small"><code>&lt;button class="cart-increase-btn" 
        data-product-id="12"&gt;
    &lt;i class="fas fa-plus"&gt;&lt;/i&gt;
&lt;/button&gt;</code></pre>
                                <div class="text-success small">
                                    <i class="fas fa-check me-1"></i>بسيط ونظيف<br>
                                    <i class="fas fa-check me-1"></i>سهل التشخيص<br>
                                    <i class="fas fa-check me-1"></i>يعمل دائماً<br>
                                    <i class="fas fa-check me-1"></i>دوال منظمة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-play me-2"></i>
                            اختبار النظام الجديد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>خطوات الاختبار:</strong>
                        </div>
                        
                        <ol class="list-group list-group-numbered">
                            <li class="list-group-item">
                                <strong>افتح الصفحة الحقيقية:</strong><br>
                                <a href="add_order.php?table_id=1" class="btn btn-success mt-2" target="_blank">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    add_order.php?table_id=1
                                </a>
                            </li>
                            
                            <li class="list-group-item">
                                <strong>افتح Developer Tools:</strong><br>
                                <kbd>F12</kbd> → <strong>Console</strong>
                            </li>
                            
                            <li class="list-group-item">
                                <strong>ابحث عن رسائل التهيئة:</strong><br>
                                <code class="text-success">🎉 تم تهيئة النظام الاحترافي بنجاح</code><br>
                                <code class="text-info">💡 استخدم Event Listeners بدلاً من onclick</code>
                            </li>
                            
                            <li class="list-group-item">
                                <strong>أضف منتج للسلة:</strong><br>
                                اضغط على أي منتج من القائمة
                            </li>
                            
                            <li class="list-group-item">
                                <strong>اختبر الأزرار الجديدة:</strong><br>
                                اضغط + أو - وراقب الرسائل:
                                <div class="mt-2">
                                    <code class="text-primary">➕ Event Listener: زيادة كمية منتج 12</code><br>
                                    <code class="text-warning">➖ Event Listener: تقليل كمية منتج 12</code>
                                </div>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            رسائل Console الجديدة
                        </h6>
                    </div>
                    <div class="card-body">
                        <h6 class="text-success">عند التهيئة:</h6>
                        <div class="bg-light p-2 rounded mb-3">
                            <small>
                                🔧 بدء تهيئة نظام السلة الاحترافي...<br>
                                🔍 فحص الدوال: {handleQuantityChange: "function"}<br>
                                ✅ جميع عناصر HTML موجودة<br>
                                🎉 تم تهيئة النظام الاحترافي بنجاح
                            </small>
                        </div>
                        
                        <h6 class="text-primary">عند الضغط على +:</h6>
                        <div class="bg-light p-2 rounded mb-3">
                            <small>
                                ➕ Event Listener: زيادة كمية منتج 12<br>
                                🔄 handleQuantityChange: منتج 12, تغيير 1<br>
                                📊 تغيير الكمية: 1 → 2<br>
                                ✅ تم تحديث كمية كباب عراقي إلى 2
                            </small>
                        </div>
                        
                        <h6 class="text-warning">عند الضغط على -:</h6>
                        <div class="bg-light p-2 rounded mb-3">
                            <small>
                                ➖ Event Listener: تقليل كمية منتج 12<br>
                                🔄 handleQuantityChange: منتج 12, تغيير -1<br>
                                📊 تغيير الكمية: 2 → 1<br>
                                ✅ تم تحديث كمية كباب عراقي إلى 1
                            </small>
                        </div>
                        
                        <h6 class="text-danger">عند الحذف:</h6>
                        <div class="bg-light p-2 rounded">
                            <small>
                                🗑️ Event Listener: حذف منتج 12<br>
                                🗑️ handleRemoveItem: منتج 12<br>
                                ✅ تم حذف كباب عراقي من السلة
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">أدوات التشخيص الجديدة</h6>
                    </div>
                    <div class="card-body">
                        <p class="small">استخدم هذه الأوامر في Console:</p>
                        
                        <div class="mb-2">
                            <strong>فحص Event Listeners:</strong>
                            <pre class="bg-light p-1 rounded small"><code>attachCartEventListeners()</code></pre>
                        </div>
                        
                        <div class="mb-2">
                            <strong>اختبار تغيير الكمية:</strong>
                            <pre class="bg-light p-1 rounded small"><code>handleQuantityChange(12, 1)</code></pre>
                        </div>
                        
                        <div class="mb-2">
                            <strong>اختبار حذف منتج:</strong>
                            <pre class="bg-light p-1 rounded small"><code>handleRemoveItem(12)</code></pre>
                        </div>
                        
                        <div class="mb-2">
                            <strong>عرض محتويات السلة:</strong>
                            <pre class="bg-light p-1 rounded small"><code>console.table(orderItems)</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- محاكاة النظام الجديد -->
        <div class="card mt-4">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">
                    <i class="fas fa-flask me-2"></i>
                    محاكاة النظام الجديد
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="alert alert-info">
                            <strong>اختبر النظام الجديد هنا قبل الانتقال للصفحة الحقيقية:</strong>
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-primary me-2" onclick="addTestProduct()">
                                <i class="fas fa-plus me-1"></i>إضافة منتج تجريبي
                            </button>
                            <button class="btn btn-secondary" onclick="clearTestCart()">
                                <i class="fas fa-trash me-1"></i>مسح السلة
                            </button>
                        </div>
                        
                        <div id="test-cart-container" class="border p-3 rounded bg-light">
                            <div class="text-center text-muted">
                                لم يتم إضافة أي منتجات بعد
                            </div>
                        </div>
                        
                        <div class="mt-3 d-flex justify-content-between align-items-center">
                            <strong>الإجمالي: <span id="test-total">0 د.ع</span></strong>
                            <button class="btn btn-success" onclick="testSubmitOrder()" disabled id="test-submit">
                                <i class="fas fa-check me-1"></i>تأكيد الطلب
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>سجل النظام الجديد:</h6>
                        <div id="test-log" style="height: 250px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
                        <button class="btn btn-sm btn-secondary mt-2 w-100" onclick="clearTestLog()">مسح السجل</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط الاختبار -->
        <div class="text-center mt-4">
            <h5>اختبر النظام الجديد:</h5>
            <div class="btn-group" role="group">
                <a href="add_order.php?table_id=1" class="btn btn-success" target="_blank">
                    <i class="fas fa-shopping-cart me-2"></i>النظام الجديد
                </a>
                <a href="debug_cart_buttons.php" class="btn btn-info">
                    <i class="fas fa-bug me-2"></i>تشخيص متقدم
                </a>
                <a href="dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h6><i class="fas fa-check-circle me-2"></i>مزايا النظام الجديد:</h6>
            <div class="row">
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>✅ <strong>Event Listeners احترافية</strong></li>
                        <li>✅ <strong>لا توجد دوال مكررة</strong></li>
                        <li>✅ <strong>معالجة أخطاء شاملة</strong></li>
                        <li>✅ <strong>رسائل تشخيص واضحة</strong></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>✅ <strong>كود نظيف ومنظم</strong></li>
                        <li>✅ <strong>سهولة الصيانة</strong></li>
                        <li>✅ <strong>أداء محسن</strong></li>
                        <li>✅ <strong>يعمل دائماً</strong></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // محاكاة النظام الجديد
        let testOrderItems = {};
        
        function logTest(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${icon} ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearTestLog() {
            document.getElementById('test-log').innerHTML = '';
            logTest('تم مسح السجل', 'info');
        }
        
        function addTestProduct() {
            const product = { id: 12, name: 'كباب عراقي', price: 8000 };
            logTest(`➕ إضافة ${product.name} (ID: ${product.id})`, 'info');
            
            if (testOrderItems[product.id]) {
                testOrderItems[product.id].quantity++;
                logTest(`📈 زيادة كمية ${product.name} إلى ${testOrderItems[product.id].quantity}`, 'success');
            } else {
                testOrderItems[product.id] = {
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    quantity: 1
                };
                logTest(`✅ تم إضافة ${product.name} للسلة`, 'success');
            }
            
            updateTestDisplay();
        }
        
        function testHandleQuantityChange(productId, change) {
            logTest(`${change > 0 ? '➕' : '➖'} Event Listener: ${change > 0 ? 'زيادة' : 'تقليل'} كمية منتج ${productId}`, change > 0 ? 'info' : 'warning');
            logTest(`🔄 handleQuantityChange: منتج ${productId}, تغيير ${change}`, 'info');
            
            if (!testOrderItems[productId]) {
                logTest(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
                return;
            }
            
            const item = testOrderItems[productId];
            const oldQuantity = item.quantity;
            const newQuantity = oldQuantity + change;
            
            logTest(`📊 تغيير الكمية: ${oldQuantity} → ${newQuantity}`, 'info');
            
            if (newQuantity <= 0) {
                const productName = item.name;
                delete testOrderItems[productId];
                logTest(`🗑️ تم حذف ${productName} من السلة`, 'warning');
            } else {
                item.quantity = newQuantity;
                logTest(`✅ تم تحديث كمية ${item.name} إلى ${newQuantity}`, 'success');
            }
            
            updateTestDisplay();
        }
        
        function testHandleRemoveItem(productId) {
            logTest(`🗑️ Event Listener: حذف منتج ${productId}`, 'error');
            logTest(`🗑️ handleRemoveItem: منتج ${productId}`, 'info');
            
            if (!testOrderItems[productId]) {
                logTest(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
                return;
            }
            
            const productName = testOrderItems[productId].name;
            delete testOrderItems[productId];
            logTest(`✅ تم حذف ${productName} من السلة`, 'success');
            
            updateTestDisplay();
        }
        
        function updateTestDisplay() {
            const container = document.getElementById('test-cart-container');
            const totalElement = document.getElementById('test-total');
            const submitButton = document.getElementById('test-submit');
            
            if (Object.keys(testOrderItems).length === 0) {
                container.innerHTML = '<div class="text-center text-muted">لم يتم إضافة أي منتجات بعد</div>';
                totalElement.textContent = '0 د.ع';
                submitButton.disabled = true;
                return;
            }
            
            let html = '';
            let total = 0;
            
            for (const item of Object.values(testOrderItems)) {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                
                html += `
                    <div class="order-item mb-3 border p-3 rounded bg-white" data-product-id="${item.id}">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="mb-1">${item.name}</h6>
                                <small class="text-muted">${item.price} د.ع</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="testHandleRemoveItem(${item.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="quantity-controls">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testHandleQuantityChange(${item.id}, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span class="quantity-display mx-2 px-2 py-1 bg-light border rounded">${item.quantity}</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testHandleQuantityChange(${item.id}, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <strong>${itemTotal} د.ع</strong>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            totalElement.textContent = total + ' د.ع';
            submitButton.disabled = false;
            
            logTest(`💰 الإجمالي: ${total} د.ع`, 'info');
            logTest(`📦 عدد العناصر: ${Object.keys(testOrderItems).length}`, 'info');
        }
        
        function clearTestCart() {
            testOrderItems = {};
            updateTestDisplay();
            logTest('🧹 تم مسح السلة', 'warning');
        }
        
        function testSubmitOrder() {
            logTest('🛒 تأكيد الطلب...', 'info');
            const total = Object.values(testOrderItems).reduce((sum, item) => sum + (item.price * item.quantity), 0);
            alert(`تم تأكيد الطلب!\nالعناصر: ${Object.keys(testOrderItems).length}\nالإجمالي: ${total} د.ع`);
            logTest('✅ تم تأكيد الطلب بنجاح', 'success');
            clearTestCart();
        }
        
        // بداية الاختبار
        document.addEventListener('DOMContentLoaded', function() {
            logTest('🚀 بدء اختبار النظام الاحترافي الجديد', 'info');
            logTest('💡 النظام يستخدم Event Listeners بدلاً من onclick', 'info');
            logTest('🎯 أضف منتج واختبر الأزرار الجديدة', 'info');
            
            console.log('🎉 مرحباً بك في النظام الاحترافي الجديد!');
            console.log('🔗 اختبر الصفحة الحقيقية: add_order.php?table_id=1');
        });
    </script>
</body>
</html>
