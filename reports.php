<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('login.php');
}

$page_title = 'التقارير';

// تواريخ افتراضية
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // بداية الشهر الحالي
$end_date = $_GET['end_date'] ?? date('Y-m-d'); // اليوم الحالي
$report_type = $_GET['report_type'] ?? 'sales';

try {
    // تقرير المبيعات
    if ($report_type == 'sales') {
        // إجمالي المبيعات
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_orders,
                COALESCE(SUM(total_amount), 0) as total_sales,
                AVG(total_amount) as avg_order_value
            FROM orders 
            WHERE DATE(created_at) BETWEEN ? AND ? 
            AND status = 'completed'
        ");
        $stmt->execute([$start_date, $end_date]);
        $sales_summary = $stmt->fetch();
        
        // المبيعات اليومية
        $stmt = $pdo->prepare("
            SELECT 
                DATE(created_at) as sale_date,
                COUNT(*) as orders_count,
                SUM(total_amount) as daily_sales
            FROM orders 
            WHERE DATE(created_at) BETWEEN ? AND ? 
            AND status = 'completed'
            GROUP BY DATE(created_at)
            ORDER BY sale_date DESC
        ");
        $stmt->execute([$start_date, $end_date]);
        $daily_sales = $stmt->fetchAll();
        
        // أفضل المنتجات مبيعاً
        $stmt = $pdo->prepare("
            SELECT 
                p.name,
                SUM(oi.quantity) as total_quantity,
                SUM(oi.quantity * oi.price) as total_revenue
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            JOIN orders o ON oi.order_id = o.id
            WHERE DATE(o.created_at) BETWEEN ? AND ? 
            AND o.status = 'completed'
            GROUP BY p.id, p.name
            ORDER BY total_quantity DESC
            LIMIT 10
        ");
        $stmt->execute([$start_date, $end_date]);
        $top_products = $stmt->fetchAll();
    }
    
    // تقرير الطلبات
    elseif ($report_type == 'orders') {
        // إحصائيات الطلبات
        $stmt = $pdo->prepare("
            SELECT 
                status,
                COUNT(*) as count
            FROM orders 
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY status
        ");
        $stmt->execute([$start_date, $end_date]);
        $order_stats = $stmt->fetchAll();
        
        // الطلبات حسب الطاولة
        $stmt = $pdo->prepare("
            SELECT 
                t.table_number,
                COUNT(o.id) as orders_count,
                COALESCE(SUM(o.total_amount), 0) as total_revenue
            FROM tables t
            LEFT JOIN orders o ON t.id = o.table_id 
                AND DATE(o.created_at) BETWEEN ? AND ?
                AND o.status = 'completed'
            GROUP BY t.id, t.table_number
            ORDER BY orders_count DESC
        ");
        $stmt->execute([$start_date, $end_date]);
        $table_stats = $stmt->fetchAll();
    }
    
    // تقرير الموظفين
    elseif ($report_type == 'staff') {
        // أداء الموظفين
        $stmt = $pdo->prepare("
            SELECT 
                u.full_name,
                u.role,
                COUNT(o.id) as orders_count,
                COALESCE(SUM(o.total_amount), 0) as total_sales
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id 
                AND DATE(o.created_at) BETWEEN ? AND ?
                AND o.status = 'completed'
            WHERE u.role IN ('admin', 'cashier')
            GROUP BY u.id, u.full_name, u.role
            ORDER BY total_sales DESC
        ");
        $stmt->execute([$start_date, $end_date]);
        $staff_performance = $stmt->fetchAll();
    }
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب البيانات';
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar me-2"></i>التقارير</h2>
    <button class="btn btn-success" onclick="exportReport()">
        <i class="fas fa-download me-2"></i>تصدير التقرير
    </button>
</div>

<!-- فلاتر التقرير -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="report_type" class="form-label">نوع التقرير</label>
                <select class="form-select" id="report_type" name="report_type">
                    <option value="sales" <?php echo $report_type == 'sales' ? 'selected' : ''; ?>>تقرير المبيعات</option>
                    <option value="orders" <?php echo $report_type == 'orders' ? 'selected' : ''; ?>>تقرير الطلبات</option>
                    <option value="staff" <?php echo $report_type == 'staff' ? 'selected' : ''; ?>>تقرير الموظفين</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="<?php echo $start_date; ?>" required>
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="<?php echo $end_date; ?>" required>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-chart-line me-2"></i>إنشاء التقرير
                </button>
            </div>
        </form>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
    </div>
<?php endif; ?>

<!-- تقرير المبيعات -->
<?php if ($report_type == 'sales'): ?>
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $sales_summary['total_orders']; ?></h4>
                            <p class="mb-0">إجمالي الطلبات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo formatPrice($sales_summary['total_sales']); ?></h4>
                            <p class="mb-0">إجمالي المبيعات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo formatPrice($sales_summary['avg_order_value']); ?></h4>
                            <p class="mb-0">متوسط قيمة الطلب</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- المبيعات اليومية -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">المبيعات اليومية</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>عدد الطلبات</th>
                                    <th>المبيعات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($daily_sales as $day): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d', strtotime($day['sale_date'])); ?></td>
                                        <td><?php echo $day['orders_count']; ?></td>
                                        <td><?php echo formatPrice($day['daily_sales']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أفضل المنتجات -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">أفضل المنتجات</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($top_products as $product): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1"><?php echo $product['name']; ?></h6>
                                <small class="text-muted"><?php echo $product['total_quantity']; ?> قطعة</small>
                            </div>
                            <strong><?php echo formatPrice($product['total_revenue']); ?></strong>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- تقرير الطلبات -->
<?php if ($report_type == 'orders'): ?>
    <div class="row">
        <!-- إحصائيات الطلبات -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إحصائيات الطلبات</h5>
                </div>
                <div class="card-body">
                    <?php
                    $status_colors = [
                        'pending' => 'warning',
                        'processing' => 'info',
                        'completed' => 'success',
                        'cancelled' => 'danger'
                    ];
                    $status_text = [
                        'pending' => 'معلق',
                        'processing' => 'قيد التحضير',
                        'completed' => 'مكتمل',
                        'cancelled' => 'ملغي'
                    ];
                    ?>
                    <?php foreach ($order_stats as $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="badge bg-<?php echo $status_colors[$stat['status']]; ?> fs-6">
                                <?php echo $status_text[$stat['status']]; ?>
                            </span>
                            <strong><?php echo $stat['count']; ?> طلب</strong>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات الطاولات -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">أداء الطاولات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الطاولة</th>
                                    <th>الطلبات</th>
                                    <th>الإيرادات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($table_stats as $table): ?>
                                    <tr>
                                        <td>طاولة <?php echo $table['table_number']; ?></td>
                                        <td><?php echo $table['orders_count']; ?></td>
                                        <td><?php echo formatPrice($table['total_revenue']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- تقرير الموظفين -->
<?php if ($report_type == 'staff'): ?>
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">أداء الموظفين</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم الموظف</th>
                            <th>الدور</th>
                            <th>عدد الطلبات</th>
                            <th>إجمالي المبيعات</th>
                            <th>متوسط الطلب</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($staff_performance as $staff): ?>
                            <tr>
                                <td><?php echo $staff['full_name']; ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $staff['role'] == 'admin' ? 'danger' : 'success'; ?>">
                                        <?php echo $staff['role'] == 'admin' ? 'مدير' : 'كاشير'; ?>
                                    </span>
                                </td>
                                <td><?php echo $staff['orders_count']; ?></td>
                                <td><?php echo formatPrice($staff['total_sales']); ?></td>
                                <td>
                                    <?php 
                                    $avg = $staff['orders_count'] > 0 ? $staff['total_sales'] / $staff['orders_count'] : 0;
                                    echo formatPrice($avg); 
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.open('export_report.php?' + params.toString(), '_blank');
}

// تحديث التواريخ السريعة
function setDateRange(days) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);
    
    document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
    document.getElementById('end_date').value = endDate.toISOString().split('T')[0];
}

// إضافة أزرار التواريخ السريعة
document.addEventListener('DOMContentLoaded', function() {
    const quickDates = document.createElement('div');
    quickDates.className = 'mt-2';
    quickDates.innerHTML = `
        <small class="text-muted">تواريخ سريعة:</small>
        <button type="button" class="btn btn-outline-secondary btn-sm ms-1" onclick="setDateRange(7)">7 أيام</button>
        <button type="button" class="btn btn-outline-secondary btn-sm ms-1" onclick="setDateRange(30)">30 يوم</button>
        <button type="button" class="btn btn-outline-secondary btn-sm ms-1" onclick="setDateRange(90)">90 يوم</button>
    `;
    
    document.querySelector('.card-body form').appendChild(quickDates);
});
</script>

<?php include 'includes/footer.php'; ?>
