<?php
// ملف الإعداد السريع للنظام

$step = intval($_GET['step'] ?? 1);
$error = '';
$success = '';

// متغيرات افتراضية
$default_host = 'localhost';
$default_username = 'root';
$default_password = '';
$default_database = 'restaurant_management_system';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($step == 1) {
        // اختبار الاتصال بقاعدة البيانات
        $host = trim($_POST['host'] ?? $default_host);
        $username = trim($_POST['username'] ?? $default_username);
        $password = $_POST['password'] ?? $default_password;
        $database = trim($_POST['database'] ?? $default_database);
        
        try {
            $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$database`");

            // إنشاء ملف إعدادات قاعدة البيانات الجديد
            $config_content = "<?php\n";
            $config_content .= "// إعدادات قاعدة البيانات\n";
            $config_content .= "define('DB_HOST', '" . addslashes($host) . "');\n";
            $config_content .= "define('DB_NAME', '" . addslashes($database) . "');\n";
            $config_content .= "define('DB_USER', '" . addslashes($username) . "');\n";
            $config_content .= "define('DB_PASS', '" . addslashes($password) . "');\n\n";
            $config_content .= "// إنشاء اتصال بقاعدة البيانات\n";
            $config_content .= "try {\n";
            $config_content .= "    \$pdo = new PDO(\"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\", DB_USER, DB_PASS);\n";
            $config_content .= "    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n";
            $config_content .= "    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);\n";
            $config_content .= "} catch(PDOException \$e) {\n";
            $config_content .= "    die(\"خطأ في الاتصال بقاعدة البيانات: \" . \$e->getMessage());\n";
            $config_content .= "}\n\n";
            $config_content .= "// بدء الجلسة\n";
            $config_content .= "if (session_status() == PHP_SESSION_NONE) {\n";
            $config_content .= "    session_start();\n";
            $config_content .= "}\n\n";
            $config_content .= "// دالة للتحقق من تسجيل الدخول\n";
            $config_content .= "function isLoggedIn() {\n";
            $config_content .= "    return isset(\$_SESSION['user_id']);\n";
            $config_content .= "}\n\n";
            $config_content .= "// دالة للتحقق من صلاحيات المستخدم\n";
            $config_content .= "function hasRole(\$role) {\n";
            $config_content .= "    return isset(\$_SESSION['user_role']) && \$_SESSION['user_role'] === \$role;\n";
            $config_content .= "}\n\n";
            $config_content .= "// دالة لإعادة التوجيه\n";
            $config_content .= "function redirect(\$url) {\n";
            $config_content .= "    header(\"Location: \$url\");\n";
            $config_content .= "    exit();\n";
            $config_content .= "}\n\n";
            $config_content .= "// دالة لعرض الرسائل\n";
            $config_content .= "function showMessage(\$message, \$type = 'info') {\n";
            $config_content .= "    \$_SESSION['message'] = \$message;\n";
            $config_content .= "    \$_SESSION['message_type'] = \$type;\n";
            $config_content .= "}\n\n";
            $config_content .= "// دالة لعرض الرسائل المحفوظة\n";
            $config_content .= "function displayMessage() {\n";
            $config_content .= "    if (isset(\$_SESSION['message'])) {\n";
            $config_content .= "        \$message = \$_SESSION['message'];\n";
            $config_content .= "        \$type = \$_SESSION['message_type'] ?? 'info';\n";
            $config_content .= "        unset(\$_SESSION['message']);\n";
            $config_content .= "        unset(\$_SESSION['message_type']);\n";
            $config_content .= "        \n";
            $config_content .= "        \$alertClass = '';\n";
            $config_content .= "        switch(\$type) {\n";
            $config_content .= "            case 'success':\n";
            $config_content .= "                \$alertClass = 'alert-success';\n";
            $config_content .= "                break;\n";
            $config_content .= "            case 'error':\n";
            $config_content .= "                \$alertClass = 'alert-danger';\n";
            $config_content .= "                break;\n";
            $config_content .= "            case 'warning':\n";
            $config_content .= "                \$alertClass = 'alert-warning';\n";
            $config_content .= "                break;\n";
            $config_content .= "            default:\n";
            $config_content .= "                \$alertClass = 'alert-info';\n";
            $config_content .= "        }\n";
            $config_content .= "        \n";
            $config_content .= "        echo \"<div class='alert \$alertClass alert-dismissible fade show' role='alert'>\n";
            $config_content .= "                \$message\n";
            $config_content .= "                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>\n";
            $config_content .= "              </div>\";\n";
            $config_content .= "    }\n";
            $config_content .= "}\n\n";
            $config_content .= "// دالة لتنسيق التاريخ والوقت\n";
            $config_content .= "function formatDateTime(\$datetime) {\n";
            $config_content .= "    return date('Y-m-d H:i:s', strtotime(\$datetime));\n";
            $config_content .= "}\n\n";
            $config_content .= "// دالة لتنسيق السعر\n";
            $config_content .= "function formatPrice(\$price) {\n";
            $config_content .= "    return number_format(\$price, 2) . ' ريال';\n";
            $config_content .= "}\n";
            $config_content .= "?>";

            file_put_contents('config/database.php', $config_content);
            
            $success = 'تم الاتصال بقاعدة البيانات بنجاح!';
            $step = 2;
            
        } catch (PDOException $e) {
            $error = 'خطأ في الاتصال: ' . $e->getMessage();
        }
    } elseif ($step == 2) {
        // إنشاء الجداول
        try {
            require_once 'config/database.php';
            
            $sql = file_get_contents('database.sql');
            $sql = str_replace('CREATE DATABASE IF NOT EXISTS restaurant_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;', '', $sql);
            $sql = str_replace('USE restaurant_management_system;', '', $sql);
            
            $pdo->exec($sql);
            
            $success = 'تم إنشاء الجداول وإدخال البيانات التجريبية بنجاح!';
            $step = 3;
            
        } catch (PDOException $e) {
            $error = 'خطأ في إنشاء الجداول: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام إدارة المطاعم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 30px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #6c5ce7;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step.pending {
            background: #e9ecef;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h2>إعداد نظام إدارة المطاعم</h2>
            <p class="mb-0">مرحباً بك! دعنا نقوم بإعداد النظام خطوة بخطوة</p>
        </div>
        
        <div class="setup-body">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? 'active' : 'pending'; ?>">3</div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <!-- الخطوة 1: إعداد قاعدة البيانات -->
                <h4>الخطوة 1: إعداد قاعدة البيانات</h4>
                <p class="text-muted">أدخل بيانات الاتصال بقاعدة البيانات MySQL</p>
                
                <form method="POST">
                    <input type="hidden" name="step" value="1">
                    
                    <div class="mb-3">
                        <label for="host" class="form-label">عنوان الخادم</label>
                        <input type="text" class="form-control" id="host" name="host" value="<?php echo htmlspecialchars($default_host); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($default_username); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password" value="<?php echo htmlspecialchars($default_password); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="database" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="database" name="database" value="<?php echo htmlspecialchars($default_database); ?>" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">اختبار الاتصال والمتابعة</button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <!-- الخطوة 2: إنشاء الجداول -->
                <h4>الخطوة 2: إنشاء الجداول</h4>
                <p class="text-muted">سيتم الآن إنشاء جداول قاعدة البيانات وإدخال البيانات التجريبية</p>
                
                <form method="POST">
                    <input type="hidden" name="step" value="2">
                    
                    <div class="alert alert-info">
                        <h6>سيتم إنشاء:</h6>
                        <ul class="mb-0">
                            <li>جداول النظام (المستخدمين، الطاولات، المنتجات، الطلبات)</li>
                            <li>بيانات تجريبية للاختبار</li>
                            <li>حسابات المستخدمين الافتراضية</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="btn btn-success w-100">إنشاء الجداول والبيانات</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- الخطوة 3: اكتمال الإعداد -->
                <h4>🎉 تم الإعداد بنجاح!</h4>
                <p class="text-muted">النظام جاهز للاستخدام الآن</p>
                
                <div class="alert alert-success">
                    <h6>بيانات تسجيل الدخول:</h6>
                    <table class="table table-sm mb-0">
                        <tr>
                            <td><strong>مدير النظام:</strong></td>
                            <td>admin / password123</td>
                        </tr>
                        <tr>
                            <td><strong>موظف كاشير:</strong></td>
                            <td>cashier / password123</td>
                        </tr>
                        <tr>
                            <td><strong>موظف مطبخ:</strong></td>
                            <td>kitchen / password123</td>
                        </tr>
                    </table>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="login.php" class="btn btn-primary">
                        بدء استخدام النظام
                    </a>
                    <a href="test_connection.php" class="btn btn-outline-info">
                        اختبار النظام
                    </a>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <small>
                        <strong>مهم:</strong> احذف ملف setup.php بعد اكتمال الإعداد لأسباب أمنية.
                    </small>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
