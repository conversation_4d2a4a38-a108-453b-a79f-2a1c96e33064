<?php
// ملف الإعداد السريع للنظام

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($step == 1) {
        // اختبار الاتصال بقاعدة البيانات
        $host = $_POST['host'];
        $username = $_POST['username'];
        $password = $_POST['password'];
        $database = $_POST['database'];
        
        try {
            $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$database`");
            
            // حفظ إعدادات الاتصال
            $config_content = "<?php\n";
            $config_content .= "// إعدادات قاعدة البيانات\n";
            $config_content .= "define('DB_HOST', '$host');\n";
            $config_content .= "define('DB_NAME', '$database');\n";
            $config_content .= "define('DB_USER', '$username');\n";
            $config_content .= "define('DB_PASS', '$password');\n\n";
            $config_content .= file_get_contents('config/database.php');
            $config_content = str_replace("<?php\n// إعدادات قاعدة البيانات\ndefine('DB_HOST', 'localhost');\ndefine('DB_NAME', 'restaurant_management_system');\ndefine('DB_USER', 'root');\ndefine('DB_PASS', '');", '', $config_content);
            
            file_put_contents('config/database.php', $config_content);
            
            $success = 'تم الاتصال بقاعدة البيانات بنجاح!';
            $step = 2;
            
        } catch (PDOException $e) {
            $error = 'خطأ في الاتصال: ' . $e->getMessage();
        }
    } elseif ($step == 2) {
        // إنشاء الجداول
        try {
            require_once 'config/database.php';
            
            $sql = file_get_contents('database.sql');
            $sql = str_replace('CREATE DATABASE IF NOT EXISTS restaurant_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;', '', $sql);
            $sql = str_replace('USE restaurant_management_system;', '', $sql);
            
            $pdo->exec($sql);
            
            $success = 'تم إنشاء الجداول وإدخال البيانات التجريبية بنجاح!';
            $step = 3;
            
        } catch (PDOException $e) {
            $error = 'خطأ في إنشاء الجداول: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام إدارة المطاعم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 30px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #6c5ce7;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step.pending {
            background: #e9ecef;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h2>إعداد نظام إدارة المطاعم</h2>
            <p class="mb-0">مرحباً بك! دعنا نقوم بإعداد النظام خطوة بخطوة</p>
        </div>
        
        <div class="setup-body">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? 'active' : 'pending'; ?>">3</div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <!-- الخطوة 1: إعداد قاعدة البيانات -->
                <h4>الخطوة 1: إعداد قاعدة البيانات</h4>
                <p class="text-muted">أدخل بيانات الاتصال بقاعدة البيانات MySQL</p>
                
                <form method="POST">
                    <input type="hidden" name="step" value="1">
                    
                    <div class="mb-3">
                        <label for="host" class="form-label">عنوان الخادم</label>
                        <input type="text" class="form-control" id="host" name="host" value="localhost" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" name="username" value="root" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password">
                    </div>
                    
                    <div class="mb-3">
                        <label for="database" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="database" name="database" value="restaurant_management_system" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">اختبار الاتصال والمتابعة</button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <!-- الخطوة 2: إنشاء الجداول -->
                <h4>الخطوة 2: إنشاء الجداول</h4>
                <p class="text-muted">سيتم الآن إنشاء جداول قاعدة البيانات وإدخال البيانات التجريبية</p>
                
                <form method="POST">
                    <input type="hidden" name="step" value="2">
                    
                    <div class="alert alert-info">
                        <h6>سيتم إنشاء:</h6>
                        <ul class="mb-0">
                            <li>جداول النظام (المستخدمين، الطاولات، المنتجات، الطلبات)</li>
                            <li>بيانات تجريبية للاختبار</li>
                            <li>حسابات المستخدمين الافتراضية</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="btn btn-success w-100">إنشاء الجداول والبيانات</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- الخطوة 3: اكتمال الإعداد -->
                <h4>🎉 تم الإعداد بنجاح!</h4>
                <p class="text-muted">النظام جاهز للاستخدام الآن</p>
                
                <div class="alert alert-success">
                    <h6>بيانات تسجيل الدخول:</h6>
                    <table class="table table-sm mb-0">
                        <tr>
                            <td><strong>مدير النظام:</strong></td>
                            <td>admin / password123</td>
                        </tr>
                        <tr>
                            <td><strong>موظف كاشير:</strong></td>
                            <td>cashier / password123</td>
                        </tr>
                        <tr>
                            <td><strong>موظف مطبخ:</strong></td>
                            <td>kitchen / password123</td>
                        </tr>
                    </table>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="login.php" class="btn btn-primary">
                        بدء استخدام النظام
                    </a>
                    <a href="test_connection.php" class="btn btn-outline-info">
                        اختبار النظام
                    </a>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <small>
                        <strong>مهم:</strong> احذف ملف setup.php بعد اكتمال الإعداد لأسباب أمنية.
                    </small>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
