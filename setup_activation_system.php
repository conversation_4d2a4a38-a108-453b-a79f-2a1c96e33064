<?php
/**
 * إعداد نظام التفعيل
 * Setup Activation System
 */

require_once 'config/database.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('login.php');
}

$page_title = 'إعداد نظام التفعيل';
$setup_complete = false;
$error_message = '';

// إنشاء جداول نظام التفعيل
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['setup_activation'])) {
    try {
        $pdo->beginTransaction();
        
        // جدول إعدادات النظام
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS system_settings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // جدول تراخيص التفعيل
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activation_licenses (
                id INT PRIMARY KEY AUTO_INCREMENT,
                license_key VARCHAR(255) UNIQUE NOT NULL,
                license_type ENUM('trial', 'full', 'premium') DEFAULT 'trial',
                max_orders INT DEFAULT 10,
                max_users INT DEFAULT 5,
                max_tables INT DEFAULT 10,
                expires_at DATETIME,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                activated_at TIMESTAMP NULL,
                activated_by INT,
                activation_data JSON,
                FOREIGN KEY (activated_by) REFERENCES users(id)
            )
        ");
        
        // جدول استخدام النظام
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS system_usage (
                id INT PRIMARY KEY AUTO_INCREMENT,
                usage_type ENUM('order', 'user', 'table', 'login') NOT NULL,
                user_id INT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ");
        
        // جدول سجل التفعيل
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activation_log (
                id INT PRIMARY KEY AUTO_INCREMENT,
                action ENUM('activate', 'deactivate', 'extend', 'upgrade') NOT NULL,
                license_key VARCHAR(255),
                user_id INT,
                old_data JSON,
                new_data JSON,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ");
        
        // إدراج الإعدادات الافتراضية
        $default_settings = [
            ['system_status', 'trial', 'حالة النظام: trial, active, expired'],
            ['trial_orders_limit', '10', 'حد الطلبات في النسخة التجريبية'],
            ['trial_duration_days', '30', 'مدة النسخة التجريبية بالأيام'],
            ['current_orders_count', '0', 'عدد الطلبات الحالي'],
            ['activation_date', NULL, 'تاريخ التفعيل'],
            ['license_key', NULL, 'مفتاح الترخيص الحالي'],
            ['system_version', '2.0', 'إصدار النظام'],
            ['last_check', NULL, 'آخر فحص للترخيص']
        ];
        
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO system_settings (setting_key, setting_value, description) 
            VALUES (?, ?, ?)
        ");
        
        foreach ($default_settings as $setting) {
            $stmt->execute($setting);
        }
        
        // إنشاء ترخيص تجريبي افتراضي
        $trial_key = 'TRIAL-' . strtoupper(bin2hex(random_bytes(8)));
        $trial_expires = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        $stmt = $pdo->prepare("
            INSERT INTO activation_licenses 
            (license_key, license_type, max_orders, expires_at, activation_data) 
            VALUES (?, 'trial', 10, ?, ?)
        ");
        
        $activation_data = json_encode([
            'created_by' => $_SESSION['user_id'],
            'created_at' => date('Y-m-d H:i:s'),
            'system_info' => [
                'php_version' => PHP_VERSION,
                'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
            ]
        ]);
        
        $stmt->execute([$trial_key, $trial_expires, $activation_data]);
        
        // تفعيل الترخيص التجريبي
        $stmt = $pdo->prepare("
            UPDATE system_settings 
            SET setting_value = ? 
            WHERE setting_key = ?
        ");
        
        $stmt->execute(['trial', 'system_status']);
        $stmt->execute([$trial_key, 'license_key']);
        $stmt->execute([date('Y-m-d H:i:s'), 'activation_date']);
        
        $pdo->commit();
        $setup_complete = true;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = 'حدث خطأ في إعداد النظام: ' . $e->getMessage();
    }
}

// فحص حالة النظام الحالية
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM system_settings");
$stmt->execute();
$current_settings = [];
while ($row = $stmt->fetch()) {
    $current_settings[$row['setting_key']] = $row['setting_value'];
}

include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            
            <?php if ($setup_complete): ?>
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle me-2"></i>تم إعداد نظام التفعيل بنجاح!</h4>
                    <p><strong>تم إنشاء ترخيص تجريبي:</strong></p>
                    <ul class="mb-0">
                        <li>مدة التجربة: 30 يوم</li>
                        <li>حد الطلبات: 10 طلبات</li>
                        <li>حالة النظام: تجريبي</li>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الإعداد</h5>
                    <p><?php echo htmlspecialchars($error_message); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        إعداد نظام التفعيل والترخيص
                    </h3>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات النظام
                            </h5>
                            
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>حالة النظام:</strong></td>
                                        <td>
                                            <?php 
                                            $status = $current_settings['system_status'] ?? 'غير مفعل';
                                            $status_class = [
                                                'trial' => 'warning',
                                                'active' => 'success',
                                                'expired' => 'danger'
                                            ];
                                            $class = $status_class[$status] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $class; ?>">
                                                <?php echo $status == 'trial' ? 'تجريبي' : ($status == 'active' ? 'مفعل' : ($status == 'expired' ? 'منتهي الصلاحية' : $status)); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>عدد الطلبات المستخدمة:</strong></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $current_settings['current_orders_count'] ?? '0'; ?> / 
                                                <?php echo $current_settings['trial_orders_limit'] ?? '10'; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ التفعيل:</strong></td>
                                        <td><?php echo $current_settings['activation_date'] ?? 'غير مفعل'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>مفتاح الترخيص:</strong></td>
                                        <td>
                                            <code class="small">
                                                <?php echo $current_settings['license_key'] ?? 'غير موجود'; ?>
                                            </code>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>إصدار النظام:</strong></td>
                                        <td><?php echo $current_settings['system_version'] ?? '2.0'; ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="text-success mb-3">
                                <i class="fas fa-cogs me-2"></i>
                                إعداد النظام
                            </h5>
                            
                            <?php if (empty($current_settings)): ?>
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>النظام غير مُعد</h6>
                                    <p>يجب إعداد نظام التفعيل أولاً لبدء استخدام النظام.</p>
                                </div>
                                
                                <form method="POST">
                                    <button type="submit" name="setup_activation" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-rocket me-2"></i>
                                        إعداد نظام التفعيل
                                    </button>
                                </form>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle me-2"></i>النظام مُعد ومجهز</h6>
                                    <p>نظام التفعيل جاهز للاستخدام.</p>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="activation_manager.php" class="btn btn-primary">
                                        <i class="fas fa-key me-2"></i>
                                        إدارة التفعيل
                                    </a>
                                    <a href="license_generator.php" class="btn btn-success">
                                        <i class="fas fa-certificate me-2"></i>
                                        مولد التراخيص
                                    </a>
                                    <a href="system_status.php" class="btn btn-info">
                                        <i class="fas fa-chart-line me-2"></i>
                                        حالة النظام
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="card mt-4 border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        كيف يعمل نظام التفعيل؟
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fas fa-gift fa-3x text-warning mb-3"></i>
                                <h6>النسخة التجريبية</h6>
                                <ul class="list-unstyled small text-muted">
                                    <li>✅ 10 طلبات مجانية</li>
                                    <li>✅ 30 يوم تجربة</li>
                                    <li>✅ جميع المزايا الأساسية</li>
                                    <li>❌ بدون دعم فني</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fas fa-star fa-3x text-success mb-3"></i>
                                <h6>النسخة الكاملة</h6>
                                <ul class="list-unstyled small text-muted">
                                    <li>✅ طلبات غير محدودة</li>
                                    <li>✅ بدون انتهاء صلاحية</li>
                                    <li>✅ جميع المزايا</li>
                                    <li>✅ دعم فني كامل</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fas fa-crown fa-3x text-primary mb-3"></i>
                                <h6>النسخة المتقدمة</h6>
                                <ul class="list-unstyled small text-muted">
                                    <li>✅ جميع مزايا الكاملة</li>
                                    <li>✅ تقارير متقدمة</li>
                                    <li>✅ تكامل مع أنظمة أخرى</li>
                                    <li>✅ دعم أولوية عالية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
