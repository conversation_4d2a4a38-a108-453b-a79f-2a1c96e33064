<?php
// ملف التثبيت المبسط
error_reporting(E_ALL);
ini_set('display_errors', 1);

$message = '';
$success = false;

// التحقق من وجود ملف الإعداد
if (file_exists('config/database.php')) {
    try {
        include_once 'config/database.php';
        $message = "<div class='success'>✅ النظام مثبت بالفعل! <a href='login.php'>تسجيل الدخول</a></div>";
        $success = true;
    } catch (Exception $e) {
        // متابعة التثبيت
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && !$success) {
    $host = $_POST['host'] ?? 'localhost';
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    $database = $_POST['database'] ?? 'restaurant_management_system';
    
    try {
        // الاتصال بـ MySQL
        $dsn = "mysql:host=$host;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$database`");
        
        // قراءة ملف SQL
        if (!file_exists('database.sql')) {
            throw new Exception('ملف database.sql غير موجود');
        }
        
        $sql = file_get_contents('database.sql');
        
        // تنظيف SQL
        $sql = str_replace('CREATE DATABASE IF NOT EXISTS restaurant_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;', '', $sql);
        $sql = str_replace('USE restaurant_management_system;', '', $sql);
        
        // تنفيذ SQL
        $pdo->exec($sql);
        
        // إنشاء مجلد config
        if (!is_dir('config')) {
            mkdir('config', 0755, true);
        }
        
        // إنشاء ملف الإعداد
        $config = "<?php\n";
        $config .= "define('DB_HOST', '$host');\n";
        $config .= "define('DB_NAME', '$database');\n";
        $config .= "define('DB_USER', '$username');\n";
        $config .= "define('DB_PASS', '$password');\n\n";
        
        $config .= "try {\n";
        $config .= "    \$pdo = new PDO(\"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\", DB_USER, DB_PASS);\n";
        $config .= "    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n";
        $config .= "    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);\n";
        $config .= "} catch(PDOException \$e) {\n";
        $config .= "    die(\"خطأ في الاتصال: \" . \$e->getMessage());\n";
        $config .= "}\n\n";
        
        $config .= "if (session_status() == PHP_SESSION_NONE) {\n";
        $config .= "    session_start();\n";
        $config .= "}\n\n";
        
        $config .= "function isLoggedIn() {\n";
        $config .= "    return isset(\$_SESSION['user_id']);\n";
        $config .= "}\n\n";
        
        $config .= "function hasRole(\$role) {\n";
        $config .= "    return isset(\$_SESSION['user_role']) && \$_SESSION['user_role'] === \$role;\n";
        $config .= "}\n\n";
        
        $config .= "function redirect(\$url) {\n";
        $config .= "    header(\"Location: \$url\");\n";
        $config .= "    exit();\n";
        $config .= "}\n\n";
        
        $config .= "function showMessage(\$message, \$type = 'info') {\n";
        $config .= "    \$_SESSION['message'] = \$message;\n";
        $config .= "    \$_SESSION['message_type'] = \$type;\n";
        $config .= "}\n\n";
        
        $config .= "function displayMessage() {\n";
        $config .= "    if (isset(\$_SESSION['message'])) {\n";
        $config .= "        \$message = \$_SESSION['message'];\n";
        $config .= "        \$type = \$_SESSION['message_type'] ?? 'info';\n";
        $config .= "        unset(\$_SESSION['message'], \$_SESSION['message_type']);\n";
        $config .= "        \$class = \$type == 'success' ? 'alert-success' : (\$type == 'error' ? 'alert-danger' : 'alert-info');\n";
        $config .= "        echo \"<div class='alert \$class'>\$message</div>\";\n";
        $config .= "    }\n";
        $config .= "}\n\n";
        
        $config .= "function formatDateTime(\$datetime) {\n";
        $config .= "    return date('Y-m-d H:i:s', strtotime(\$datetime));\n";
        $config .= "}\n\n";
        
        $config .= "function formatPrice(\$price) {\n";
        $config .= "    return number_format(\$price, 2) . ' ريال';\n";
        $config .= "}\n";
        $config .= "?>";
        
        file_put_contents('config/database.php', $config);
        
        $message = "<div class='success'>🎉 تم التثبيت بنجاح!<br>";
        $message .= "<strong>بيانات الدخول:</strong><br>";
        $message .= "مدير: admin / password123<br>";
        $message .= "كاشير: cashier / password123<br>";
        $message .= "مطبخ: kitchen / password123<br>";
        $message .= "<a href='login.php' class='btn'>بدء الاستخدام</a></div>";
        $success = true;
        
    } catch (Exception $e) {
        $message = "<div class='error'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام المطاعم</title>
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content { padding: 30px; }
        .form-group { margin-bottom: 20px; }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
        }
        input:focus {
            border-color: #6c5ce7;
            outline: none;
        }
        .btn {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .btn:hover {
            background: linear-gradient(135deg, #5a4fcf, #8b7eff);
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin-bottom: 20px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍽️ نظام إدارة المطاعم</h1>
            <p>تثبيت سريع وآمن</p>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <?php echo $message; ?>
            <?php endif; ?>
            
            <?php if (!$success): ?>
                <h3>إعداد قاعدة البيانات</h3>
                <p>أدخل بيانات MySQL:</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label>عنوان الخادم:</label>
                        <input type="text" name="host" value="localhost" required>
                    </div>
                    
                    <div class="form-group">
                        <label>اسم المستخدم:</label>
                        <input type="text" name="username" value="root" required>
                    </div>
                    
                    <div class="form-group">
                        <label>كلمة المرور:</label>
                        <input type="password" name="password" value="">
                    </div>
                    
                    <div class="form-group">
                        <label>اسم قاعدة البيانات:</label>
                        <input type="text" name="database" value="restaurant_management_system" required>
                    </div>
                    
                    <button type="submit" class="btn">تثبيت النظام</button>
                </form>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
