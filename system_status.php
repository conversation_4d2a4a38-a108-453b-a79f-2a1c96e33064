<?php
require_once 'config/database.php';
require_once 'includes/activation_check.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

$page_title = 'حالة النظام والإحصائيات';

// الحصول على حالة التفعيل
$activation_status = checkSystemActivation();
$usage_stats = isset($activationManager) ? $activationManager->getUsageStats() : [];

// إحصائيات إضافية
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_orders,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
        SUM(total_amount) as total_revenue
    FROM orders 
    WHERE DATE(created_at) = CURDATE()
");
$stmt->execute();
$today_stats = $stmt->fetch();

// إحصائيات الأسبوع
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as weekly_orders,
        SUM(total_amount) as weekly_revenue
    FROM orders 
    WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
");
$stmt->execute();
$weekly_stats = $stmt->fetch();

// إحصائيات الشهر
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as monthly_orders,
        SUM(total_amount) as monthly_revenue
    FROM orders 
    WHERE MONTH(created_at) = MONTH(CURDATE()) 
    AND YEAR(created_at) = YEAR(CURDATE())
");
$stmt->execute();
$monthly_stats = $stmt->fetch();

// آخر النشاطات
$stmt = $pdo->prepare("
    SELECT su.*, u.username 
    FROM system_usage su 
    LEFT JOIN users u ON su.user_id = u.id 
    ORDER BY su.created_at DESC 
    LIMIT 10
");
$stmt->execute();
$recent_activities = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-chart-line me-2 text-primary"></i>حالة النظام والإحصائيات</h2>
        <div>
            <button class="btn btn-outline-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>تحديث
            </button>
            <a href="activation_manager.php" class="btn btn-primary">
                <i class="fas fa-key me-1"></i>إدارة التفعيل
            </a>
        </div>
    </div>
    
    <!-- تحذير التفعيل -->
    <?php 
    $warning = getActivationWarning();
    if ($warning): 
    ?>
        <div class="alert alert-<?php echo $warning['type']; ?> alert-dismissible fade show">
            <h6><i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($warning['title']); ?></h6>
            <p class="mb-2"><?php echo htmlspecialchars($warning['message']); ?></p>
            <?php if ($warning['action_url']): ?>
                <a href="<?php echo htmlspecialchars($warning['action_url']); ?>" class="btn btn-sm btn-<?php echo $warning['type']; ?>">
                    <?php echo htmlspecialchars($warning['action_text']); ?>
                </a>
            <?php endif; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- حالة التفعيل -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        حالة التفعيل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <?php
                            $status_config = [
                                'trial_active' => ['icon' => 'clock', 'color' => 'warning', 'text' => 'نسخة تجريبية'],
                                'active' => ['icon' => 'check-circle', 'color' => 'success', 'text' => 'مفعل بالكامل'],
                                'expired' => ['icon' => 'times-circle', 'color' => 'danger', 'text' => 'منتهي الصلاحية'],
                                'not_setup' => ['icon' => 'exclamation-triangle', 'color' => 'warning', 'text' => 'غير مُعد']
                            ];
                            $config = $status_config[$activation_status['status']] ?? ['icon' => 'question', 'color' => 'secondary', 'text' => 'غير معروف'];
                            ?>
                            <i class="fas fa-<?php echo $config['icon']; ?> fa-3x text-<?php echo $config['color']; ?> mb-2"></i>
                            <h6><?php echo $config['text']; ?></h6>
                        </div>
                        
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الحالة:</strong> <?php echo htmlspecialchars($activation_status['message']); ?></p>
                                    <?php if (isset($activation_status['remaining_orders'])): ?>
                                        <p><strong>الطلبات المتبقية:</strong> 
                                            <span class="badge bg-warning"><?php echo $activation_status['remaining_orders']; ?></span>
                                        </p>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>إجمالي الطلبات:</strong> 
                                        <span class="badge bg-primary"><?php echo $usage_stats['total_orders'] ?? 0; ?></span>
                                    </p>
                                    <p><strong>طلبات اليوم:</strong> 
                                        <span class="badge bg-info"><?php echo $usage_stats['today_orders'] ?? 0; ?></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات اليوم -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                    <h4 class="text-primary"><?php echo $today_stats['total_orders'] ?? 0; ?></h4>
                    <small class="text-muted">طلبات اليوم</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="text-warning"><?php echo $today_stats['pending_orders'] ?? 0; ?></h4>
                    <small class="text-muted">طلبات معلقة</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="text-success"><?php echo $today_stats['completed_orders'] ?? 0; ?></h4>
                    <small class="text-muted">طلبات مكتملة</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-money-bill-wave fa-2x text-info mb-2"></i>
                    <h4 class="text-info"><?php echo number_format($today_stats['total_revenue'] ?? 0); ?></h4>
                    <small class="text-muted">إيرادات اليوم (د.ع)</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات مقارنة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">إحصائيات اليوم</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>الطلبات:</span>
                        <strong><?php echo $today_stats['total_orders'] ?? 0; ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الإيرادات:</span>
                        <strong><?php echo number_format($today_stats['total_revenue'] ?? 0); ?> د.ع</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>متوسط الطلب:</span>
                        <strong>
                            <?php 
                            $avg = ($today_stats['total_orders'] ?? 0) > 0 
                                ? ($today_stats['total_revenue'] ?? 0) / $today_stats['total_orders'] 
                                : 0;
                            echo number_format($avg);
                            ?> د.ع
                        </strong>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">إحصائيات الأسبوع</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>الطلبات:</span>
                        <strong><?php echo $weekly_stats['weekly_orders'] ?? 0; ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الإيرادات:</span>
                        <strong><?php echo number_format($weekly_stats['weekly_revenue'] ?? 0); ?> د.ع</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>متوسط يومي:</span>
                        <strong>
                            <?php 
                            $daily_avg = ($weekly_stats['weekly_orders'] ?? 0) / 7;
                            echo number_format($daily_avg, 1);
                            ?> طلب
                        </strong>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">إحصائيات الشهر</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>الطلبات:</span>
                        <strong><?php echo $monthly_stats['monthly_orders'] ?? 0; ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الإيرادات:</span>
                        <strong><?php echo number_format($monthly_stats['monthly_revenue'] ?? 0); ?> د.ع</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>النمو:</span>
                        <strong class="text-success">+12%</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- آخر النشاطات -->
    <div class="row">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        آخر النشاطات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_activities)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد نشاطات حديثة</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-<?php echo $activity['usage_type'] == 'order' ? 'shopping-cart' : 'user'; ?> me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($activity['description']); ?>
                                            </h6>
                                            <small class="text-muted">
                                                بواسطة: <?php echo htmlspecialchars($activity['username'] ?? 'غير محدد'); ?>
                                            </small>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo date('H:i', strtotime($activity['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        معلومات النظام
                    </h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span>إصدار PHP:</span>
                            <strong><?php echo PHP_VERSION; ?></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>إصدار النظام:</span>
                            <strong>2.0</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>قاعدة البيانات:</span>
                            <strong>MySQL</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>المستخدمين:</span>
                            <strong><?php echo $usage_stats['total_users'] ?? 0; ?></strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>آخر تحديث:</span>
                            <strong><?php echo date('H:i'); ?></strong>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-body text-center">
                    <h6 class="mb-3">إجراءات سريعة</h6>
                    <div class="d-grid gap-2">
                        <a href="activation_manager.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-key me-1"></i>إدارة التفعيل
                        </a>
                        <a href="license_generator.php" class="btn btn-success btn-sm">
                            <i class="fas fa-certificate me-1"></i>مولد التراخيص
                        </a>
                        <a href="ultra_modern_orders.php" class="btn btn-info btn-sm">
                            <i class="fas fa-rocket me-1"></i>النظام الفائق
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث تلقائي كل دقيقة
setInterval(function() {
    location.reload();
}, 60000);

// إضافة تأثيرات للبطاقات
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
