<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$page_title = 'إدارة الطاولات';

// جلب جميع الطاولات مع معلومات الطلبات النشطة
try {
    $stmt = $pdo->query("
        SELECT t.*, 
               o.id as order_id, 
               o.total_amount,
               o.created_at as order_time,
               COUNT(oi.id) as items_count
        FROM tables t
        LEFT JOIN orders o ON t.id = o.table_id AND o.status IN ('pending', 'processing')
        LEFT JOIN order_items oi ON o.id = oi.order_id
        GROUP BY t.id, o.id
        ORDER BY CAST(t.table_number AS UNSIGNED)
    ");
    $tables = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب بيانات الطاولات';
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-table me-2"></i>إدارة الطاولات</h2>
    <?php if (hasRole('admin')): ?>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTableModal">
        <i class="fas fa-plus me-2"></i>إضافة طاولة جديدة
    </button>
    <?php endif; ?>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
    </div>
<?php endif; ?>

<!-- عرض الطاولات -->
<div class="row">
    <?php foreach ($tables as $table): ?>
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card table-card <?php echo $table['status'] == 'available' ? 'table-available' : 'table-occupied'; ?>" 
                 onclick="<?php echo $table['status'] == 'available' ? "location.href='add_order.php?table_id={$table['id']}'" : "location.href='view_order.php?order_id={$table['order_id']}'"; ?>">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-table fa-3x"></i>
                    </div>
                    
                    <h4 class="card-title">طاولة <?php echo $table['table_number']; ?></h4>
                    
                    <p class="card-text">
                        <i class="fas fa-users me-1"></i>
                        <?php echo $table['capacity']; ?> أشخاص
                    </p>
                    
                    <?php if ($table['status'] == 'occupied' && $table['order_id']): ?>
                        <div class="mt-3">
                            <div class="badge bg-light text-dark mb-2">
                                طلب #<?php echo $table['order_id']; ?>
                            </div>
                            <div class="small">
                                <div><?php echo $table['items_count']; ?> عنصر</div>
                                <div><?php echo formatPrice($table['total_amount']); ?></div>
                                <div class="text-light">
                                    <?php echo date('H:i', strtotime($table['order_time'])); ?>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark">متاحة</span>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if (hasRole('admin')): ?>
                <div class="card-footer bg-transparent border-0">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-sm btn-outline-light" onclick="event.stopPropagation(); editTable(<?php echo $table['id']; ?>, '<?php echo $table['table_number']; ?>', <?php echo $table['capacity']; ?>)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-light" onclick="event.stopPropagation(); deleteTable(<?php echo $table['id']; ?>)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<?php if (empty($tables)): ?>
    <div class="text-center py-5">
        <i class="fas fa-table fa-5x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد طاولات</h4>
        <p class="text-muted">قم بإضافة طاولات جديدة لبدء استقبال الطلبات</p>
    </div>
<?php endif; ?>

<?php if (hasRole('admin')): ?>
<!-- نافذة إضافة طاولة جديدة -->
<div class="modal fade" id="addTableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة طاولة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="ajax/add_table.php" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="table_number" class="form-label">رقم الطاولة</label>
                        <input type="text" class="form-control" id="table_number" name="table_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="capacity" class="form-label">عدد الأشخاص</label>
                        <input type="number" class="form-control" id="capacity" name="capacity" min="1" max="20" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل الطاولة -->
<div class="modal fade" id="editTableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الطاولة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="ajax/edit_table.php" method="POST">
                <input type="hidden" id="edit_table_id" name="table_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_table_number" class="form-label">رقم الطاولة</label>
                        <input type="text" class="form-control" id="edit_table_number" name="table_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_capacity" class="form-label">عدد الأشخاص</label>
                        <input type="number" class="form-control" id="edit_capacity" name="capacity" min="1" max="20" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editTable(id, number, capacity) {
    document.getElementById('edit_table_id').value = id;
    document.getElementById('edit_table_number').value = number;
    document.getElementById('edit_capacity').value = capacity;
    new bootstrap.Modal(document.getElementById('editTableModal')).show();
}

function deleteTable(id) {
    if (confirm('هل أنت متأكد من حذف هذه الطاولة؟')) {
        window.location.href = 'ajax/delete_table.php?id=' + id;
    }
}
</script>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
