<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة طلب - طاولة 1</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <h4><i class="fas fa-info-circle me-2"></i>اختبار أزرار + و - في إضافة الطلب</h4>
                    <p>هذه صفحة اختبار مبسطة لتشخيص مشكلة الأزرار في صفحة إضافة الطلب</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- المنتجات -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-utensils me-2"></i>
                            المنتجات المتاحة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="products-container">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
                
                <!-- سجل الأحداث -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bug me-2"></i>
                            سجل الأحداث والتشخيص
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="debug-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-secondary" onclick="clearLog()">مسح السجل</button>
                            <button class="btn btn-sm btn-info" onclick="showOrderItems()">عرض السلة</button>
                            <button class="btn btn-sm btn-warning" onclick="testAllFunctions()">اختبار الدوال</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- سلة الطلب -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            طلب طاولة 1
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="order-items">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                <p>لم يتم إضافة أي منتجات بعد</p>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <strong>الإجمالي:</strong>
                            <strong id="total-amount" class="text-success">0 د.ع</strong>
                        </div>
                        
                        <button type="button" class="btn btn-success w-100" id="submit-order" onclick="submitOrder()" disabled>
                            <i class="fas fa-check me-2"></i>
                            تأكيد الطلب
                        </button>
                    </div>
                </div>
                
                <!-- معلومات التشخيص -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">معلومات التشخيص</h6>
                    </div>
                    <div class="card-body">
                        <div id="diagnostic-info">
                            <div><strong>JavaScript:</strong> <span id="js-status">جاري الفحص...</span></div>
                            <div><strong>عناصر HTML:</strong> <span id="html-status">جاري الفحص...</span></div>
                            <div><strong>الدوال:</strong> <span id="functions-status">جاري الفحص...</span></div>
                            <div><strong>السلة:</strong> <span id="cart-status">فارغة</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أزرار التنقل -->
        <div class="text-center mt-4">
            <a href="add_order.php?table_id=1" class="btn btn-primary me-2">
                <i class="fas fa-external-link-alt me-2"></i>
                الصفحة الحقيقية
            </a>
            <a href="browser_fix.php" class="btn btn-warning me-2">
                <i class="fas fa-tools me-2"></i>
                أداة إصلاح المتصفح
            </a>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-home me-2"></i>
                لوحة التحكم
            </a>
        </div>
    </div>

    <style>
        .product-card {
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .order-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quantity-display {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            min-width: 50px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }
    </style>

    <script>
        // بيانات المنتجات (نفس البيانات من الصفحة الحقيقية)
        const products = [
            { id: 1, name: 'شاي عراقي', description: 'شاي أصيل بالهيل', price: 1000, category_id: 1 },
            { id: 2, name: 'قهوة عربية', description: 'قهوة مميزة', price: 1500, category_id: 1 },
            { id: 3, name: 'كباب عراقي', description: 'كباب مشوي على الفحم', price: 8000, category_id: 2 },
            { id: 4, name: 'دولمة عراقية', description: 'دولمة تقليدية', price: 6000, category_id: 2 },
            { id: 5, name: 'حمص بالطحينة', description: 'حمص طازج', price: 3000, category_id: 3 }
        ];
        
        // سلة الطلب (نفس المتغير من الصفحة الحقيقية)
        let orderItems = {};
        
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${icon} ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            // تحديث حالة السلة
            updateCartStatus();
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }
        
        // إضافة منتج للطلب (نفس الدالة من الصفحة الحقيقية)
        function addProductToOrder(productId) {
            log(`➕ محاولة إضافة منتج ${productId}`, 'info');
            
            const product = products.find(p => p.id == productId);
            if (!product) {
                log(`❌ منتج غير موجود: ${productId}`, 'error');
                return;
            }
            
            if (orderItems[productId]) {
                orderItems[productId].quantity++;
                log(`📈 زيادة كمية ${product.name} إلى ${orderItems[productId].quantity}`, 'success');
            } else {
                orderItems[productId] = {
                    id: productId,
                    name: product.name,
                    price: product.price,
                    quantity: 1
                };
                log(`✅ تم إضافة ${product.name} للسلة`, 'success');
            }
            
            updateOrderDisplay();
        }
        
        // حذف منتج من السلة
        function removeItem(productId) {
            log(`🗑️ حذف منتج ${productId}`, 'warning');
            
            if (orderItems[productId]) {
                const productName = orderItems[productId].name;
                delete orderItems[productId];
                log(`✅ تم حذف ${productName} من السلة`, 'success');
                updateOrderDisplay();
            } else {
                log(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
            }
        }
        
        // تحديث الكمية (نفس الدالة المحسنة)
        function updateQuantity(productId, change) {
            log(`🔄 تحديث الكمية: منتج ${productId}, التغيير: ${change}`, 'info');

            if (!orderItems[productId]) {
                log(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
                alert('خطأ: المنتج غير موجود في السلة');
                return;
            }

            try {
                if (typeof change === 'string') {
                    // إذا كان التغيير من input field
                    const newQuantity = parseInt(change);
                    if (newQuantity > 0) {
                        orderItems[productId].quantity = newQuantity;
                        log(`✅ تم تحديث الكمية من input: ${newQuantity}`, 'success');
                    } else {
                        delete orderItems[productId];
                        log(`🗑️ تم حذف المنتج (كمية 0)`, 'warning');
                    }
                } else {
                    // إذا كان التغيير من الأزرار + أو -
                    const oldQuantity = orderItems[productId].quantity;
                    orderItems[productId].quantity += change;
                    
                    log(`📊 الكمية: ${oldQuantity} → ${orderItems[productId].quantity}`, 'info');

                    if (orderItems[productId].quantity <= 0) {
                        log(`🗑️ حذف المنتج من السلة (كمية <= 0)`, 'warning');
                        delete orderItems[productId];
                    }
                }

                log(`📋 حالة السلة بعد التحديث: ${JSON.stringify(orderItems)}`, 'info');
                updateOrderDisplay();
                
            } catch (error) {
                log(`❌ خطأ في تحديث الكمية: ${error.message}`, 'error');
                alert('حدث خطأ في تحديث الكمية: ' + error.message);
            }
        }
        
        // تحديث عرض السلة (نفس الدالة المحسنة)
        function updateOrderDisplay() {
            log('🔄 تحديث عرض السلة...', 'info');

            const container = document.getElementById('order-items');
            const totalElement = document.getElementById('total-amount');
            const submitButton = document.getElementById('submit-order');

            // التحقق من وجود العناصر
            if (!container) {
                log('❌ عنصر order-items غير موجود', 'error');
                alert('خطأ: عنصر السلة غير موجود في الصفحة');
                return;
            }
            
            if (!totalElement) {
                log('❌ عنصر total-amount غير موجود', 'error');
                return;
            }
            
            if (!submitButton) {
                log('❌ عنصر submit-order غير موجود', 'error');
                return;
            }

            log(`📋 عناصر السلة الحالية: ${JSON.stringify(orderItems)}`, 'info');
            
            if (Object.keys(orderItems).length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>لم يتم إضافة أي منتجات بعد</p>
                    </div>
                `;
                totalElement.textContent = '0 د.ع';
                submitButton.disabled = true;
                log('📭 السلة فارغة', 'info');
                return;
            }
            
            let html = '';
            let total = 0;
            
            for (const item of Object.values(orderItems)) {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                
                html += `
                    <div class="order-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="mb-1">${item.name}</h6>
                                <small class="text-muted">${Math.round(item.price)} د.ع</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(${item.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="quantity-controls">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span class="quantity-display">${item.quantity}</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <strong>${Math.round(itemTotal)} د.ع</strong>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            totalElement.textContent = Math.round(total) + ' د.ع';
            submitButton.disabled = false;

            log('✅ تم تحديث العرض بنجاح', 'success');
            log(`💰 الإجمالي: ${total} د.ع`, 'info');
            log(`📦 عدد العناصر: ${Object.keys(orderItems).length}`, 'info');
        }
        
        // تأكيد الطلب
        function submitOrder() {
            log('🛒 محاولة تأكيد الطلب...', 'info');
            
            if (Object.keys(orderItems).length === 0) {
                alert('يرجى إضافة منتجات للطلب');
                log('❌ محاولة تأكيد طلب فارغ', 'error');
                return;
            }

            const orderData = {
                table_id: 1,
                items: Object.values(orderItems)
            };

            log(`📋 بيانات الطلب: ${JSON.stringify(orderData)}`, 'info');
            
            // محاكاة نجاح الطلب
            const total = Object.values(orderItems).reduce((sum, item) => sum + (item.price * item.quantity), 0);
            alert(`تم تأكيد الطلب بنجاح!\nالعناصر: ${Object.keys(orderItems).length}\nالإجمالي: ${total} د.ع`);
            
            log('✅ تم تأكيد الطلب بنجاح', 'success');
            
            // مسح السلة
            orderItems = {};
            updateOrderDisplay();
        }
        
        // عرض محتويات السلة
        function showOrderItems() {
            if (Object.keys(orderItems).length === 0) {
                alert('السلة فارغة');
                return;
            }
            
            let message = 'محتويات السلة:\n\n';
            let total = 0;
            
            for (const item of Object.values(orderItems)) {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                message += `${item.name}: ${item.quantity} × ${item.price} = ${itemTotal} د.ع\n`;
            }
            
            message += `\nالإجمالي: ${total} د.ع`;
            alert(message);
        }
        
        // اختبار جميع الدوال
        function testAllFunctions() {
            log('🧪 بدء اختبار جميع الدوال...', 'info');
            
            // اختبار الدوال الأساسية
            const functions = ['addProductToOrder', 'updateQuantity', 'removeItem', 'updateOrderDisplay', 'submitOrder'];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ دالة ${funcName} موجودة`, 'success');
                } else {
                    log(`❌ دالة ${funcName} مفقودة`, 'error');
                }
            });
            
            // اختبار العناصر
            const elements = ['order-items', 'total-amount', 'submit-order'];
            
            elements.forEach(elementId => {
                if (document.getElementById(elementId)) {
                    log(`✅ عنصر ${elementId} موجود`, 'success');
                } else {
                    log(`❌ عنصر ${elementId} مفقود`, 'error');
                }
            });
            
            log('🏁 انتهى اختبار الدوال', 'info');
        }
        
        // تحديث حالة السلة في التشخيص
        function updateCartStatus() {
            const cartStatus = document.getElementById('cart-status');
            if (cartStatus) {
                const itemCount = Object.keys(orderItems).length;
                if (itemCount === 0) {
                    cartStatus.textContent = 'فارغة';
                    cartStatus.className = 'text-muted';
                } else {
                    cartStatus.textContent = `${itemCount} عنصر`;
                    cartStatus.className = 'text-success';
                }
            }
        }
        
        // تحديث معلومات التشخيص
        function updateDiagnosticInfo() {
            // حالة JavaScript
            const jsStatus = document.getElementById('js-status');
            if (jsStatus) {
                jsStatus.textContent = 'يعمل';
                jsStatus.className = 'text-success';
            }
            
            // حالة عناصر HTML
            const htmlStatus = document.getElementById('html-status');
            if (htmlStatus) {
                const elements = ['order-items', 'total-amount', 'submit-order'];
                const foundElements = elements.filter(id => document.getElementById(id));
                htmlStatus.textContent = `${foundElements.length}/${elements.length} موجود`;
                htmlStatus.className = foundElements.length === elements.length ? 'text-success' : 'text-warning';
            }
            
            // حالة الدوال
            const functionsStatus = document.getElementById('functions-status');
            if (functionsStatus) {
                const functions = ['addProductToOrder', 'updateQuantity', 'removeItem', 'updateOrderDisplay'];
                const foundFunctions = functions.filter(name => typeof window[name] === 'function');
                functionsStatus.textContent = `${foundFunctions.length}/${functions.length} موجود`;
                functionsStatus.className = foundFunctions.length === functions.length ? 'text-success' : 'text-warning';
            }
        }
        
        // إنشاء عرض المنتجات
        function createProductsDisplay() {
            const container = document.getElementById('products-container');
            
            products.forEach(product => {
                const productHtml = `
                    <div class="col-md-6 mb-3">
                        <div class="card product-card h-100" onclick="addProductToOrder(${product.id})">
                            <div class="card-body text-center">
                                <div class="bg-light d-flex align-items-center justify-content-center mb-3" style="height: 80px; border-radius: 5px;">
                                    <i class="fas fa-utensils fa-2x text-muted"></i>
                                </div>
                                <h6 class="card-title">${product.name}</h6>
                                <p class="card-text small text-muted">${product.description}</p>
                                <div class="price-tag">
                                    <strong class="text-success">${product.price} د.ع</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += productHtml;
            });
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء تحميل صفحة اختبار إضافة الطلب', 'info');
            
            // إنشاء عرض المنتجات
            createProductsDisplay();
            
            // تحديث معلومات التشخيص
            updateDiagnosticInfo();
            
            // تحديث عرض السلة
            updateOrderDisplay();
            
            log('✅ تم تحميل الصفحة بنجاح', 'success');
            log('💡 أضف منتجات واختبر أزرار + و -', 'info');
        });
    </script>
</body>
</html>
