<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار + و -</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">🧪 اختبار أزرار + و - للطلبات</h1>
        
        <!-- اختبار JavaScript فقط -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">1. اختبار JavaScript فقط (بدون AJAX)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="border p-3 rounded">
                            <h6>شاي عراقي</h6>
                            <div class="d-flex align-items-center gap-2 mt-2">
                                <button class="btn btn-sm btn-outline-danger" onclick="updateQuantityLocal(1, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span id="qty-1" class="badge bg-secondary px-3">1</span>
                                <button class="btn btn-sm btn-outline-success" onclick="updateQuantityLocal(1, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <span class="ms-3">السعر: <strong id="price-1">1000 د.ع</strong></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border p-3 rounded">
                            <h6>قهوة عربية</h6>
                            <div class="d-flex align-items-center gap-2 mt-2">
                                <button class="btn btn-sm btn-outline-danger" onclick="updateQuantityLocal(2, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span id="qty-2" class="badge bg-secondary px-3">1</span>
                                <button class="btn btn-sm btn-outline-success" onclick="updateQuantityLocal(2, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <span class="ms-3">السعر: <strong id="price-2">1500 د.ع</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <h5>الإجمالي: <span id="total-local" class="text-success">2500 د.ع</span></h5>
                </div>
            </div>
        </div>
        
        <!-- اختبار AJAX -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">2. اختبار AJAX</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="border p-3 rounded">
                            <h6>كباب عراقي</h6>
                            <div class="d-flex align-items-center gap-2 mt-2">
                                <button class="btn btn-sm btn-outline-danger" onclick="updateQuantityAjax(3, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span id="qty-3" class="badge bg-secondary px-3">1</span>
                                <button class="btn btn-sm btn-outline-success" onclick="updateQuantityAjax(3, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <span class="ms-3">السعر: <strong id="price-3">8000 د.ع</strong></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border p-3 rounded">
                            <h6>دولمة عراقية</h6>
                            <div class="d-flex align-items-center gap-2 mt-2">
                                <button class="btn btn-sm btn-outline-danger" onclick="updateQuantityAjax(4, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span id="qty-4" class="badge bg-secondary px-3">1</span>
                                <button class="btn btn-sm btn-outline-success" onclick="updateQuantityAjax(4, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <span class="ms-3">السعر: <strong id="price-4">6000 د.ع</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <h5>الإجمالي: <span id="total-ajax" class="text-success">14000 د.ع</span></h5>
                </div>
            </div>
        </div>
        
        <!-- سجل الأحداث -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">3. سجل الأحداث والأخطاء</h5>
            </div>
            <div class="card-body">
                <div id="log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px; white-space: pre-wrap;"></div>
                <button class="btn btn-secondary mt-2" onclick="clearLog()">مسح السجل</button>
            </div>
        </div>
        
        <!-- أزرار الاختبار -->
        <div class="text-center mt-4">
            <a href="add_order.php?table_id=1" class="btn btn-primary me-2">اختبار الصفحة الحقيقية</a>
            <a href="debug_buttons.php" class="btn btn-warning me-2">أداة التشخيص</a>
            <a href="dashboard.php" class="btn btn-secondary">العودة للوحة التحكم</a>
        </div>
    </div>

    <script>
        // بيانات المنتجات
        const products = {
            1: { name: 'شاي عراقي', price: 1000, quantity: 1 },
            2: { name: 'قهوة عربية', price: 1500, quantity: 1 },
            3: { name: 'كباب عراقي', price: 8000, quantity: 1 },
            4: { name: 'دولمة عراقية', price: 6000, quantity: 1 }
        };
        
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }
        
        // اختبار JavaScript فقط
        function updateQuantityLocal(productId, change) {
            log(`🔄 تحديث محلي للمنتج ${productId} بـ ${change}`, 'info');
            
            try {
                products[productId].quantity += change;
                
                if (products[productId].quantity < 1) {
                    products[productId].quantity = 1;
                    log(`⚠️ الكمية لا يمكن أن تكون أقل من 1`, 'warning');
                }
                
                // تحديث العرض
                document.getElementById(`qty-${productId}`).textContent = products[productId].quantity;
                document.getElementById(`price-${productId}`).textContent = 
                    (products[productId].quantity * products[productId].price) + ' د.ع';
                
                // تحديث الإجمالي
                const total = products[1].quantity * products[1].price + 
                             products[2].quantity * products[2].price;
                document.getElementById('total-local').textContent = total + ' د.ع';
                
                log(`✅ تم تحديث الكمية إلى ${products[productId].quantity}`, 'success');
                
            } catch (error) {
                log(`❌ خطأ في JavaScript: ${error.message}`, 'error');
            }
        }
        
        // اختبار AJAX
        function updateQuantityAjax(productId, change) {
            log(`📡 تحديث AJAX للمنتج ${productId} بـ ${change}`, 'info');
            
            try {
                const data = {
                    action: 'update_quantity',
                    product_id: productId,
                    change: change,
                    current_quantity: products[productId].quantity
                };
                
                log(`📤 إرسال البيانات: ${JSON.stringify(data)}`, 'info');
                
                fetch('ajax/simple_test.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    log(`📥 استجابة HTTP: ${response.status}`, response.ok ? 'success' : 'error');
                    return response.json();
                })
                .then(data => {
                    log(`📋 بيانات الاستجابة: ${JSON.stringify(data)}`, 'info');
                    
                    if (data.success) {
                        // تحديث البيانات المحلية
                        products[productId].quantity += change;
                        
                        if (products[productId].quantity < 1) {
                            products[productId].quantity = 1;
                        }
                        
                        // تحديث العرض
                        document.getElementById(`qty-${productId}`).textContent = products[productId].quantity;
                        document.getElementById(`price-${productId}`).textContent = 
                            (products[productId].quantity * products[productId].price) + ' د.ع';
                        
                        // تحديث الإجمالي
                        const total = products[3].quantity * products[3].price + 
                                     products[4].quantity * products[4].price;
                        document.getElementById('total-ajax').textContent = total + ' د.ع';
                        
                        log(`✅ AJAX نجح: ${data.message}`, 'success');
                    } else {
                        log(`❌ AJAX فشل: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    log(`❌ خطأ في AJAX: ${error.message}`, 'error');
                    console.error('AJAX Error:', error);
                });
                
            } catch (error) {
                log(`❌ خطأ في JavaScript: ${error.message}`, 'error');
            }
        }
        
        // تسجيل بداية الاختبار
        log('🚀 بدء اختبار أزرار + و -', 'info');
        log('💡 اختبر الأزرار أعلاه لتشخيص المشكلة', 'info');
    </script>
</body>
</html>
