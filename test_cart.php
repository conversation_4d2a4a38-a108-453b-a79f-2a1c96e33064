<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سلة الطلب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">🛒 اختبار سلة الطلب</h1>
        
        <div class="row">
            <!-- المنتجات -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>المنتجات المتاحة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card product-card" onclick="addToCart(1)">
                                    <div class="card-body text-center">
                                        <h6>شاي عراقي</h6>
                                        <p class="text-muted">شاي أصيل بالهيل</p>
                                        <strong class="text-success">1000 د.ع</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card product-card" onclick="addToCart(2)">
                                    <div class="card-body text-center">
                                        <h6>قهوة عربية</h6>
                                        <p class="text-muted">قهوة مميزة</p>
                                        <strong class="text-success">1500 د.ع</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card product-card" onclick="addToCart(3)">
                                    <div class="card-body text-center">
                                        <h6>كباب عراقي</h6>
                                        <p class="text-muted">كباب مشوي على الفحم</p>
                                        <strong class="text-success">8000 د.ع</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card product-card" onclick="addToCart(4)">
                                    <div class="card-body text-center">
                                        <h6>دولمة عراقية</h6>
                                        <p class="text-muted">دولمة تقليدية</p>
                                        <strong class="text-success">6000 د.ع</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- سلة الطلب -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            سلة الطلب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="cart-items">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                <p>لم يتم إضافة أي منتجات بعد</p>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <strong>الإجمالي:</strong>
                            <strong id="cart-total" class="text-success">0 د.ع</strong>
                        </div>
                        
                        <button type="button" class="btn btn-success w-100" id="checkout-btn" onclick="checkout()" disabled>
                            <i class="fas fa-check me-2"></i>
                            تأكيد الطلب
                        </button>
                    </div>
                </div>
                
                <!-- سجل الأحداث -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">سجل الأحداث</h6>
                    </div>
                    <div class="card-body">
                        <div id="log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
                        <button class="btn btn-sm btn-secondary mt-2" onclick="clearLog()">مسح</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="add_order.php?table_id=1" class="btn btn-primary me-2">الصفحة الحقيقية</a>
            <a href="debug_buttons.php" class="btn btn-warning me-2">أداة التشخيص</a>
            <a href="dashboard.php" class="btn btn-secondary">لوحة التحكم</a>
        </div>
    </div>

    <style>
        .product-card {
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .cart-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .quantity-display {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 5px 10px;
            min-width: 40px;
            text-align: center;
            font-weight: bold;
        }
    </style>

    <script>
        // بيانات المنتجات
        const products = {
            1: { id: 1, name: 'شاي عراقي', price: 1000 },
            2: { id: 2, name: 'قهوة عربية', price: 1500 },
            3: { id: 3, name: 'كباب عراقي', price: 8000 },
            4: { id: 4, name: 'دولمة عراقية', price: 6000 }
        };
        
        // سلة الطلب
        let cartItems = {};
        
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }
        
        // إضافة منتج للسلة
        function addToCart(productId) {
            log(`➕ إضافة منتج ${productId} للسلة`, 'info');
            
            try {
                const product = products[productId];
                if (!product) {
                    log(`❌ منتج غير موجود: ${productId}`, 'error');
                    return;
                }
                
                if (cartItems[productId]) {
                    cartItems[productId].quantity++;
                    log(`📈 زيادة كمية ${product.name} إلى ${cartItems[productId].quantity}`, 'success');
                } else {
                    cartItems[productId] = {
                        id: productId,
                        name: product.name,
                        price: product.price,
                        quantity: 1
                    };
                    log(`✅ تم إضافة ${product.name} للسلة`, 'success');
                }
                
                updateCartDisplay();
                
            } catch (error) {
                log(`❌ خطأ في إضافة المنتج: ${error.message}`, 'error');
            }
        }
        
        // تحديث الكمية
        function updateQuantity(productId, change) {
            log(`🔄 تحديث كمية المنتج ${productId} بـ ${change}`, 'info');
            
            try {
                if (!cartItems[productId]) {
                    log(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
                    return;
                }
                
                cartItems[productId].quantity += change;
                
                if (cartItems[productId].quantity <= 0) {
                    const productName = cartItems[productId].name;
                    delete cartItems[productId];
                    log(`🗑️ تم حذف ${productName} من السلة`, 'warning');
                } else {
                    log(`✅ تم تحديث الكمية إلى ${cartItems[productId].quantity}`, 'success');
                }
                
                updateCartDisplay();
                
            } catch (error) {
                log(`❌ خطأ في تحديث الكمية: ${error.message}`, 'error');
            }
        }
        
        // حذف منتج من السلة
        function removeFromCart(productId) {
            log(`🗑️ حذف منتج ${productId} من السلة`, 'warning');
            
            try {
                if (cartItems[productId]) {
                    const productName = cartItems[productId].name;
                    delete cartItems[productId];
                    log(`✅ تم حذف ${productName} من السلة`, 'success');
                    updateCartDisplay();
                } else {
                    log(`❌ المنتج غير موجود في السلة: ${productId}`, 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في حذف المنتج: ${error.message}`, 'error');
            }
        }
        
        // تحديث عرض السلة
        function updateCartDisplay() {
            log('🔄 تحديث عرض السلة...', 'info');
            
            try {
                const container = document.getElementById('cart-items');
                const totalElement = document.getElementById('cart-total');
                const checkoutBtn = document.getElementById('checkout-btn');
                
                if (Object.keys(cartItems).length === 0) {
                    container.innerHTML = `
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                            <p>لم يتم إضافة أي منتجات بعد</p>
                        </div>
                    `;
                    totalElement.textContent = '0 د.ع';
                    checkoutBtn.disabled = true;
                    log('📭 السلة فارغة', 'info');
                    return;
                }
                
                let html = '';
                let total = 0;
                
                for (const item of Object.values(cartItems)) {
                    const itemTotal = item.price * item.quantity;
                    total += itemTotal;
                    
                    html += `
                        <div class="cart-item">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="mb-1">${item.name}</h6>
                                    <small class="text-muted">${item.price} د.ع</small>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.id})">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="quantity-controls">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, -1)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <span class="quantity-display">${item.quantity}</span>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, 1)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <strong>${itemTotal} د.ع</strong>
                            </div>
                        </div>
                    `;
                }
                
                container.innerHTML = html;
                totalElement.textContent = total + ' د.ع';
                checkoutBtn.disabled = false;
                
                log(`✅ تم تحديث العرض. العناصر: ${Object.keys(cartItems).length}, الإجمالي: ${total}`, 'success');
                
            } catch (error) {
                log(`❌ خطأ في تحديث العرض: ${error.message}`, 'error');
            }
        }
        
        // تأكيد الطلب
        function checkout() {
            log('🛒 تأكيد الطلب...', 'info');
            
            if (Object.keys(cartItems).length === 0) {
                alert('السلة فارغة!');
                log('❌ محاولة تأكيد طلب فارغ', 'error');
                return;
            }
            
            const total = Object.values(cartItems).reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
            log(`✅ تم تأكيد الطلب. العناصر: ${Object.keys(cartItems).length}, الإجمالي: ${total}`, 'success');
            alert(`تم تأكيد الطلب!\nالعناصر: ${Object.keys(cartItems).length}\nالإجمالي: ${total} د.ع`);
            
            // مسح السلة
            cartItems = {};
            updateCartDisplay();
            log('🧹 تم مسح السلة بعد التأكيد', 'info');
        }
        
        // بداية الاختبار
        log('🚀 بدء اختبار سلة الطلب', 'info');
        log('💡 أضف منتجات واختبر أزرار + و -', 'info');
    </script>
</body>
</html>
