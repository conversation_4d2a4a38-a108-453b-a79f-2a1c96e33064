<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إكمال الطلبات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="alert alert-success">
            <h4><i class="fas fa-check-circle me-2"></i>تم إصلاح مشكلة إكمال الطلبات!</h4>
            <p><strong>المشاكل التي تم إصلاحها:</strong></p>
            <ul class="mb-0">
                <li>إضافة `status_text` في استجابة AJAX</li>
                <li>تحديث حالة الطاولة تلقائ<|im_start|></li>
                <li>تحديث حالة عناصر الطلب</li>
                <li>إضافة `updated_at` timestamp</li>
                <li>تحسين رسائل الاستجابة</li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            طلبات تجريبية للاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- طلب معلق -->
                        <div class="card border-warning mb-3">
                            <div class="card-header bg-warning text-dark">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">طلب #1001 - طاولة 1</h6>
                                        <small>شاي عراقي × 2، قهوة عربية × 1</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-warning" id="status-1001">معلق</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex gap-2">
                                    <button class="btn btn-success btn-sm" onclick="testStartProcessing(1001)" id="start-btn-1001">
                                        <i class="fas fa-play me-1"></i>بدء التحضير
                                    </button>
                                    <button class="btn btn-info btn-sm d-none" onclick="testCompleteOrder(1001)" id="complete-btn-1001">
                                        <i class="fas fa-check me-1"></i>إكمال الطلب
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="testCancelOrder(1001)">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- طلب قيد التحضير -->
                        <div class="card border-info mb-3">
                            <div class="card-header bg-info text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">طلب #1002 - طاولة 3</h6>
                                        <small>كباب عراقي × 1، دولمة × 2</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-info" id="status-1002">قيد التحضير</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex gap-2">
                                    <button class="btn btn-info btn-sm" onclick="testCompleteOrder(1002)" id="complete-btn-1002">
                                        <i class="fas fa-check me-1"></i>إكمال الطلب
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="testCancelOrder(1002)">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- طلب مكتمل -->
                        <div class="card border-success mb-3">
                            <div class="card-header bg-success text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">طلب #1003 - طاولة 5</h6>
                                        <small>حمص بالطحينة × 1، شاي × 3</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-success" id="status-1003">مكتمل</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success mb-0">
                                    <i class="fas fa-check-circle me-2"></i>
                                    تم إكمال هذا الطلب بنجاح
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- اختبار مباشر -->
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-flask me-2"></i>
                            اختبار مباشر لدوال إكمال الطلب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>اختبار الدوال:</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="testUpdateOrderStatus(1001, 'processing')">
                                        اختبار updateOrderStatus(1001, 'processing')
                                    </button>
                                    <button class="btn btn-success" onclick="testUpdateOrderStatus(1001, 'completed')">
                                        اختبار updateOrderStatus(1001, 'completed')
                                    </button>
                                    <button class="btn btn-danger" onclick="testUpdateOrderStatus(1001, 'cancelled')">
                                        اختبار updateOrderStatus(1001, 'cancelled')
                                    </button>
                                    <button class="btn btn-secondary" onclick="testAjaxConnection()">
                                        اختبار اتصال AJAX
                                    </button>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>اختبار Console:</h6>
                                <div class="bg-light p-3 rounded">
                                    <small>افتح Developer Tools (F12) وراقب الرسائل:</small>
                                    <pre class="small mt-2"><code>// في Console
testCompleteOrder(1001);

// يجب أن تظهر:
✅ إكمال الطلب: 1001
🔄 تحديث حالة الطلب: 1001 إلى: completed
📥 استجابة HTTP: 200
📋 بيانات الاستجابة: {success: true, ...}
✅ تم تحديث حالة الطلب بنجاح: مكتمل</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            سجل الأحداث
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="test-log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
                        <button class="btn btn-sm btn-secondary mt-2 w-100" onclick="clearLog()">مسح السجل</button>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">ما تم إصلاحه</h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <small>إضافة status_text في AJAX</small>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <small>تحديث حالة الطاولة</small>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <small>تحديث عناصر الطلب</small>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <small>إضافة updated_at</small>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <small>تحسين رسائل الخطأ</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header bg-dark text-white">
                        <h6 class="mb-0">حالة النظام</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>AJAX:</span>
                            <span class="badge bg-success" id="ajax-status">جاهز</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>JavaScript:</span>
                            <span class="badge bg-success" id="js-status">يعمل</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الدوال:</span>
                            <span class="badge bg-success" id="functions-status">موجودة</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>الاتصال:</span>
                            <span class="badge bg-success" id="connection-status">متصل</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط الاختبار -->
        <div class="text-center mt-4">
            <h5>اختبار النظام الحقيقي:</h5>
            <div class="btn-group" role="group">
                <a href="orders.php" class="btn btn-primary" target="_blank">
                    <i class="fas fa-list me-2"></i>صفحة الطلبات
                </a>
                <a href="add_order.php?table_id=1" class="btn btn-success" target="_blank">
                    <i class="fas fa-plus me-2"></i>إضافة طلب
                </a>
                <a href="dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </div>
        </div>
        
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-lightbulb me-2"></i>خطوات الاختبار:</h6>
            <ol class="mb-0">
                <li><strong>اختبر الدوال أعلاه</strong> - يجب أن تعمل بدون أخطاء</li>
                <li><strong>افتح صفحة الطلبات الحقيقية</strong> - orders.php</li>
                <li><strong>ابحث عن طلب قيد التحضير</strong> - يجب أن يظهر زر "إكمال"</li>
                <li><strong>اضغط زر إكمال الطلب</strong> - يجب أن يعمل بنجاح</li>
                <li><strong>تحقق من تحديث الحالة</strong> - يجب أن تصبح "مكتمل"</li>
            </ol>
        </div>
    </div>

    <script>
        // دوال اختبار إكمال الطلبات
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${icon} ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }
        
        // نسخ دوال من orders.php للاختبار
        function testStartProcessing(orderId) {
            log(`🚀 بدء تحضير الطلب ${orderId}`, 'info');
            
            if (confirm('هل تريد بدء تحضير هذا الطلب؟')) {
                testUpdateOrderStatus(orderId, 'processing');
            }
        }
        
        function testCompleteOrder(orderId) {
            log(`✅ إكمال الطلب ${orderId}`, 'info');
            
            if (confirm('هل تم الانتهاء من تحضير هذا الطلب؟')) {
                testUpdateOrderStatus(orderId, 'completed');
            }
        }
        
        function testCancelOrder(orderId) {
            log(`❌ إلغاء الطلب ${orderId}`, 'warning');
            
            if (confirm('هل تريد إلغاء هذا الطلب؟')) {
                testUpdateOrderStatus(orderId, 'cancelled');
            }
        }
        
        function testUpdateOrderStatus(orderId, newStatus) {
            log(`🔄 تحديث حالة الطلب: ${orderId} إلى: ${newStatus}`, 'info');
            
            const data = {
                order_id: orderId,
                status: newStatus
            };
            
            fetch('ajax/update_order_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                log(`📥 استجابة HTTP: ${response.status}`, response.ok ? 'success' : 'error');
                return response.json();
            })
            .then(data => {
                log(`📋 بيانات الاستجابة: ${JSON.stringify(data)}`, 'info');
                
                if (data.success) {
                    log(`✅ تم تحديث حالة الطلب بنجاح: ${data.status_text}`, 'success');
                    
                    // تحديث واجهة الاختبار
                    updateTestUI(orderId, newStatus, data.status_text);
                    
                } else {
                    log(`❌ حدث خطأ: ${data.message}`, 'error');
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                log(`❌ خطأ في AJAX: ${error.message}`, 'error');
                alert('حدث خطأ في الاتصال: ' + error.message);
            });
        }
        
        function updateTestUI(orderId, newStatus, statusText) {
            const statusElement = document.getElementById(`status-${orderId}`);
            const startBtn = document.getElementById(`start-btn-${orderId}`);
            const completeBtn = document.getElementById(`complete-btn-${orderId}`);
            
            if (statusElement) {
                statusElement.textContent = statusText;
                
                // تحديث لون الشارة
                statusElement.className = 'badge ';
                switch (newStatus) {
                    case 'pending':
                        statusElement.className += 'bg-warning';
                        break;
                    case 'processing':
                        statusElement.className += 'bg-info';
                        break;
                    case 'completed':
                        statusElement.className += 'bg-success';
                        break;
                    case 'cancelled':
                        statusElement.className += 'bg-danger';
                        break;
                }
            }
            
            // تحديث الأزرار
            if (startBtn && completeBtn) {
                if (newStatus === 'pending') {
                    startBtn.classList.remove('d-none');
                    completeBtn.classList.add('d-none');
                } else if (newStatus === 'processing') {
                    startBtn.classList.add('d-none');
                    completeBtn.classList.remove('d-none');
                } else {
                    startBtn.classList.add('d-none');
                    completeBtn.classList.add('d-none');
                }
            }
            
            log(`🎨 تم تحديث واجهة الطلب ${orderId}`, 'success');
        }
        
        function testAjaxConnection() {
            log('🧪 اختبار اتصال AJAX...', 'info');
            
            fetch('ajax/update_order_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({test: true})
            })
            .then(response => {
                log(`📡 حالة الاتصال: ${response.status}`, response.ok ? 'success' : 'error');
                return response.text();
            })
            .then(data => {
                log(`📄 استجابة الخادم: ${data.substring(0, 100)}...`, 'info');
                
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success !== undefined) {
                        log('✅ ملف AJAX يعمل بشكل صحيح', 'success');
                    } else {
                        log('⚠️ ملف AJAX يستجيب لكن بصيغة غير متوقعة', 'warning');
                    }
                } catch (e) {
                    log('❌ ملف AJAX لا يرجع JSON صحيح', 'error');
                }
            })
            .catch(error => {
                log(`❌ خطأ في الاتصال: ${error.message}`, 'error');
            });
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار إكمال الطلبات', 'info');
            log('✅ تم إصلاح مشكلة إكمال الطلبات', 'success');
            log('💡 اختبر الدوال أعلاه للتأكد من عملها', 'info');
            
            // فحص الدوال
            const functions = ['testCompleteOrder', 'testUpdateOrderStatus', 'testStartProcessing'];
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ دالة ${funcName} موجودة`, 'success');
                } else {
                    log(`❌ دالة ${funcName} مفقودة`, 'error');
                }
            });
            
            log('🎉 تم تحميل صفحة الاختبار بنجاح', 'success');
        });
    </script>
</body>
</html>
