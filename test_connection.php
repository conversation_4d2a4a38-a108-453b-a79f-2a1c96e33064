<?php
// ملف اختبار الاتصال بقاعدة البيانات

echo "<h2>اختبار نظام إدارة المطاعم</h2>";
echo "<hr>";

// اختبار PHP
echo "<h3>1. اختبار PHP</h3>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "الذاكرة المتاحة: " . ini_get('memory_limit') . "<br>";
echo "حد رفع الملفات: " . ini_get('upload_max_filesize') . "<br>";
echo "✅ PHP يعمل بشكل صحيح<br><br>";

// اختبار الاتصال بقاعدة البيانات
echo "<h3>2. اختبار قاعدة البيانات</h3>";

try {
    require_once 'config/database.php';
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
    
    // اختبار الجداول
    $tables = ['users', 'tables', 'categories', 'products', 'orders', 'order_items'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "جدول $table: $count سجل<br>";
    }
    
    echo "<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br><br>";
}

// اختبار المستخدمين
echo "<h3>3. اختبار المستخدمين</h3>";
try {
    $stmt = $pdo->query("SELECT username, full_name, role FROM users");
    $users = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الدور</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['username'] . "</td>";
        echo "<td>" . $user['full_name'] . "</td>";
        echo "<td>" . $user['role'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table><br>";
    echo "✅ بيانات المستخدمين متوفرة<br><br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب بيانات المستخدمين: " . $e->getMessage() . "<br><br>";
}

// اختبار الطاولات
echo "<h3>4. اختبار الطاولات</h3>";
try {
    $stmt = $pdo->query("SELECT table_number, capacity, status FROM tables ORDER BY CAST(table_number AS UNSIGNED)");
    $tables = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>رقم الطاولة</th><th>السعة</th><th>الحالة</th></tr>";
    
    foreach ($tables as $table) {
        echo "<tr>";
        echo "<td>" . $table['table_number'] . "</td>";
        echo "<td>" . $table['capacity'] . "</td>";
        echo "<td>" . ($table['status'] == 'available' ? 'متاحة' : 'مشغولة') . "</td>";
        echo "</tr>";
    }
    
    echo "</table><br>";
    echo "✅ بيانات الطاولات متوفرة<br><br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب بيانات الطاولات: " . $e->getMessage() . "<br><br>";
}

// اختبار المنتجات
echo "<h3>5. اختبار المنتجات</h3>";
try {
    $stmt = $pdo->query("
        SELECT p.name, p.price, c.name as category_name 
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        ORDER BY c.name, p.name
    ");
    $products = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>المنتج</th><th>الفئة</th><th>السعر</th></tr>";
    
    foreach ($products as $product) {
        echo "<tr>";
        echo "<td>" . $product['name'] . "</td>";
        echo "<td>" . $product['category_name'] . "</td>";
        echo "<td>" . number_format($product['price'], 2) . " ريال</td>";
        echo "</tr>";
    }
    
    echo "</table><br>";
    echo "✅ بيانات المنتجات متوفرة<br><br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب بيانات المنتجات: " . $e->getMessage() . "<br><br>";
}

// اختبار الجلسات
echo "<h3>6. اختبار الجلسات</h3>";
if (session_status() == PHP_SESSION_ACTIVE) {
    echo "✅ الجلسات تعمل بشكل صحيح<br>";
    echo "معرف الجلسة: " . session_id() . "<br><br>";
} else {
    echo "❌ مشكلة في الجلسات<br><br>";
}

// اختبار الملفات المطلوبة
echo "<h3>7. اختبار الملفات</h3>";
$required_files = [
    'config/database.php',
    'includes/header.php',
    'includes/footer.php',
    'login.php',
    'dashboard.php',
    'tables.php',
    'cashier.php',
    'kitchen.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file مفقود<br>";
    }
}

echo "<br>";

// ملخص النتائج
echo "<h3>8. ملخص الاختبار</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
echo "<strong>✅ النظام جاهز للاستخدام!</strong><br><br>";
echo "يمكنك الآن:<br>";
echo "• الانتقال إلى <a href='login.php'>صفحة تسجيل الدخول</a><br>";
echo "• استخدام البيانات التجريبية للدخول<br>";
echo "• بدء استخدام النظام<br>";
echo "</div>";

echo "<br><hr>";
echo "<p><strong>تاريخ الاختبار:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #333;
}

table {
    border-collapse: collapse;
    width: 100%;
    max-width: 600px;
    background: white;
}

th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}
</style>
