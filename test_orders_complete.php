<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لإدارة الطلبات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <h4><i class="fas fa-clipboard-check me-2"></i>اختبار شامل لإدارة الطلبات</h4>
                    <p>اختبار جميع وظائف الطلبات: بدء التحضير، تعديل الطلبات، أزرار + و -</p>
                </div>
            </div>
        </div>
        
        <!-- اختبار بدء التحضير -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-play me-2"></i>
                    اختبار بدء التحضير
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>طلبات تجريبية:</h6>
                        <div id="test-orders">
                            <div class="border p-3 rounded mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6>طلب #1001 - طاولة 1</h6>
                                        <small class="text-muted">شاي عراقي × 2، قهوة عربية × 1</small>
                                        <div>الحالة: <span id="status-1001" class="badge bg-warning">معلق</span></div>
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-success" onclick="testStartProcessing(1001)" id="start-btn-1001">
                                            <i class="fas fa-play me-1"></i>بدء التحضير
                                        </button>
                                        <button class="btn btn-sm btn-info d-none" onclick="testCompleteOrder(1001)" id="complete-btn-1001">
                                            <i class="fas fa-check me-1"></i>إكمال
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="border p-3 rounded mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6>طلب #1002 - طاولة 3</h6>
                                        <small class="text-muted">كباب عراقي × 1، دولمة × 2</small>
                                        <div>الحالة: <span id="status-1002" class="badge bg-warning">معلق</span></div>
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-success" onclick="testStartProcessing(1002)" id="start-btn-1002">
                                            <i class="fas fa-play me-1"></i>بدء التحضير
                                        </button>
                                        <button class="btn btn-sm btn-info d-none" onclick="testCompleteOrder(1002)" id="complete-btn-1002">
                                            <i class="fas fa-check me-1"></i>إكمال
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>سجل بدء التحضير</h6>
                            </div>
                            <div class="card-body">
                                <div id="processing-log" style="height: 150px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
                                <button class="btn btn-sm btn-secondary mt-2 w-100" onclick="clearProcessingLog()">مسح</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار تعديل الطلبات -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    اختبار تعديل الطلبات وأزرار + و -
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>طلب قيد التعديل:</h6>
                        <div id="edit-order-area">
                            <div class="border p-3 rounded">
                                <h6>طلب #1003 - طاولة 5</h6>
                                <div id="order-items-edit">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>الإجمالي: <span id="edit-total">0 د.ع</span></strong>
                                    <button class="btn btn-primary" onclick="testSaveOrder()">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>سجل التعديل</h6>
                            </div>
                            <div class="card-body">
                                <div id="edit-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
                                <button class="btn btn-sm btn-secondary mt-2 w-100" onclick="clearEditLog()">مسح</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار AJAX -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-network-wired me-2"></i>
                    اختبار AJAX
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>اختبار ملفات AJAX:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="testAjaxFile('update_order_status.php')">
                                اختبار update_order_status.php
                            </button>
                            <button class="btn btn-outline-primary" onclick="testAjaxFile('update_order.php')">
                                اختبار update_order.php
                            </button>
                            <button class="btn btn-outline-primary" onclick="testAjaxFile('get_order_details.php')">
                                اختبار get_order_details.php
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>نتائج AJAX</h6>
                            </div>
                            <div class="card-body">
                                <div id="ajax-results" style="height: 150px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط الاختبار -->
        <div class="text-center">
            <h5>اختبار الصفحات الحقيقية:</h5>
            <div class="btn-group" role="group">
                <a href="orders.php" class="btn btn-primary">صفحة الطلبات</a>
                <a href="edit_order.php?id=1" class="btn btn-warning">تعديل طلب</a>
                <a href="add_order.php?table_id=1" class="btn btn-success">إضافة طلب</a>
                <a href="dashboard.php" class="btn btn-secondary">لوحة التحكم</a>
            </div>
        </div>
    </div>

    <style>
        .order-item-edit {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quantity-display {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            min-width: 50px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }
    </style>

    <script>
        // بيانات تجريبية للطلبات
        let testOrderItems = {
            1: { id: 1, name: 'شاي عراقي', price: 1000, quantity: 2 },
            2: { id: 2, name: 'قهوة عربية', price: 1500, quantity: 1 },
            3: { id: 3, name: 'كباب عراقي', price: 8000, quantity: 1 }
        };
        
        // دوال تسجيل الأحداث
        function logProcessing(message, type = 'info') {
            const logDiv = document.getElementById('processing-log');
            logMessage(logDiv, message, type);
        }
        
        function logEdit(message, type = 'info') {
            const logDiv = document.getElementById('edit-log');
            logMessage(logDiv, message, type);
        }
        
        function logAjax(message, type = 'info') {
            const logDiv = document.getElementById('ajax-results');
            logMessage(logDiv, message, type);
        }
        
        function logMessage(logDiv, message, type) {
            if (!logDiv) return;
            
            const time = new Date().toLocaleTimeString();
            const colors = {
                'info': '#007bff',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const color = colors[type] || '#6c757d';
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logDiv.innerHTML += `<span style="color: ${color};">[${time}] ${icon} ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearProcessingLog() {
            document.getElementById('processing-log').innerHTML = '';
            logProcessing('تم مسح السجل', 'info');
        }
        
        function clearEditLog() {
            document.getElementById('edit-log').innerHTML = '';
            logEdit('تم مسح السجل', 'info');
        }
        
        // اختبار بدء التحضير
        function testStartProcessing(orderId) {
            logProcessing(`🚀 بدء تحضير الطلب ${orderId}`, 'info');
            
            // محاكاة طلب AJAX
            const data = {
                order_id: orderId,
                status: 'processing'
            };
            
            logProcessing(`📤 إرسال البيانات: ${JSON.stringify(data)}`, 'info');
            
            // محاكاة استجابة ناجحة
            setTimeout(() => {
                const statusElement = document.getElementById(`status-${orderId}`);
                const startBtn = document.getElementById(`start-btn-${orderId}`);
                const completeBtn = document.getElementById(`complete-btn-${orderId}`);
                
                if (statusElement && startBtn && completeBtn) {
                    statusElement.textContent = 'قيد التحضير';
                    statusElement.className = 'badge bg-info';
                    
                    startBtn.classList.add('d-none');
                    completeBtn.classList.remove('d-none');
                    
                    logProcessing(`✅ تم بدء تحضير الطلب ${orderId}`, 'success');
                } else {
                    logProcessing(`❌ عناصر HTML مفقودة للطلب ${orderId}`, 'error');
                }
            }, 1000);
        }
        
        // اختبار إكمال الطلب
        function testCompleteOrder(orderId) {
            logProcessing(`✅ إكمال الطلب ${orderId}`, 'info');
            
            setTimeout(() => {
                const statusElement = document.getElementById(`status-${orderId}`);
                const completeBtn = document.getElementById(`complete-btn-${orderId}`);
                
                if (statusElement && completeBtn) {
                    statusElement.textContent = 'مكتمل';
                    statusElement.className = 'badge bg-success';
                    
                    completeBtn.classList.add('d-none');
                    
                    logProcessing(`✅ تم إكمال الطلب ${orderId}`, 'success');
                } else {
                    logProcessing(`❌ عناصر HTML مفقودة للطلب ${orderId}`, 'error');
                }
            }, 1000);
        }
        
        // اختبار تحديث الكمية
        function testUpdateQuantity(itemId, change) {
            logEdit(`🔄 تحديث كمية المنتج ${itemId} بـ ${change}`, 'info');
            
            if (!testOrderItems[itemId]) {
                logEdit(`❌ المنتج غير موجود: ${itemId}`, 'error');
                return;
            }
            
            try {
                const oldQuantity = testOrderItems[itemId].quantity;
                testOrderItems[itemId].quantity += change;
                
                logEdit(`📊 الكمية: ${oldQuantity} → ${testOrderItems[itemId].quantity}`, 'info');
                
                if (testOrderItems[itemId].quantity <= 0) {
                    delete testOrderItems[itemId];
                    logEdit(`🗑️ تم حذف المنتج ${itemId}`, 'warning');
                } else {
                    logEdit(`✅ تم تحديث الكمية بنجاح`, 'success');
                }
                
                renderEditOrderItems();
                updateEditTotal();
                
            } catch (error) {
                logEdit(`❌ خطأ في تحديث الكمية: ${error.message}`, 'error');
            }
        }
        
        // عرض عناصر الطلب قيد التعديل
        function renderEditOrderItems() {
            const container = document.getElementById('order-items-edit');
            if (!container) return;
            
            if (Object.keys(testOrderItems).length === 0) {
                container.innerHTML = '<div class="text-center text-muted py-3">لا توجد عناصر في الطلب</div>';
                return;
            }
            
            let html = '';
            
            for (const item of Object.values(testOrderItems)) {
                const itemTotal = item.price * item.quantity;
                
                html += `
                    <div class="order-item-edit">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="mb-1">${item.name}</h6>
                                <small class="text-muted">${item.price} د.ع</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="testRemoveItem(${item.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="quantity-controls">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testUpdateQuantity(${item.id}, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span class="quantity-display">${item.quantity}</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testUpdateQuantity(${item.id}, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <strong>${itemTotal} د.ع</strong>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }
        
        // تحديث الإجمالي
        function updateEditTotal() {
            const totalElement = document.getElementById('edit-total');
            if (!totalElement) return;
            
            const total = Object.values(testOrderItems).reduce((sum, item) => {
                return sum + (item.price * item.quantity);
            }, 0);
            
            totalElement.textContent = total + ' د.ع';
        }
        
        // حذف عنصر
        function testRemoveItem(itemId) {
            logEdit(`🗑️ حذف المنتج ${itemId}`, 'warning');
            
            if (testOrderItems[itemId]) {
                const itemName = testOrderItems[itemId].name;
                delete testOrderItems[itemId];
                logEdit(`✅ تم حذف ${itemName}`, 'success');
                
                renderEditOrderItems();
                updateEditTotal();
            } else {
                logEdit(`❌ المنتج غير موجود: ${itemId}`, 'error');
            }
        }
        
        // اختبار حفظ الطلب
        function testSaveOrder() {
            logEdit('💾 حفظ تغييرات الطلب...', 'info');
            
            const orderData = {
                order_id: 1003,
                items: Object.values(testOrderItems)
            };
            
            logEdit(`📋 بيانات الطلب: ${JSON.stringify(orderData)}`, 'info');
            
            if (orderData.items.length === 0) {
                logEdit('⚠️ لا يمكن حفظ طلب فارغ', 'warning');
                alert('لا يمكن حفظ طلب فارغ');
                return;
            }
            
            // محاكاة حفظ ناجح
            setTimeout(() => {
                logEdit('✅ تم حفظ الطلب بنجاح', 'success');
                alert('تم حفظ التغييرات بنجاح');
            }, 1000);
        }
        
        // اختبار ملفات AJAX
        function testAjaxFile(filename) {
            logAjax(`🧪 اختبار ملف: ${filename}`, 'info');
            
            const testData = {
                test: true,
                timestamp: new Date().toISOString()
            };
            
            fetch(`ajax/${filename}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                logAjax(`📥 استجابة HTTP: ${response.status}`, response.ok ? 'success' : 'error');
                return response.text();
            })
            .then(data => {
                logAjax(`📋 الاستجابة: ${data.substring(0, 100)}...`, 'info');
                
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success !== undefined) {
                        logAjax(`✅ ملف ${filename} يعمل بشكل صحيح`, 'success');
                    } else {
                        logAjax(`⚠️ ملف ${filename} يستجيب لكن بصيغة غير متوقعة`, 'warning');
                    }
                } catch (e) {
                    logAjax(`❌ ملف ${filename} لا يرجع JSON صحيح`, 'error');
                }
            })
            .catch(error => {
                logAjax(`❌ خطأ في ${filename}: ${error.message}`, 'error');
            });
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            logProcessing('🚀 بدء اختبار إدارة الطلبات', 'info');
            logEdit('🚀 بدء اختبار تعديل الطلبات', 'info');
            logAjax('🚀 بدء اختبار AJAX', 'info');
            
            // عرض عناصر الطلب قيد التعديل
            renderEditOrderItems();
            updateEditTotal();
            
            logProcessing('✅ تم تحميل الصفحة بنجاح', 'success');
            logEdit('💡 اختبر أزرار + و - أعلاه', 'info');
        });
    </script>
</body>
</html>
