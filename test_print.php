<?php
require_once 'config/database.php';
require_once 'includes/printer_functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$page_title = 'اختبار الطباعة';
$print_type = $_GET['type'] ?? 'kitchen';

// بيانات تجريبية للاختبار
$test_order_data = [
    'order_id' => 1001,
    'table_number' => 'طاولة 5',
    'user_name' => 'أحمد محمد',
    'created_at' => date('Y-m-d H:i:s'),
    'total_amount' => 25000,
    'items' => [
        [
            'id' => 1,
            'name' => 'كباب عراقي',
            'quantity' => 2,
            'price' => 8000,
            'notes' => 'بدون بصل'
        ],
        [
            'id' => 2,
            'name' => 'شاي عراقي',
            'quantity' => 3,
            'price' => 1000,
            'notes' => ''
        ],
        [
            'id' => 3,
            'name' => 'دولمة عراقية',
            'quantity' => 1,
            'price' => 6000,
            'notes' => 'حار'
        ]
    ]
];

$print_result = false;
$print_message = '';

// تنفيذ الطباعة
if ($_SERVER['REQUEST_METHOD'] == 'POST' || isset($_GET['auto_print'])) {
    try {
        switch ($print_type) {
            case 'kitchen':
                $printer = getKitchenPrinter();
                $print_result = $printer->printKitchenOrder($test_order_data);
                $print_message = $print_result ? 'تم طباعة طلب المطبخ بنجاح!' : 'فشل في طباعة طلب المطبخ';
                break;
                
            case 'cashier':
                $printer = getCashierPrinter();
                $print_result = $printer->printCashierReceipt($test_order_data);
                $print_message = $print_result ? 'تم طباعة فاتورة الكاشير بنجاح!' : 'فشل في طباعة فاتورة الكاشير';
                break;
                
            case 'both':
                $kitchen_result = printToKitchen($test_order_data);
                $cashier_result = printToCashier($test_order_data);
                $print_result = $kitchen_result && $cashier_result;
                
                if ($print_result) {
                    $print_message = 'تم طباعة الطلب على الطابعتين بنجاح!';
                } else {
                    $print_message = 'فشل في الطباعة: ';
                    if (!$kitchen_result) $print_message .= 'طابعة المطبخ ';
                    if (!$cashier_result) $print_message .= 'طابعة الكاشير ';
                }
                break;
                
            case 'test':
                $kitchen_printer = getKitchenPrinter();
                $cashier_printer = getCashierPrinter();
                
                $kitchen_test = $kitchen_printer->testPrinter();
                $cashier_test = $cashier_printer->testPrinter();
                
                $print_result = $kitchen_test && $cashier_test;
                $print_message = $print_result ? 'جميع الطابعات تعمل بنجاح!' : 'بعض الطابعات لا تعمل';
                break;
        }
        
        if ($print_result) {
            showMessage($print_message, 'success');
        } else {
            showMessage($print_message, 'error');
        }
        
    } catch (Exception $e) {
        showMessage('حدث خطأ في الطباعة: ' . $e->getMessage(), 'error');
    }
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-print me-2"></i>اختبار الطباعة</h2>
    <div>
        <a href="printer_settings.php" class="btn btn-secondary me-2">
            <i class="fas fa-cog me-2"></i>إعدادات الطابعة
        </a>
        <a href="dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>لوحة التحكم
        </a>
    </div>
</div>

<div class="row">
    <!-- خيارات الطباعة -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    خيارات الطباعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="test_print.php?type=kitchen&auto_print=1" 
                       class="btn btn-success <?php echo $print_type == 'kitchen' ? 'active' : ''; ?>">
                        <i class="fas fa-utensils me-2"></i>
                        طباعة طلب المطبخ
                    </a>
                    
                    <a href="test_print.php?type=cashier&auto_print=1" 
                       class="btn btn-info <?php echo $print_type == 'cashier' ? 'active' : ''; ?>">
                        <i class="fas fa-receipt me-2"></i>
                        طباعة فاتورة الكاشير
                    </a>
                    
                    <a href="test_print.php?type=both&auto_print=1" 
                       class="btn btn-warning <?php echo $print_type == 'both' ? 'active' : ''; ?>">
                        <i class="fas fa-print me-2"></i>
                        طباعة على الطابعتين
                    </a>
                    
                    <a href="test_print.php?type=test&auto_print=1" 
                       class="btn btn-secondary <?php echo $print_type == 'test' ? 'active' : ''; ?>">
                        <i class="fas fa-vial me-2"></i>
                        اختبار الطابعات
                    </a>
                </div>
                
                <hr>
                
                <form method="POST">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-redo me-2"></i>
                        إعادة الطباعة
                    </button>
                </form>
            </div>
        </div>
        
        <!-- معلومات الطباعة -->
        <div class="card mt-3">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الطباعة
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong>نوع الطباعة:</strong>
                        <span class="badge bg-primary">
                            <?php
                            $types = [
                                'kitchen' => 'طلب المطبخ',
                                'cashier' => 'فاتورة الكاشير',
                                'both' => 'الطابعتين معاً',
                                'test' => 'اختبار الطابعات'
                            ];
                            echo $types[$print_type] ?? 'غير محدد';
                            ?>
                        </span>
                    </div>
                    
                    <div class="mb-2">
                        <strong>حالة الطباعة:</strong>
                        <?php if (isset($print_result)): ?>
                            <span class="badge bg-<?php echo $print_result ? 'success' : 'danger'; ?>">
                                <?php echo $print_result ? 'نجحت' : 'فشلت'; ?>
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary">لم تتم بعد</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-2">
                        <strong>الوقت:</strong>
                        <?php echo date('H:i:s'); ?>
                    </div>
                    
                    <div>
                        <strong>التاريخ:</strong>
                        <?php echo date('Y-m-d'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معاينة الطباعة -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    معاينة الطباعة
                </h5>
            </div>
            <div class="card-body">
                <?php if ($print_type == 'kitchen' || $print_type == 'both'): ?>
                    <!-- معاينة طلب المطبخ -->
                    <div class="card border-success mb-3">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">طلب المطبخ</h6>
                        </div>
                        <div class="card-body">
                            <div class="receipt-preview" style="font-family: monospace; font-size: 12px; line-height: 1.2; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                                <div style="text-align: center; font-weight: bold; font-size: 16px;">
                                    === المطبخ ===<br>
                                    <span style="font-size: 20px;">طلب جديد</span>
                                </div>
                                <div style="border-top: 2px solid #000; margin: 10px 0;"></div>
                                <div>
                                    <strong>رقم الطلب:</strong> #<?php echo $test_order_data['order_id']; ?><br>
                                    <strong>الطاولة:</strong> <?php echo $test_order_data['table_number']; ?><br>
                                    <strong>الوقت:</strong> <?php echo date('H:i:s'); ?><br>
                                    <strong>التاريخ:</strong> <?php echo date('Y-m-d'); ?>
                                </div>
                                <div style="border-top: 2px solid #000; margin: 10px 0;"></div>
                                <div style="text-align: center; font-weight: bold;">عناصر الطلب</div>
                                <div style="border-top: 1px solid #000; margin: 5px 0;"></div>
                                
                                <?php foreach ($test_order_data['items'] as $item): ?>
                                    <div style="margin: 10px 0;">
                                        <div style="font-weight: bold; font-size: 14px;"><?php echo $item['name']; ?></div>
                                        <div>الكمية: <?php echo $item['quantity']; ?> قطعة</div>
                                        <?php if (!empty($item['notes'])): ?>
                                            <div style="text-decoration: underline;">ملاحظات: <?php echo $item['notes']; ?></div>
                                        <?php endif; ?>
                                        <div style="border-top: 1px dashed #000; margin: 5px 0;"></div>
                                    </div>
                                <?php endforeach; ?>
                                
                                <div>
                                    <strong>إجمالي العناصر:</strong> <?php echo count($test_order_data['items']); ?><br>
                                    <strong>الموظف:</strong> <?php echo $test_order_data['user_name']; ?>
                                </div>
                                <div style="text-align: center; font-weight: bold; font-size: 14px; margin-top: 10px;">
                                    *** يرجى التحضير فوراً ***
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($print_type == 'cashier' || $print_type == 'both'): ?>
                    <!-- معاينة فاتورة الكاشير -->
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">فاتورة الكاشير</h6>
                        </div>
                        <div class="card-body">
                            <div class="receipt-preview" style="font-family: monospace; font-size: 12px; line-height: 1.2; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                                <div style="text-align: center; font-weight: bold; font-size: 16px;">
                                    مطعم وسام<br>
                                    <span style="font-size: 10px;">نظام إدارة المطعم المتكامل</span>
                                </div>
                                <div style="border-top: 2px solid #000; margin: 10px 0;"></div>
                                <div>
                                    <strong>رقم الفاتورة:</strong> #<?php echo $test_order_data['order_id']; ?><br>
                                    <strong>الطاولة:</strong> <?php echo $test_order_data['table_number']; ?><br>
                                    <strong>التاريخ:</strong> <?php echo date('Y-m-d'); ?><br>
                                    <strong>الوقت:</strong> <?php echo date('H:i:s'); ?><br>
                                    <strong>الكاشير:</strong> <?php echo $test_order_data['user_name']; ?>
                                </div>
                                <div style="border-top: 2px solid #000; margin: 10px 0;"></div>
                                <div style="font-weight: bold;">
                                    الصنف&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;كمية&nbsp;&nbsp;&nbsp;&nbsp;المبلغ
                                </div>
                                <div style="border-top: 1px solid #000; margin: 5px 0;"></div>
                                
                                <?php 
                                $total = 0;
                                foreach ($test_order_data['items'] as $item): 
                                    $item_total = $item['price'] * $item['quantity'];
                                    $total += $item_total;
                                    $name = mb_substr($item['name'], 0, 16);
                                ?>
                                    <div><?php echo sprintf("%-16s %3d %8s", $name, $item['quantity'], number_format($item_total) . ' د.ع'); ?></div>
                                <?php endforeach; ?>
                                
                                <div style="border-top: 1px solid #000; margin: 5px 0;"></div>
                                <div style="font-weight: bold; font-size: 14px;">
                                    الإجمالي: <?php echo number_format($total); ?> د.ع
                                </div>
                                <div style="border-top: 2px solid #000; margin: 10px 0;"></div>
                                <div>
                                    <strong>طريقة الدفع:</strong> نقداً<br>
                                    <strong>المبلغ المدفوع:</strong> <?php echo number_format($total); ?> د.ع<br>
                                    <strong>الباقي:</strong> 0 د.ع
                                </div>
                                <div style="text-align: center; font-weight: bold; margin-top: 10px;">
                                    شكراً لزيارتكم<br>
                                    نتطلع لخدمتكم مرة أخرى
                                </div>
                                <div style="text-align: center; font-size: 10px; margin-top: 10px;">
                                    هاتف: 07XX XXX XXXX<br>
                                    العنوان: بغداد - العراق
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($print_type == 'test'): ?>
                    <!-- معاينة اختبار الطابعات -->
                    <div class="card border-secondary">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0">اختبار الطابعات</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>اختبار الطابعات</h6>
                                <p>سيتم إرسال صفحة اختبار لكل طابعة تحتوي على:</p>
                                <ul>
                                    <li>معلومات الطابعة (IP، المنفذ، النوع)</li>
                                    <li>الوقت والتاريخ الحالي</li>
                                    <li>رسالة تأكيد عمل الطابعة</li>
                                    <li>اختبار الخطوط والتنسيق</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الطباعة -->
<div class="card mt-4">
    <div class="card-header bg-dark text-white">
        <h5 class="mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            إحصائيات الطباعة
        </h5>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-3">
                <div class="card border-success">
                    <div class="card-body">
                        <h5 class="text-success">طلبات المطبخ</h5>
                        <h3 class="text-success">156</h3>
                        <small class="text-muted">اليوم</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card border-info">
                    <div class="card-body">
                        <h5 class="text-info">فواتير الكاشير</h5>
                        <h3 class="text-info">142</h3>
                        <small class="text-muted">اليوم</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card border-warning">
                    <div class="card-body">
                        <h5 class="text-warning">أخطاء الطباعة</h5>
                        <h3 class="text-warning">3</h3>
                        <small class="text-muted">اليوم</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card border-primary">
                    <div class="card-body">
                        <h5 class="text-primary">معدل النجاح</h5>
                        <h3 class="text-primary">98%</h3>
                        <small class="text-muted">اليوم</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث الصفحة كل 30 ثانية لإظهار آخر الإحصائيات
setTimeout(function() {
    // يمكن إضافة AJAX لتحديث الإحصائيات
}, 30000);

// إضافة صوت عند نجاح الطباعة
<?php if (isset($print_result) && $print_result): ?>
    // تشغيل صوت نجاح (اختياري)
    console.log('تم طباعة الطلب بنجاح!');
<?php endif; ?>
</script>

<?php include 'includes/footer.php'; ?>
