<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سلة الطلب الحقيقية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="alert alert-success">
            <h4><i class="fas fa-check-circle me-2"></i>تم إصلاح مشكلة أزرار + و - في سلة الطلب!</h4>
            <p><strong>المشكلة كانت:</strong> دالة updateQuantity مكررة في add_order.php</p>
            <p><strong>الحل:</strong> تم حذف الدالة المكررة والاحتفاظ بالدالة المحسنة</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bug me-2"></i>
                            ما تم إصلاحه
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-times-circle text-danger me-2"></i>
                                <div>
                                    <strong>المشكلة:</strong> دالة updateQuantity مكررة<br>
                                    <small class="text-muted">السطر 281-291 و 306-346</small>
                                </div>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <div>
                                    <strong>الحل:</strong> حذف الدالة البسيطة<br>
                                    <small class="text-muted">الاحتفاظ بالدالة المحسنة مع console.log</small>
                                </div>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <div>
                                    <strong>النتيجة:</strong> أزرار + و - تعمل الآن<br>
                                    <small class="text-muted">مع رسائل تشخيص في Console</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>
                            خطوات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol class="list-group list-group-numbered">
                            <li class="list-group-item">
                                <strong>افتح الصفحة الحقيقية:</strong><br>
                                <a href="add_order.php?table_id=1" class="btn btn-sm btn-primary mt-1" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    add_order.php?table_id=1
                                </a>
                            </li>
                            <li class="list-group-item">
                                <strong>افتح Developer Tools:</strong><br>
                                <kbd>F12</kbd> ثم اذهب لتبويب <strong>Console</strong>
                            </li>
                            <li class="list-group-item">
                                <strong>أضف منتج للسلة:</strong><br>
                                اضغط على أي منتج من القائمة
                            </li>
                            <li class="list-group-item">
                                <strong>اختبر أزرار + و -:</strong><br>
                                يجب أن تظهر رسائل في Console
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- رسائل Console المتوقعة -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-terminal me-2"></i>
                    رسائل Console المتوقعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">✅ عند إضافة منتج:</h6>
                        <pre class="bg-light p-2 rounded"><code>🔄 تحديث عرض السلة...
📋 عناصر السلة الحالية: {12: {id: 12, name: "كباب عراقي", ...}}
✅ تم تحديث العرض بنجاح
💰 الإجمالي: 8000 د.ع
📦 عدد العناصر: 1</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">✅ عند الضغط على زر +:</h6>
                        <pre class="bg-light p-2 rounded"><code>🔄 تحديث الكمية: 12 التغيير: 1
📊 الكمية: 1 → 2
📋 حالة السلة بعد التحديث: {12: {id: 12, ...}}
🔄 تحديث عرض السلة...
✅ تم تحديث العرض بنجاح</code></pre>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6 class="text-warning">⚠️ عند الضغط على زر -:</h6>
                        <pre class="bg-light p-2 rounded"><code>🔄 تحديث الكمية: 12 التغيير: -1
📊 الكمية: 2 → 1
📋 حالة السلة بعد التحديث: {12: {id: 12, ...}}
🔄 تحديث عرض السلة...
✅ تم تحديث العرض بنجاح</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">🗑️ عند حذف منتج (كمية 0):</h6>
                        <pre class="bg-light p-2 rounded"><code>🔄 تحديث الكمية: 12 التغيير: -1
📊 الكمية: 1 → 0
🗑️ حذف المنتج من السلة (كمية <= 0)
📋 حالة السلة بعد التحديث: {}
🔄 تحديث عرض السلة...</code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار مباشر -->
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-flask me-2"></i>
                    اختبار مباشر للدوال
                </h5>
            </div>
            <div class="card-body">
                <p>يمكنك اختبار الدوال مباشرة في Console للصفحة الحقيقية:</p>
                
                <div class="row">
                    <div class="col-md-4">
                        <h6>اختبار إضافة منتج:</h6>
                        <pre class="bg-light p-2 rounded small"><code>// في Console
addProductToOrder(12);</code></pre>
                    </div>
                    <div class="col-md-4">
                        <h6>اختبار زيادة الكمية:</h6>
                        <pre class="bg-light p-2 rounded small"><code>// في Console
updateQuantity(12, 1);</code></pre>
                    </div>
                    <div class="col-md-4">
                        <h6>اختبار تقليل الكمية:</h6>
                        <pre class="bg-light p-2 rounded small"><code>// في Console
updateQuantity(12, -1);</code></pre>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-4">
                        <h6>عرض محتويات السلة:</h6>
                        <pre class="bg-light p-2 rounded small"><code>// في Console
console.log(orderItems);</code></pre>
                    </div>
                    <div class="col-md-4">
                        <h6>اختبار تحديث العرض:</h6>
                        <pre class="bg-light p-2 rounded small"><code>// في Console
updateOrderDisplay();</code></pre>
                    </div>
                    <div class="col-md-4">
                        <h6>فحص العناصر HTML:</h6>
                        <pre class="bg-light p-2 rounded small"><code>// في Console
document.getElementById('order-items')</code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط إضافية -->
        <div class="text-center mt-4">
            <h5>روابط الاختبار:</h5>
            <div class="btn-group" role="group">
                <a href="add_order.php?table_id=1" class="btn btn-success" target="_blank">
                    <i class="fas fa-shopping-cart me-2"></i>
                    الصفحة الحقيقية
                </a>
                <a href="debug_cart_buttons.php" class="btn btn-info">
                    <i class="fas fa-bug me-2"></i>
                    صفحة التشخيص
                </a>
                <a href="test_add_order.php" class="btn btn-warning">
                    <i class="fas fa-flask me-2"></i>
                    صفحة اختبار أخرى
                </a>
                <a href="dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>
        
        <!-- تعليمات إضافية -->
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-lightbulb me-2"></i>نصائح للاختبار:</h6>
            <ul class="mb-0">
                <li><strong>إذا لم تظهر رسائل Console:</strong> تأكد من فتح تبويب Console في Developer Tools</li>
                <li><strong>إذا لم تعمل الأزرار:</strong> امسح التخزين المؤقت (Ctrl+F5) وأعد المحاولة</li>
                <li><strong>إذا ظهرت أخطاء:</strong> انسخ الرسالة وأرسلها للمطور</li>
                <li><strong>للتأكد من الإصلاح:</strong> يجب أن تظهر رسائل 🔄 و ✅ عند الضغط على الأزرار</li>
            </ul>
        </div>
    </div>

    <script>
        // إضافة بعض الدوال المساعدة
        function openRealPage() {
            window.open('add_order.php?table_id=1', '_blank');
        }
        
        function showConsoleInstructions() {
            alert(`خطوات فتح Console:

1. اضغط F12 في المتصفح
2. اذهب لتبويب "Console"
3. ستظهر رسائل عند استخدام الأزرار

أو:
- Chrome: Ctrl+Shift+J
- Firefox: Ctrl+Shift+K
- Edge: F12 ثم Console`);
        }
        
        // عرض تعليمات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎉 تم إصلاح مشكلة أزرار + و - في سلة الطلب!');
            console.log('🔗 افتح الصفحة الحقيقية: add_order.php?table_id=1');
            console.log('🧪 اختبر الأزرار وراقب هذه الرسائل');
        });
    </script>
</body>
</html>
