<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || (!hasRole('admin') && !hasRole('cashier'))) {
    redirect('login.php');
}

$page_title = 'نظام الطلبات الفائق - Ultra Modern';

// جلب الطلبات مع تفاصيل كاملة
$stmt = $pdo->prepare("
    SELECT
        o.id,
        o.table_id,
        o.total_amount,
        o.status,
        o.created_at,
        o.updated_at,
        t.table_number,
        u.username as user_name,
        COUNT(oi.id) as items_count,
        GROUP_CONCAT(CONCAT(p.name, ' (', oi.quantity, ')') SEPARATOR ', ') as items_summary
    FROM orders o
    LEFT JOIN tables t ON o.table_id = t.id
    LEFT JOIN users u ON o.user_id = u.id
    LEFT JOIN order_items oi ON o.id = oi.order_id
    LEFT JOIN products p ON oi.product_id = p.id
    WHERE DATE(o.created_at) = CURDATE()
    GROUP BY o.id
    ORDER BY
        CASE o.status
            WHEN 'pending' THEN 1
            WHEN 'processing' THEN 2
            WHEN 'completed' THEN 3
            WHEN 'cancelled' THEN 4
        END,
        o.created_at DESC
");
$stmt->execute();
$orders = $stmt->fetchAll();

include 'includes/header.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الطلبات الفائق</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body class="bg-light">

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1">
                                <i class="fas fa-rocket me-2"></i>
                                نظام الطلبات الفائق
                            </h2>
                            <p class="mb-0 opacity-75">تقنيات متقدمة • تشخيص ذكي • أداء فائق</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-light btn-sm" onclick="ultraManager.forceRefresh()">
                                <i class="fas fa-sync-alt me-1"></i>تحديث فوري
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="ultraManager.runDiagnostics()">
                                <i class="fas fa-stethoscope me-1"></i>تشخيص
                            </button>
                            <a href="add_test_orders.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>طلبات تجريبية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="status-indicator me-2" id="system-status">
                                    <div class="spinner-border spinner-border-sm text-success" role="status"></div>
                                </div>
                                <span class="small">حالة النظام</span>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-warning">
                                <i class="fas fa-clock fa-2x mb-1"></i>
                                <div><strong id="pending-count">0</strong></div>
                                <small>معلقة</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-info">
                                <i class="fas fa-cog fa-2x mb-1"></i>
                                <div><strong id="processing-count">0</strong></div>
                                <small>قيد التحضير</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x mb-1"></i>
                                <div><strong id="completed-count">0</strong></div>
                                <small>مكتملة</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-danger">
                                <i class="fas fa-times-circle fa-2x mb-1"></i>
                                <div><strong id="cancelled-count">0</strong></div>
                                <small>ملغية</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-primary">
                                <i class="fas fa-chart-line fa-2x mb-1"></i>
                                <div><strong id="success-rate">100%</strong></div>
                                <small>معدل النجاح</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Grid -->
    <div class="row" id="orders-grid">
        <!-- سيتم ملؤها بـ JavaScript -->
    </div>

    <!-- Debug Console -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-terminal me-2"></i>
                        وحدة التشخيص المتقدم
                    </h6>
                    <button class="btn btn-sm btn-outline-light" onclick="ultraManager.clearConsole()">
                        <i class="fas fa-trash me-1"></i>مسح
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="debug-console" style="height: 200px; overflow-y: auto; background: #1a1a1a; color: #00ff00; font-family: 'Courier New', monospace; font-size: 12px; padding: 10px;">
                        <div class="console-line">[SYSTEM] Ultra Modern Orders System v2.0 - Initializing...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Confirmation Modal -->
<div class="modal fade" id="ultraConfirmModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header border-0" id="modal-header">
                <h5 class="modal-title" id="modal-title">تأكيد العملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div id="modal-icon" class="mb-3">
                    <i class="fas fa-question-circle fa-3x text-primary"></i>
                </div>
                <h6 id="modal-message">هل أنت متأكد من هذا الإجراء؟</h6>
                <p class="text-muted small" id="modal-details"></p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="button" class="btn btn-primary" id="ultra-confirm-btn">
                    <i class="fas fa-check me-1"></i>تأكيد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header border-0 bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الطلب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="order-details-content">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<style>
/* Ultra Modern Styles */
.order-card {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: none;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.order-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.order-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.order-card.status-pending {
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
}

.order-card.status-processing {
    background: linear-gradient(135deg, #d1ecf1 0%, #ffffff 100%);
}

.order-card.status-completed {
    background: linear-gradient(135deg, #d4edda 0%, #ffffff 100%);
}

.order-card.status-cancelled {
    background: linear-gradient(135deg, #f8d7da 0%, #ffffff 100%);
}

.ultra-btn {
    border: none;
    border-radius: 50px;
    padding: 10px 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ultra-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.ultra-btn:hover::before {
    left: 100%;
}

.ultra-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.console-line {
    margin-bottom: 2px;
    opacity: 0;
    animation: fadeInConsole 0.5s ease forwards;
}

@keyframes fadeInConsole {
    to { opacity: 1; }
}

.pulse-success {
    animation: pulseSuccess 2s infinite;
}

@keyframes pulseSuccess {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    z-index: 10;
}

.order-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-btn:hover {
    transform: scale(1.1) rotate(5deg);
}

.order-timer {
    font-size: 11px;
    opacity: 0.8;
    font-weight: 500;
}

.order-priority {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.priority-high { background: #ff4757; }
.priority-medium { background: #ffa502; }
.priority-low { background: #2ed573; }
</style>

<script>
/**
 * Ultra Modern Orders Management System
 * نظام إدارة الطلبات الفائق مع تشخيص متقدم
 */
class UltraModernOrdersManager {
    constructor() {
        this.orders = <?php echo json_encode($orders); ?>;
        this.currentOrderId = null;
        this.confirmCallback = null;
        this.diagnostics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            lastError: null
        };
        this.isOnline = navigator.onLine;

        this.init();
    }

    init() {
        this.log('🚀 تهيئة نظام الطلبات الفائق v2.0', 'system');

        // تهيئة المكونات
        this.setupEventListeners();
        this.renderOrders();
        this.updateStatistics();
        this.startSystemMonitoring();
        this.checkSystemHealth();

        this.log('✅ تم تهيئة النظام بنجاح', 'success');
        this.updateSystemStatus('online');
    }

    setupEventListeners() {
        // Event delegation متقدم
        document.getElementById('orders-grid').addEventListener('click', this.handleOrderAction.bind(this));

        // زر التأكيد المتقدم
        document.getElementById('ultra-confirm-btn').addEventListener('click', this.executeConfirmedAction.bind(this));

        // مراقبة حالة الاتصال
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.updateSystemStatus('online');
            this.log('🌐 تم استعادة الاتصال بالإنترنت', 'success');
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateSystemStatus('offline');
            this.log('⚠️ انقطع الاتصال بالإنترنت', 'warning');
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

        this.log('🔗 تم ربط Event Listeners المتقدمة', 'info');
    }

    handleOrderAction(event) {
        const target = event.target.closest('[data-action]');
        if (!target) return;

        event.preventDefault();

        const action = target.dataset.action;
        const orderId = parseInt(target.dataset.orderId);

        if (!orderId || orderId <= 0) {
            this.log(`❌ معرف طلب غير صحيح: ${orderId}`, 'error');
            this.showAdvancedAlert('خطأ', 'معرف الطلب غير صحيح', 'error');
            return;
        }

        this.log(`🎯 تنفيذ إجراء: ${action} للطلب: ${orderId}`, 'action');

        // تعطيل الزر مؤقتاً لمنع النقر المتكرر
        target.disabled = true;
        setTimeout(() => target.disabled = false, 2000);

        switch (action) {
            case 'start':
                this.showAdvancedConfirmation(
                    'بدء التحضير',
                    `هل تريد بدء تحضير الطلب #${orderId}؟`,
                    'سيتم تغيير حالة الطلب إلى "قيد التحضير"',
                    'warning',
                    () => this.updateOrderStatus(orderId, 'processing')
                );
                break;

            case 'complete':
                this.showAdvancedConfirmation(
                    'إكمال الطلب',
                    `هل تم الانتهاء من تحضير الطلب #${orderId}؟`,
                    'سيتم تغيير حالة الطلب إلى "مكتمل" وتحرير الطاولة',
                    'success',
                    () => this.updateOrderStatus(orderId, 'completed')
                );
                break;

            case 'cancel':
                this.showAdvancedConfirmation(
                    'إلغاء الطلب',
                    `هل أنت متأكد من إلغاء الطلب #${orderId}؟`,
                    'لا يمكن التراجع عن هذا الإجراء',
                    'danger',
                    () => this.updateOrderStatus(orderId, 'cancelled')
                );
                break;

            case 'view':
                this.viewOrderDetails(orderId);
                break;

            case 'edit':
                window.open(`edit_order.php?id=${orderId}`, '_blank');
                break;

            case 'print':
                this.printOrder(orderId);
                break;

            default:
                this.log(`❌ إجراء غير معروف: ${action}`, 'error');
        }
    }

    async updateOrderStatus(orderId, newStatus) {
        const startTime = Date.now();
        this.diagnostics.totalRequests++;

        this.log(`🔄 بدء تحديث الطلب ${orderId} إلى ${newStatus}`, 'info');
        this.showOrderLoading(orderId, true);

        try {
            // التحقق من الاتصال
            if (!this.isOnline) {
                throw new Error('لا يوجد اتصال بالإنترنت');
            }

            const response = await fetch('ajax/ultra_update_order.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    order_id: orderId,
                    status: newStatus,
                    timestamp: Date.now(),
                    user_agent: navigator.userAgent
                })
            });

            const responseTime = Date.now() - startTime;
            this.updateResponseTime(responseTime);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.diagnostics.successfulRequests++;
                this.log(`✅ تم تحديث الطلب ${orderId} بنجاح في ${responseTime}ms`, 'success');

                // تحديث البيانات المحلية
                await this.refreshOrderData();
                this.renderOrders();
                this.updateStatistics();

                // إشعار متقدم
                this.showAdvancedAlert(
                    'تم بنجاح',
                    data.message,
                    'success',
                    3000
                );

                // تأثير بصري
                this.highlightOrderCard(orderId);

            } else {
                throw new Error(data.message || 'فشل في تحديث الطلب');
            }

        } catch (error) {
            this.diagnostics.failedRequests++;
            this.diagnostics.lastError = {
                message: error.message,
                timestamp: new Date().toISOString(),
                orderId: orderId,
                action: newStatus
            };

            this.log(`❌ خطأ في تحديث الطلب ${orderId}: ${error.message}`, 'error');

            this.showAdvancedAlert(
                'حدث خطأ',
                `فشل في تحديث الطلب: ${error.message}`,
                'error'
            );

        } finally {
            this.showOrderLoading(orderId, false);
            this.updateSuccessRate();
        }
    }

    async refreshOrderData() {
        try {
            const response = await fetch('ajax/get_orders.php');
            const data = await response.json();

            if (data.success) {
                this.orders = data.orders;
                this.log(`🔄 تم تحديث ${data.orders.length} طلب`, 'info');
                return true;
            } else {
                throw new Error(data.message || 'فشل في جلب البيانات');
            }
        } catch (error) {
            this.log(`❌ خطأ في تحديث البيانات: ${error.message}`, 'error');
            return false;
        }
    }

    renderOrders() {
        const container = document.getElementById('orders-grid');

        if (!this.orders || this.orders.length === 0) {
            container.innerHTML = this.createEmptyState();
            return;
        }

        const html = this.orders.map((order, index) =>
            this.createUltraOrderCard(order, index)
        ).join('');

        container.innerHTML = html;

        // تأثيرات الظهور المتدرجة
        this.animateOrderCards();

        this.log(`📋 تم عرض ${this.orders.length} طلب`, 'info');
    }

    createUltraOrderCard(order, index) {
        const statusConfig = {
            pending: {
                class: 'warning',
                text: 'معلق',
                icon: 'clock',
                actions: ['start', 'edit', 'cancel', 'view', 'print'],
                priority: 'high'
            },
            processing: {
                class: 'info',
                text: 'قيد التحضير',
                icon: 'cog fa-spin',
                actions: ['complete', 'cancel', 'view', 'print'],
                priority: 'medium'
            },
            completed: {
                class: 'success',
                text: 'مكتمل',
                icon: 'check-circle',
                actions: ['view', 'print'],
                priority: 'low'
            },
            cancelled: {
                class: 'danger',
                text: 'ملغي',
                icon: 'times-circle',
                actions: ['view'],
                priority: 'low'
            }
        };

        const config = statusConfig[order.status] || statusConfig.pending;
        const timeAgo = this.getAdvancedTimeAgo(order.created_at);
        const priority = this.calculateOrderPriority(order);

        return `
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4" style="animation-delay: ${index * 0.1}s">
                <div class="card order-card status-${order.status} shadow-sm"
                     data-order-id="${order.id}"
                     id="order-card-${order.id}">

                    <div class="order-priority priority-${priority}"></div>

                    <div class="card-header border-0 bg-transparent">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0 fw-bold">
                                    <i class="fas fa-hashtag me-1 text-primary"></i>
                                    طلب ${order.id}
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-table me-1"></i>
                                    ${order.table_number || 'طاولة ' + order.table_id}
                                </small>
                            </div>
                            <span class="badge bg-${config.class} px-3 py-2">
                                <i class="fas fa-${config.icon} me-1"></i>
                                ${config.text}
                            </span>
                        </div>
                    </div>

                    <div class="card-body pt-0">
                        <div class="order-items mb-3 p-2 bg-light rounded">
                            <i class="fas fa-utensils me-1 text-muted"></i>
                            <small>${order.items_summary || 'لا توجد عناصر'}</small>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong class="text-success fs-6">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    ${parseInt(order.total_amount).toLocaleString()} د.ع
                                </strong>
                            </div>
                            <div class="order-timer text-muted">
                                <i class="fas fa-clock me-1"></i>
                                ${timeAgo}
                            </div>
                        </div>

                        <div class="order-actions">
                            ${this.createUltraActionButtons(order.id, config.actions)}
                        </div>

                        <div class="loading-overlay d-none" id="loading-${order.id}">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحديث...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createUltraActionButtons(orderId, actions) {
        const buttons = {
            start: `<button class="action-btn btn btn-success" data-action="start" data-order-id="${orderId}" title="بدء التحضير">
                        <i class="fas fa-play"></i>
                    </button>`,
            complete: `<button class="action-btn btn btn-info" data-action="complete" data-order-id="${orderId}" title="إكمال الطلب">
                          <i class="fas fa-check"></i>
                       </button>`,
            edit: `<button class="action-btn btn btn-warning" data-action="edit" data-order-id="${orderId}" title="تعديل">
                      <i class="fas fa-edit"></i>
                   </button>`,
            cancel: `<button class="action-btn btn btn-danger" data-action="cancel" data-order-id="${orderId}" title="إلغاء">
                        <i class="fas fa-times"></i>
                     </button>`,
            view: `<button class="action-btn btn btn-primary" data-action="view" data-order-id="${orderId}" title="عرض التفاصيل">
                      <i class="fas fa-eye"></i>
                   </button>`,
            print: `<button class="action-btn btn btn-secondary" data-action="print" data-order-id="${orderId}" title="طباعة">
                       <i class="fas fa-print"></i>
                    </button>`
        };

        return actions.map(action => buttons[action] || '').join('');
    }

    showAdvancedConfirmation(title, message, details, type, callback) {
        const modal = document.getElementById('ultraConfirmModal');
        const header = document.getElementById('modal-header');
        const titleEl = document.getElementById('modal-title');
        const iconEl = document.getElementById('modal-icon');
        const messageEl = document.getElementById('modal-message');
        const detailsEl = document.getElementById('modal-details');
        const confirmBtn = document.getElementById('ultra-confirm-btn');

        // تخصيص المظهر حسب النوع
        const configs = {
            success: {
                headerClass: 'bg-success text-white',
                icon: 'fas fa-check-circle fa-3x text-success',
                btnClass: 'btn-success'
            },
            warning: {
                headerClass: 'bg-warning text-dark',
                icon: 'fas fa-exclamation-triangle fa-3x text-warning',
                btnClass: 'btn-warning'
            },
            danger: {
                headerClass: 'bg-danger text-white',
                icon: 'fas fa-exclamation-circle fa-3x text-danger',
                btnClass: 'btn-danger'
            },
            info: {
                headerClass: 'bg-info text-white',
                icon: 'fas fa-info-circle fa-3x text-info',
                btnClass: 'btn-info'
            }
        };

        const config = configs[type] || configs.info;

        header.className = `modal-header border-0 ${config.headerClass}`;
        titleEl.textContent = title;
        iconEl.innerHTML = `<i class="${config.icon}"></i>`;
        messageEl.textContent = message;
        detailsEl.textContent = details;
        confirmBtn.className = `btn ${config.btnClass}`;

        this.confirmCallback = callback;

        new bootstrap.Modal(modal).show();

        this.log(`💬 عرض تأكيد متقدم: ${title}`, 'info');
    }

    executeConfirmedAction() {
        if (this.confirmCallback) {
            this.confirmCallback();
            this.confirmCallback = null;

            // إغلاق Modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('ultraConfirmModal'));
            if (modal) modal.hide();
        }
    }

    showOrderLoading(orderId, show) {
        const loadingEl = document.getElementById(`loading-${orderId}`);
        if (loadingEl) {
            loadingEl.classList.toggle('d-none', !show);
        }
    }

    highlightOrderCard(orderId) {
        const card = document.getElementById(`order-card-${orderId}`);
        if (card) {
            card.classList.add('pulse-success');
            setTimeout(() => {
                card.classList.remove('pulse-success');
            }, 3000);
        }
    }

    updateStatistics() {
        const stats = {
            pending: 0,
            processing: 0,
            completed: 0,
            cancelled: 0
        };

        this.orders.forEach(order => {
            stats[order.status] = (stats[order.status] || 0) + 1;
        });

        // تحديث العدادات مع تأثيرات
        Object.keys(stats).forEach(status => {
            const element = document.getElementById(`${status}-count`);
            if (element) {
                this.animateCounter(element, parseInt(element.textContent) || 0, stats[status]);
            }
        });

        this.log(`📊 إحصائيات محدثة: ${JSON.stringify(stats)}`, 'info');
    }

    animateCounter(element, from, to) {
        const duration = 1000;
        const steps = 20;
        const stepValue = (to - from) / steps;
        let current = from;
        let step = 0;

        const timer = setInterval(() => {
            current += stepValue;
            element.textContent = Math.round(current);

            if (++step >= steps) {
                element.textContent = to;
                clearInterval(timer);
            }
        }, duration / steps);
    }

    // دوال مساعدة متقدمة
    getAdvancedTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);

        if (diffMins < 1) return 'الآن';
        if (diffMins < 60) return `${diffMins} دقيقة`;

        const diffHours = Math.floor(diffMins / 60);
        if (diffHours < 24) return `${diffHours} ساعة`;

        return date.toLocaleDateString('ar');
    }

    calculateOrderPriority(order) {
        const now = new Date();
        const created = new Date(order.created_at);
        const ageMinutes = (now - created) / 60000;

        if (order.status === 'pending' && ageMinutes > 15) return 'high';
        if (order.status === 'processing' && ageMinutes > 30) return 'high';
        if (order.status === 'pending') return 'medium';
        return 'low';
    }

    createEmptyState() {
        return `
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-inbox fa-5x text-muted opacity-50"></i>
                    </div>
                    <h4 class="text-muted">لا توجد طلبات اليوم</h4>
                    <p class="text-muted">ابدأ بإضافة طلب جديد لرؤية النظام في العمل</p>
                    <div class="mt-4">
                        <a href="add_test_orders.php" class="btn btn-primary ultra-btn me-2">
                            <i class="fas fa-plus me-2"></i>إضافة طلبات تجريبية
                        </a>
                        <a href="add_order.php?table_id=1" class="btn btn-success ultra-btn">
                            <i class="fas fa-utensils me-2"></i>طلب جديد
                        </a>
                    </div>
                </div>
            </div>
        `;
    }

    animateOrderCards() {
        const cards = document.querySelectorAll('.order-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // دوال التشخيص والمراقبة
    log(message, type = 'info') {
        const console = document.getElementById('debug-console');
        const timestamp = new Date().toLocaleTimeString();

        const icons = {
            system: '⚙️',
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            action: '🎯'
        };

        const colors = {
            system: '#00bcd4',
            success: '#4caf50',
            error: '#f44336',
            warning: '#ff9800',
            info: '#2196f3',
            action: '#9c27b0'
        };

        const line = document.createElement('div');
        line.className = 'console-line';
        line.style.color = colors[type] || '#00ff00';
        line.innerHTML = `[${timestamp}] ${icons[type] || 'ℹ️'} ${message}`;

        console.appendChild(line);
        console.scrollTop = console.scrollHeight;

        // الاحتفاظ بآخر 100 سطر فقط
        const lines = console.querySelectorAll('.console-line');
        if (lines.length > 100) {
            lines[0].remove();
        }
    }

    updateSystemStatus(status) {
        const indicator = document.getElementById('system-status');
        const statusConfig = {
            online: { class: 'text-success', icon: 'fas fa-check-circle' },
            offline: { class: 'text-danger', icon: 'fas fa-times-circle' },
            loading: { class: 'text-warning', icon: 'fas fa-spinner fa-spin' }
        };

        const config = statusConfig[status] || statusConfig.loading;
        indicator.innerHTML = `<i class="${config.icon} ${config.class}"></i>`;
    }

    updateResponseTime(time) {
        this.diagnostics.averageResponseTime =
            (this.diagnostics.averageResponseTime + time) / 2;
    }

    updateSuccessRate() {
        const rate = this.diagnostics.totalRequests > 0
            ? Math.round((this.diagnostics.successfulRequests / this.diagnostics.totalRequests) * 100)
            : 100;

        const element = document.getElementById('success-rate');
        if (element) {
            element.textContent = `${rate}%`;
            element.className = rate >= 90 ? 'text-success' : rate >= 70 ? 'text-warning' : 'text-danger';
        }
    }

    startSystemMonitoring() {
        // تحديث تلقائي كل 30 ثانية
        setInterval(async () => {
            this.log('🔄 تحديث تلقائي للبيانات', 'system');
            const success = await this.refreshOrderData();
            if (success) {
                this.renderOrders();
                this.updateStatistics();
            }
        }, 30000);

        this.log('🔄 تم تفعيل المراقبة التلقائية', 'system');
    }

    checkSystemHealth() {
        const health = {
            memory: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 'غير متاح',
            connection: this.isOnline ? 'متصل' : 'منقطع',
            orders: this.orders.length,
            timestamp: new Date().toISOString()
        };

        this.log(`💊 فحص صحة النظام: الذاكرة ${health.memory}MB، الطلبات ${health.orders}`, 'system');
    }

    // دوال عامة
    forceRefresh() {
        this.log('🔄 تحديث فوري مطلوب من المستخدم', 'action');
        location.reload();
    }

    runDiagnostics() {
        this.log('🔍 تشغيل التشخيص الشامل', 'system');

        const report = {
            ...this.diagnostics,
            systemHealth: {
                online: this.isOnline,
                ordersCount: this.orders.length,
                memoryUsage: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 'غير متاح'
            }
        };

        this.showAdvancedAlert(
            'تقرير التشخيص',
            `الطلبات الكلية: ${report.totalRequests}\nالناجحة: ${report.successfulRequests}\nالفاشلة: ${report.failedRequests}\nمتوسط الاستجابة: ${Math.round(report.averageResponseTime)}ms`,
            'info'
        );

        this.log(`📊 تقرير التشخيص: ${JSON.stringify(report)}`, 'system');
    }

    clearConsole() {
        document.getElementById('debug-console').innerHTML =
            '<div class="console-line">[SYSTEM] تم مسح وحدة التشخيص</div>';
    }

    showAdvancedAlert(title, message, type, duration = 5000) {
        Swal.fire({
            title: title,
            text: message,
            icon: type,
            timer: duration,
            timerProgressBar: true,
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            background: '#fff',
            color: '#333'
        });
    }

    handleKeyboardShortcuts(event) {
        // Ctrl + R للتحديث
        if (event.ctrlKey && event.key === 'r') {
            event.preventDefault();
            this.forceRefresh();
        }

        // Ctrl + D للتشخيص
        if (event.ctrlKey && event.key === 'd') {
            event.preventDefault();
            this.runDiagnostics();
        }
    }

    async viewOrderDetails(orderId) {
        try {
            this.log(`👁️ عرض تفاصيل الطلب ${orderId}`, 'action');

            const response = await fetch(`ajax/get_order_details.php?order_id=${orderId}`);
            const html = await response.text();

            document.getElementById('order-details-content').innerHTML = html;
            new bootstrap.Modal(document.getElementById('orderDetailsModal')).show();

        } catch (error) {
            this.log(`❌ خطأ في عرض التفاصيل: ${error.message}`, 'error');
            this.showAdvancedAlert('خطأ', 'فشل في جلب تفاصيل الطلب', 'error');
        }
    }

    printOrder(orderId) {
        this.log(`🖨️ طباعة الطلب ${orderId}`, 'action');
        window.open(`print_invoice.php?order_id=${orderId}`, '_blank', 'width=800,height=600');
    }
}

// تهيئة النظام الفائق
let ultraManager;

document.addEventListener('DOMContentLoaded', function() {
    ultraManager = new UltraModernOrdersManager();

    // إضافة النظام للنطاق العام للوصول من الخارج
    window.ultraManager = ultraManager;

    console.log('🚀 Ultra Modern Orders System v2.0 - Ready!');
});
</script>

<?php include 'includes/footer.php'; ?>
</body>
</html>