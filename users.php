<?php
require_once 'config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('login.php');
}

$page_title = 'إدارة المستخدمين';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        try {
            if ($action == 'add') {
                $username = trim($_POST['username']);
                $password = $_POST['password'];
                $full_name = trim($_POST['full_name']);
                $role = $_POST['role'];
                
                // التحقق من عدم وجود اسم المستخدم
                $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                $stmt->execute([$username]);
                if ($stmt->fetch()) {
                    showMessage('اسم المستخدم موجود بالفعل', 'error');
                } else {
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    
                    $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$username, $hashed_password, $full_name, $role]);
                    
                    showMessage('تم إضافة المستخدم بنجاح', 'success');
                }
                
            } elseif ($action == 'edit') {
                $id = intval($_POST['id']);
                $username = trim($_POST['username']);
                $full_name = trim($_POST['full_name']);
                $role = $_POST['role'];
                
                // التحقق من عدم وجود اسم المستخدم لمستخدم آخر
                $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
                $stmt->execute([$username, $id]);
                if ($stmt->fetch()) {
                    showMessage('اسم المستخدم موجود بالفعل', 'error');
                } else {
                    $stmt = $pdo->prepare("UPDATE users SET username = ?, full_name = ?, role = ? WHERE id = ?");
                    $stmt->execute([$username, $full_name, $role, $id]);
                    
                    showMessage('تم تحديث المستخدم بنجاح', 'success');
                }
                
            } elseif ($action == 'change_password') {
                $id = intval($_POST['id']);
                $new_password = $_POST['new_password'];
                
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                $stmt->execute([$hashed_password, $id]);
                
                showMessage('تم تغيير كلمة المرور بنجاح', 'success');
                
            } elseif ($action == 'delete') {
                $id = intval($_POST['id']);
                
                // منع حذف المستخدم الحالي
                if ($id == $_SESSION['user_id']) {
                    showMessage('لا يمكن حذف المستخدم الحالي', 'error');
                } else {
                    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    showMessage('تم حذف المستخدم بنجاح', 'success');
                }
            }
        } catch (PDOException $e) {
            showMessage('حدث خطأ في العملية', 'error');
        }
    }
}

// جلب المستخدمين
$search = $_GET['search'] ?? '';
$role_filter = $_GET['role'] ?? '';

try {
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(username LIKE ? OR full_name LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    if (!empty($role_filter)) {
        $where_conditions[] = "role = ?";
        $params[] = $role_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $pdo->prepare("SELECT * FROM users $where_clause ORDER BY created_at DESC");
    $stmt->execute($params);
    $users = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب البيانات';
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users me-2"></i>إدارة المستخدمين</h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal">
        <i class="fas fa-plus me-2"></i>مستخدم جديد
    </button>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="role" class="form-label">الدور</label>
                <select class="form-select" id="role" name="role">
                    <option value="">جميع الأدوار</option>
                    <option value="admin" <?php echo $role_filter == 'admin' ? 'selected' : ''; ?>>مدير</option>
                    <option value="cashier" <?php echo $role_filter == 'cashier' ? 'selected' : ''; ?>>كاشير</option>
                    <option value="kitchen" <?php echo $role_filter == 'kitchen' ? 'selected' : ''; ?>>مطبخ</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="اسم المستخدم أو الاسم الكامل">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
    </div>
<?php endif; ?>

<!-- جدول المستخدمين -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المستخدمين (<?php echo count($users); ?>)</h5>
    </div>
    <div class="card-body">
        <?php if (empty($users)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مستخدمين</h4>
                <p class="text-muted">لم يتم العثور على مستخدمين تطابق معايير البحث</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal">
                    <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>الدور</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $user['username']; ?></strong>
                                    <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                        <span class="badge bg-info ms-2">أنت</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $user['full_name']; ?></td>
                                <td>
                                    <?php
                                    $role_colors = [
                                        'admin' => 'danger',
                                        'cashier' => 'success',
                                        'kitchen' => 'warning'
                                    ];
                                    $role_text = [
                                        'admin' => 'مدير',
                                        'cashier' => 'كاشير',
                                        'kitchen' => 'مطبخ'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $role_colors[$user['role']]; ?>">
                                        <?php echo $role_text[$user['role']]; ?>
                                    </span>
                                </td>
                                <td>
                                    <small>
                                        <?php echo date('Y-m-d', strtotime($user['created_at'])); ?><br>
                                        <?php echo date('H:i', strtotime($user['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-success">نشط</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" 
                                                onclick="editUser(<?php echo htmlspecialchars(json_encode($user)); ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        
                                        <button class="btn btn-outline-warning" 
                                                onclick="changePassword(<?php echo $user['id']; ?>, '<?php echo $user['username']; ?>')">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        
                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo $user['username']; ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- نافذة إضافة/تعديل مستخدم -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalTitle">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="userForm" method="POST">
                <input type="hidden" name="action" id="userAction" value="add">
                <input type="hidden" name="id" id="userId">
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="userUsername" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="userUsername" name="username" required>
                    </div>
                    
                    <div class="mb-3" id="passwordField">
                        <label for="userPassword" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="userPassword" name="password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="userFullName" class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" id="userFullName" name="full_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="userRole" class="form-label">الدور</label>
                        <select class="form-select" id="userRole" name="role" required>
                            <option value="">اختر الدور</option>
                            <option value="admin">مدير</option>
                            <option value="cashier">كاشير</option>
                            <option value="kitchen">مطبخ</option>
                        </select>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تغيير كلمة المرور -->
<div class="modal fade" id="passwordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="passwordForm" method="POST">
                <input type="hidden" name="action" value="change_password">
                <input type="hidden" name="id" id="passwordUserId">
                
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        تغيير كلمة المرور للمستخدم: <strong id="passwordUsername"></strong>
                    </div>
                    
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="newPassword" name="new_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">تغيير كلمة المرور</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج حذف المستخدم -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="id" id="deleteUserId">
</form>

<script>
function editUser(user) {
    document.getElementById('userModalTitle').textContent = 'تعديل المستخدم';
    document.getElementById('userAction').value = 'edit';
    document.getElementById('userId').value = user.id;
    document.getElementById('userUsername').value = user.username;
    document.getElementById('userFullName').value = user.full_name;
    document.getElementById('userRole').value = user.role;
    
    // إخفاء حقل كلمة المرور في التعديل
    document.getElementById('passwordField').style.display = 'none';
    document.getElementById('userPassword').required = false;
    
    new bootstrap.Modal(document.getElementById('userModal')).show();
}

function changePassword(id, username) {
    document.getElementById('passwordUserId').value = id;
    document.getElementById('passwordUsername').textContent = username;
    document.getElementById('passwordForm').reset();
    
    new bootstrap.Modal(document.getElementById('passwordModal')).show();
}

function deleteUser(id, username) {
    if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟`)) {
        document.getElementById('deleteUserId').value = id;
        document.getElementById('deleteForm').submit();
    }
}

// التحقق من تطابق كلمات المرور
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('كلمات المرور غير متطابقة');
    }
});

// إعادة تعيين النموذج عند إغلاق النافذة
document.getElementById('userModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('userModalTitle').textContent = 'إضافة مستخدم جديد';
    document.getElementById('userAction').value = 'add';
    document.getElementById('userForm').reset();
    document.getElementById('passwordField').style.display = 'block';
    document.getElementById('userPassword').required = true;
});
</script>

<?php include 'includes/footer.php'; ?>
